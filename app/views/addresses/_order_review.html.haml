#totals_block
  .heading-title
    = image_tag 'address/card', alt: 'card', class: 'checkout-logo'
    Order Summary
  %table.cart_info_table
    - totals, grandtotal, shipping = get_cart_total_information(@country_code, @rate)
    - totals.each do |total_value|
      - if total_value[:amount]
        %tr.row.h5
          %td.item-text.small-7.columns
            - if total_value[:bold]
              .sub_total= "#{total_value[:title]}:"
            - else
              = "#{total_value[:title]}:"
          %td.item-total.small-5.columns{id:total_value[:id]}
            - if ['Coupon Discounts', 'Cart Discounts'].include?(total_value[:title])
              %span.discounts-text.discount-percent="(#{(total_value[:amount]*100.0/totals.first[:amount]).round(0)}% OFF)"
            - if total_value[:bold]
              .total_amount= "#{get_price_with_symbol(total_value[:amount], @hex_symbol)}"
            - else
              = "#{get_price_with_symbol(total_value[:amount], @hex_symbol)}"
      - if total_value[:title] == 'Shipping' && DOMESTIC_SHIPPING_CHARGES.blank? && ESSENTIAL_DESIGNERS["total_below_x"].to_f <= 0
        %tr.row.text-right
          %td.small-7.columns.small_msg.text-right (Shipping to India is Free.)
    %tr.row.h5.text-right
      - currency_is_allowed = Order::PAYPAL_ALLOWED_CURRENCIES.include?(@symbol)
      - if currency_is_allowed==false && @country_code != 'IN' && (paypal_rate = CurrencyConvert.currency_convert_memcached.find{|c| c.country_code == @country_code}.paypal_rate).present?
        %td.small-7.columns.small_msg.text-right
          (Please note that you will be charged in US Dollars, Approx. USD #{(paypal_rate * grandtotal).floor})
  - if @country_code == 'IN' && shipping > 0 && ESSENTIAL_DESIGNERS["total_below_x"].to_f <= 0
    %tr.row.h5.text-right
      .col-sm-12.add_more_items
        Shop for 
        %span.charges 
          = get_price_with_symbol(add_more_items_value_for(grandtotal, shipping), @hex_symbol)
        more  to  get 
        %span 
          %b FREE DELIVERY
:javascript
  wallet_error = function(element) {
    element.removeProp('checked');
    $('#wallet_error').show();
    $('#wallet_error').text('*Something went wrong. Try again later');
  };

  $('#referral').click(function() {
    var cart_id, element;
    cart_id = '#{@cart.id}';
    element = $(this);
    if (this.innerText == "Apply") {
      return $.ajax({
        data: {
          cart_id: cart_id
        },
        type: 'PUT',
        url: '/api/v1/user/wallets/apply_wallet_referral',
        success: function(response) {
          if ($.isNumeric(response.id)) {
            window.location.reload();
          } else {
            wallet_error(element);
          }
        },
        error: function(error) {
          wallet_error(element);
        }
      });
    } else {
      return $.ajax({
        data: {
          cart_id: cart_id
        },
        type: 'PUT',
        url: '/api/v1/user/wallets/remove_wallet_referral',
        success: function(response) {
          if ($.isNumeric(response.id)) {
            window.location.reload();
          }
        },
        error: function(error) {
          wallet_error(element);
        }
      });
    }
  });
