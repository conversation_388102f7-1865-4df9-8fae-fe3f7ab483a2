%link{rel: "dns-prefetch", href: "#{SITE_PROTOCOL}#{ENV['MOBILE_SITE_URL']}"}
%link{rel: "dns-prefetch", href: "https://assets0.mirraw.com"}
%link{rel: "dns-prefetch", href: "https://assetsm0.mirraw.com"}
-#%link{rel: "dns-prefetch", href: "http://assets1.mirraw.com"}
-#%link{rel: "dns-prefetch", href: "http://assets2.mirraw.com"}
-#%link{rel: "dns-prefetch", href: "http://assets3.mirraw.com"}
-#%link{rel: "dns-prefetch", href: "http://assets4.mirraw.com"}
-#%link{rel: "dns-prefetch", href: "http://assets5.mirraw.com"}
%link{rel: "dns-prefetch", href: "https://api.branch.io"}
%link{rel: "dns-prefetch", href: "http://pixel-geo.prfct.co"}
%link{rel: "dns-prefetch", href: "https://www.google-analytics.com"}
-#criteo
%link{rel: "dns-prefetch", href: "https://static.criteo.net"}
-#google ads
%link{rel: "dns-prefetch", href: "https://www.google.com"}
%link{rel: "dns-prefetch", href: "https://stats.g.doubleclick.net"}
%link{rel: "dns-prefetch", href: "https://www.googleadservices.com"}
-#facebook
%link{rel: "dns-prefetch", href: "https://www.facebook.com"}
%link{rel: "dns-prefetch", href: "https://connect.facebook.net"}

%link{:as => "font", :crossorigin => "", :href => asset_path('Lato-Regular.woff2'), :media => "all", :rel => "preload", :type => "font/woff2"}
%link{:as => "font", :crossorigin => "", :href => asset_path('foundation-icons.woff'), :media => "all", :rel => "preload", :type => "font/woff"}

%title= content_for?(:title) ? content_for(:title) : get_constant('DEFAULT_TITLE')
- keywords = content_for?(:keywords) ? content_for(:keywords) : get_constant('DEFAULT_KEYWORDS')
- description =  content_for?(:description) ? content_for(:description) : get_constant('DEFAULT_DESCRIPTION')

- keywords = "" if keywords == "blank"
- description = "" if description == "blank"

%meta{name: 'keywords', content: keywords }
%meta{name: 'description', content: description }

/ Chrome, Firefox OS and Opera
%meta{content: "#fff", name: "theme-color"}
/ Windows Phone
%meta{content: "#fff", name: "msapplication-navbutton-color"}
/ iOS Safari
%meta{content: "#fff", name: "apple-mobile-web-app-status-bar-style"}

- unless params[:action] == "catalog_page"
  %meta{name: "turbolinks-cache-control", content: "no-cache"}
%meta{name: "turbolinks-cache-control", content: "no-preview"}
- if design_details_page? || stitching_information_page?
  %meta{content: 'width=device-width, initial-scale=1.0', name: 'viewport'}
- else
  %meta{content: 'width=device-width, initial-scale=1.0, maximum-scale=6.0, user-scalable=1', name: 'viewport'}
- #https://support.3dcart.com/knowledgebase/article/View/89/16/how-do-i-verify-my-site-with-google-webmaster-tools
%meta{name: 'google-site-verification', content: '7gZv-hi-3CODOEW4UyhCiFeUFKYTuxsZ7DEGG79O23E'}
- #http://blogger-hints-and-tips.blogspot.in/2013/03/pinterest-analytics-and-website-verification.html
%meta{content: 'ae162c57511278e951259fb168ee2257', name: 'p:domain_verify'}
%meta{content: '2a662054611db26020810ba3279e4d02', name: 'p:domain_verify'}
- #http://blogtimenow.com/seo/meta-tag-verification-google-bing-alexa/
%meta{content: '********************************', name: 'msvalidate.01'}
- host_name = request.env['HTTP_HOST'].try(:downcase) || 'localhost'

- og_title = content_for?(:title) ? content_for(:title) : get_constant('DEFAULT_OG_TITLE')
- og_image = content_for?(:og_image) ? content_for(:og_image) : asset_url('logo-red-og-image.png')
- #og_url =  content_for?(:og_url) ? content_for(:og_url) : "//#{host_name}"
- og_type = content_for?(:og_type) ? content_for(:og_type) : 'website'

// http://ogp.me/
%meta{property: 'og:title', content: og_title}
%meta{property: 'og:type', content: og_type}
%meta{property: 'og:url', content: "#{SITE_PROTOCOL}#{ENV['MOBILE_SITE_URL']}#{request.fullpath}"}
%meta{property: 'og:image', content: og_image }
%meta{property: 'og:site_name', content: 'Mirraw.com'}
%meta{property: 'og:description', content: description}
%meta{property: 'og:locale', content: 'en_US'}
%meta{property: 'fb:admins', content: '506263136'}
%meta{property: 'fb:app_id', content: Rails.application.config.fb_app_id }

- #http://www.metatags.info/meta_name_robots
- if (request.host != "#{ENV['MOBILE_SITE_URL']}") #|| (cookies[:theme] == 'red_theme')
  %meta{content: 'NOINDEX,NOFOLLOW', name: 'robots'}
- elsif (params[:action] == "landing_page" && params[:landing] == "Rakhi-2016") || check_for_collection_noindex || (params[:controller] == 'pages' && params[:action] == 'price_match_guarantee_tnc') || (params[:controller] == "store" && params[:action] == "catalog_page" && params[:tag].present?) || (params[:kind] == 'direct_dollar') || check_for_catalogue_noindex
  %meta{content: 'NOINDEX,NOFOLLOW', name: 'robots'}
- elsif @facet_properties.to_a.length > 2 || (params[:controller] == "store" && params[:action] == "catalog_page" && params[:id].present? && params[:page].present? && params[:page] > "1") || (params[:controller] == "reviews" && params[:action] == "site_review" && params[:page].present? && params[:page] > "1")
  %meta{content: 'NOINDEX,FOLLOW', name: 'robots'}
- else
  %meta{:content => "index, follow, max-image-preview:large, max-snippet:-1", :name => "robots"}

- if browser.safari?
  %meta{content: 'telephone=no', name: 'format-detection'}

/Twitter Card data
%meta{:name => "twitter:card", :content => "summary_large_image"}
%meta{:name => "twitter:site", :content => "@MirrawDesigns"}
%meta{:name => "twitter:creator", :content => "@blacklife"}
/Twitter app Card data
%meta{:name => "twitter:app:country", :content => "IN"}
%meta{:name => "twitter:app:name:iphone", :content => "Mirraw"}
%meta{:name => "twitter:app:id:iphone", :content => "1112569519"}
%meta{:name => "twitter:app:url:iphone", :content => "#{SITE_PROTOCOL}#{ENV['MOBILE_SITE_URL']}#{request.fullpath}"}
%meta{:name => "twitter:app:name:googleplay", :content => "Mirraw.com"}
%meta{:name => "twitter:app:id:googleplay", :content => "com.mirraw.android"}
%meta{:name => "twitter:app:url:googleplay", :content => "#{SITE_PROTOCOL}#{ENV['MOBILE_SITE_URL']}#{request.fullpath}"}

%meta{content: 'text/html; charset=utf-8', 'http-equiv' => 'Content-Type'}

%link{rel: 'apple-touch-icon', type: 'image/png', href: '/apple-touch-icon-144x144.png', sizes: '144x144'}
%link{rel: 'apple-touch-icon', type: 'image/png', href: '/apple-touch-icon-114x114.png', sizes: '114x114'}
%link{rel: 'apple-touch-icon', type: 'image/png', href: '/apple-touch-icon-72x72.png', sizes: '72x72'}
%link{rel: 'apple-touch-icon', type: 'image/png', href: '/apple-touch-icon.png'}
%link{:rel => 'icon', :type => 'image/x-icon', :href => '/red-favicon.ico' }

- #%script{:async => "", "data-ad-client" => "ca-pub-7598907697606399", :src => "https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"}
