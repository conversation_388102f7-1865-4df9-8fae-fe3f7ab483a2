= form_tag search_path, method: :get, id: 'search_form', class: 'search-bar-form' do
  .row.collapse.postfix-round.search-css
    .small-10.columns{style: "padding-left: 5px;"}
      = text_field_tag(:q, params[:q], placeholder: '<PERSON> <PERSON><PERSON>, <PERSON><PERSON> or Product id', unbxdattr: 'sq', style: "padding-left: 5px;", required: true, minlength: '3', maxlength: '50', 'aria-label' => 'text', class: 'search-trending', id: 'search_input')
    .small-2.columns
      = button_tag(type: 'submit', id: 'search_submit_btn', class: "button postfix submit_btn", name: nil, 'aria-label' => 'submit') do
        %i.fi-magnifying-glass

:javascript
  var inputElem = document.getElementById("search_input");

  inputElem.addEventListener('input', () => {
    if (inputElem.value.length !== 0) {
    document.getElementById("search_submit_btn").setAttribute("unbxdattr", "sq_bt");
    } else {
      document.getElementById("search_submit_btn").removeAttribute("unbxdattr")
    }
  });

/ Trending Results commented for temperory purpose
/ .trending-results-box{data: {visited: (params[:q].present? ? 'true' : 'false')}}
/   .trending-results-text
/     %span.trending-icon &#x26A1;
/     %span Trending Results
