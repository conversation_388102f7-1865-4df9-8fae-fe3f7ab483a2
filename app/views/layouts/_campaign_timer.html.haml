-# remove this condition @offer_message from outside 
- if @offer_message.present?
  .global-timer.alert-box.radius{data: {alert: ''}, class: 'info'}
    .message-block
      =raw @offer_message
      - if @timer_promotion.present? && @timer_promotion.end_date.between?(Time.zone.now, 120.hours.from_now)
        #offer_message_countdown
          %span{style: 'display:inline'} Deal ends in:
          %span#offer_message_clock
            %span.deal_timer 00:00:00:00
          %input{id:'offer_message_timer', type: 'hidden', value: @timer_promotion.end_date.getlocal.utc.strftime("%Y/%m/%d %H:%M:%S")}
      %a{href: '#', class: 'close deal-close'} &times;
