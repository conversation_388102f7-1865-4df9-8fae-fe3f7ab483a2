-if controller.controller_name == 'designs'
  - pdpfooter = 'pdp_footer'
-else
  - pdpfooter = 'normal_footer'
%footer.footer-component-wrapper#desktop_footer.d_web{'class' => pdpfooter}
  .footer-section-one.footer-section-two
    .row
      .footer_row  
        .footer-left 
          .footer{'class' => pdpfooter}
            .footer-section
              %h3 COMPANY
              %ul
                %li
                  = link_to 'About Us', '/pages/about', :rel => 'nofollow'
                %li
                  = link_to 'Contact Us', '/pages/help_center', :rel => 'nofollow'
                -if is_domestic?
                  %li
                    = link_to "Careers", "http://careers.mirraw.com/about-us",:rel => 'nofollow',:target => '_blank'
                %li
                  = link_to 'Blog', 'https://blog.mirraw.com',:target => '_blank'
                %li
                  = link_to 'Terms', '/pages/terms', :rel => 'nofollow'
                %li
                  = link_to 'Privacy', '/pages/privacy', :rel => 'nofollow'
                %li
                  = link_to 'Mirraw Offers', "#{SITE_PROTOCOL}#{MOBILE_SITE_URL}/offers?icn=mobile_offers&ici=mobile_menu_sidebar",:target => '_blank'
            .footer-section
              %h3 HELP
              %ul
                %li
                  = link_to 'Survey', '/pages/survey', :target => "_blank", :rel => 'nofollow'
                %li
                  = link_to "Track Your Order", pages_faq_path(track_order: true), :target => "_blank", :rel => 'nofollow'
                %li
                  = link_to 'Payments', pages_faq_path(category: ORDERING_OF_FAQ['Payments'][0]), :target => "_blank", :rel => 'nofollow'
                %li
                  = link_to 'Shipping', pages_faq_path(category: ORDERING_OF_FAQ['Shipping'][0]), :target => "_blank", :rel => 'nofollow'
                %li
                  = link_to 'Returns', pages_faq_path(category: ORDERING_OF_FAQ['Cancellation & Returns'][0]), :target => "_blank", :rel => 'nofollow'
                %li
                  = link_to 'FAQ', pages_faq_path, :target => "_blank", :rel => 'nofollow'
            .footer-section
              %h3 BUSINESS
              %ul
                %li
                  = link_to 'International Buyers',pages_faq_path(question: 73), :target => '_blank', :rel => 'nofollow'
                -if is_domestic?
                  %li
                    = link_to 'Sell On Mirraw', 'https://seller.mirraw.com/pages/sell', :target => '_blank', :rel => 'nofollow'
                %li
                  = link_to 'Designer Login', 'https://seller.mirraw.com/sellers/sign_in', :target => '_blank', :rel => 'nofollow'
                %li
                  = link_to 'Bulk Order Inquiry', '/pages/bulk_order_inquiry', :rel => 'nofollow'           
                %li
                  = link_to 'Mirraw Partner Program', mirraw_partner_program_path, :rel => 'nofollow' , :target => '_blank', :rel => 'nofollow'          
                %li
                  = link_to 'Mirraw Coupons', coupons_path, :target => '_blank', :rel => 'nofollow'          
            .footer-section.contact-section
              %h3 CONTACT US
              %ul
                %li
                  = image_tag('footer/call-icon.svg', alt: 'call icon', height: '16', width: '16', class: 'lazy img-responsive')
                  = link_to "tel:#{Country.get_helpline_number(@country_code)}" do
                    = "#{Country.get_helpline_number(@country_code)} (Available 24*7)"
                %li
                  = image_tag('footer/email-icon.svg', alt: 'email icon', height: '16', width: '16', class: 'lazy img-responsive')
                  %a{ href: "mailto:<EMAIL>" } <EMAIL>
                %li
                  %a{:href => "https://api.whatsapp.com/send?phone=918591937092&text=Hello", target: "_blank", 'aria-label' => "Whatsapp"}
                    = image_tag('footer/whatsapp-icon.svg', alt: 'whatsapp icon', height: '16', width: '16', class: 'lazy img-responsive')
                    +91 8591937092
          #footer-banner
            %hr
            #share-icon
              %div.icon-wrapper
                .circular-icon
                  = image_tag('footer/hassle-free.svg', alt: 'hassle-free', class: 'services-logo lazy img-responsive')
                %span.text-white Hassle-Free Delivery
              %div.icon-wrapper
                .circular-icon
                  = image_tag('footer/ship-worldwide.svg', alt: 'ship-worldwide', class: 'services-logo lazy img-responsive')
                %span.text-white Ships Worldwide
              %div.icon-wrapper
                .circular-icon
                  = image_tag('footer/money-back.svg', alt: 'money-back', class: 'services-logo lazy img-responsive')
                %span.text-white 100% Money Back Guarantee
        .footer-right          
          .footer-section
            .footer-social-icons 
              %h3 FOLLOW US ON SOCIAL MEDIA 
              .flex-item-2.social-icon-wrap
                %a{:href => "https://api.whatsapp.com/send?phone=918591937092&text=Hello", target: "_blank", 'aria-label' => "Whatsapp"}
                  = image_tag('footer/whatsapp-white.svg', alt: 'whatsapp icon', height: '24', width: '24', class: 'lazy img-responsive')
                %a{:href => "https://x.com/MirrawDesigns", target: "_blank", 'aria-label' => "Follow Mirraw on Twitter"}
                  = image_tag('footer/x-logo.svg', alt: 'twitter icon', height: '24', width: '24', class: 'lazy img-responsive')
                %a{:href => "https://www.youtube.com/channel/UCFz6WjSUuICOJqIYl3DWA-g", target: "_blank", 'aria-label' => "Subscribe to Mirraw on YouTube"}
                  = image_tag('footer/youtube.svg', alt: 'youtube icon', height: '24', width: '24', class: 'lazy img-responsive')
                %a{:href => "https://www.instagram.com/mirraw/?hl=en", target: "_blank", 'aria-label' => "Follow Mirraw on Instagram"}
                  = image_tag('footer/instagram.svg', alt: 'instagram icon', height: '24', width: '24', class: 'lazy img-responsive')
                %a{:href => "https://www.facebook.com/MirrawDesigns/", target: "_blank", 'aria-label' => "Follow Mirraw on Facebook"}
                  = image_tag('footer/facebook.svg', alt: 'facebook icon', height: '24', width: '24', class: 'lazy img-responsive')
            .footer-media-print 
              %h3 IN THE SPOTLIGHT
              .third-item-image
                %li.yourstory.footer_press_logo
                  = link_to 'https://economictimes.indiatimes.com/wealth/earn/ethnic-products-e-commerce-startup-mirraw-[…]l-of-fiscal-prudence/articleshow/51266864.cms?from=mdr', :target => '_blank', :rel => 'nofollow' , aria: { label: 'Read about us on Economic Times' }do 
                    = image_tag('etImage.png', class: 'lazy img-responsive')
                %li.dnaindia.footer_press_logo
                  = link_to 'https://www.indiaretailing.com/2024/06/19/d2c-brand-mirraw-eyes-rs-125-crore-this-fiscal-eyes-us-uk-and-australia-expansion/', :target => '_blank', :rel => 'nofollow', aria: { label: 'Read about us on India Retailing' } do 
                    = image_tag('IrImage.png', class: 'lazy img-responsive')
                %li.yourstory.footer_press_logo
                  = link_to 'https://www.fibre2fashion.com/interviews/industry-speak/mirraw/shailesh-jain/13496-1', :target => '_blank', :rel => 'nofollow', aria: { label: 'Read about us on Fibre2Fashion' } do 
                    = image_tag('f2f.png', class: 'lazy img-responsive')
            .footer-app-icon
              %h3 EXPERIENCE MIRRAW APP ON MOBILE 
              .footer-app
                .div.footer-app-android
                  = link_to 'https://play.google.com/store/apps/details?id=com.mirraw.android', :target => '_blank', :rel => 'nofollow' , aria: { label: 'Download our app from Google Play Store' } do 
                    = image_tag('footer/get-it-on-google-play-g.svg', class: 'lazy img-responsive')
                .div.footer-app-iphone
                  = link_to 'https://itunes.apple.com/in/app/mirraw-online-fashion-shopping/id1112569519?pt=118123691&ct=desktop_footer_link&mt=8', :target => '_blank', :rel => 'nofollow' , aria: { label: 'Download our app from App Store' } do 
                    = image_tag('footer/download-on-app-store.svg', class: 'lazy img-responsive')
    .main-flex-two-container
      .flex-item-1
        %span Copyright © #{DateTime.now.year}, Mirraw Online Services Pvt. Ltd. All Rights Reserved.
-if controller.controller_name == 'designs'
  - mpdpfooter = 'mpdp_footer'
-else
  - pdpfooter = 'normal_footer'
#mobile_footer.m_web{'class' => mpdpfooter}
  = render partial: 'footer/social_icons_footer', cached: true 

  - if !checkout_flow_page? && !checkout_cart_login? && !login_page?
    .row
      - if !account_signed_in?
        .col-xs-4.sign_in_button{:style => "margin-left: 34%; padding: 3px"}
          = link_to "Sign In", new_account_session_url(protocol: Rails.application.config.partial_protocol), data: {turbolinks: 'false'}, class: "ga-sign-in-tracking"
    %br
  .row
    = render partial: 'footer/guarentees_footer', cached: true
  .row
    = render partial: 'footer/nav_links_footer', cached: true
  -if !(uc_browser? || opera_mini_browser?) && cookies[:subscribe].nil? && @subscription_banner_hash.present?
    -button_color = @subscription_banner_hash['color'] || '#FF8030'
    -bg_color = @subscription_banner_hash['bg_color'] || '#FFDEC9'
    #mobile-subscribe-window.reveal-modal{data: {reveal:""}, "close_on_background_click"=>"true" , style: 'padding: 0px; height: 100%;'}
      #modal-subscribe-box
        .modal-body
          .newsletter-image{style: 'max-height:100%;max-width:100%;'}
            - if @subscription_banner_hash['image_url'].is_a?(String)
              = image_tag(BASE_64_PLACHOLDER_IMAGE, alt: 'Newsletter Subscription', id:'newsletter-sub-image', class: 'js-lazy',  data: {original: @subscription_banner_hash['image_url']})
            - else
              = webp_picture_tag(@subscription_banner_hash['image_url'], p_style: :mobile_main, id:'newsletter-sub-image', alt: 'Newsletter Subscription', lazy: {})
        .modal-footer
          .row
            .columns.small-12.text-center
              -if opera_mini_browser?
                %input#subscribe-input{:type => "text", :placeholder => "Enter Email", :value => "", :style => "width:250px;color:#{button_color}", class:"subscription-input"}
                .phone-field-container
                  %input#subscribe-input-country-code{:type => "text", :placeholder => "+1", :value => "", :maxlength => "5", style: "color:#{button_color}", class:"subscription-input"}
                  %input#subscribe-input-phone{:type => "text", :placeholder => "Enter Mobile Number", :value => "", :maxlength => "14", style: "color:#{button_color}", class:"subscription-input"}
              -else
                %input#subscribe-input{:type => "text", :placeholder => "Enter Email", :value => "", style: "color:#{button_color}", class:"subscription-input"}
                .phone-field-container
                  %input#subscribe-input-country-code{:type => "text", :placeholder => "+1", :value => "", :maxlength => "5", style: "color:#{button_color}", class:"subscription-input"}
                  %input#subscribe-input-phone{:type => "text", :placeholder => "Enter Mobile Number", :value => "", :maxlength => "14", style: "color:#{button_color}", class:"subscription-input"}
          .row
            .columns.small-12.text-center
              %input#email-subscribe-button{:type => "submit", :value => @subscription_banner_hash['text'], style: "background:#{button_color};color:#{bg_color}"}
          .row
            .columns.small-12.text-center
              %label.sub-msg{style: "background:#{bg_color};color:#{button_color}"}
          .row
            .columns.small-12.text-center
              %input#email-cancel-button{:type => "submit", :value => "No Thanks!"}

  - if cookies[:sticky_coupon_banner].nil? && @sticky_coupon_banner.present?
    .sticky-coupon-banner
      .wrapper
        .sticky-coupon-image
          = link_to @sticky_coupon_banner.link do
            = webp_picture_tag(@sticky_coupon_banner.photo, p_style: :main_m, alt: 'sticky coupon banner', lazy: {})
        %a.close-sticky-coupon-banner.right.close-button
          .close-label x
          