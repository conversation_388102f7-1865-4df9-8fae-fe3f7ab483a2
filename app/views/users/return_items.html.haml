- content_for :page_specific_css do
  = stylesheet_link_tag 'returns'
- content_for :page_specific_js do
  = javascript_include_tag 'returns'
- if params[:api_layout] == 'true'
  :scss
    section.main-section{
      div#main-section{
        #container{
          margin-top: 0px;
        }
      }
    }

.user_return_panel.returns
  -shipments = @order.shipments
  -require_bank_info = ['Cash On Delivery','COD','CBD','Bank Deposit','Cash Before Delivery']
  -eligible_for_automated_refund = @order.eligible_for_automated_refund?
  -if @return.new_record?
    = form_for @return, html: { multipart: true } do |f|
      .row.panel_block
        .columns.panel_heading
          %h6.returns_heading 1. Select to Return
          #new_return.columns.panel_content
            - line_items = @order.line_items
            - if line_items.present? { |item| item.design.premium }
              .notice
                %p Note: Luxe products are not returnable. Please proceed with returning other eligible items.
              = f.hidden_field :order_id, value: @order.id
              -items = []
              -item_status, last_date = @order.returnable_line_items('android',ALLOWED_APP_VERSIONS.last,true)
              .designer_well
                %ul
                  - line_items.in_groups_of(2).each do |item_group|
                    %li.returns_list
                      %ul.small-block-grid-2
                        - item_group.each do |item|
                          -if item.present?
                            -designer_order = item.designer_order
                            -items << item.id
                            - design = item.design
                            - premium = design.premium
                            - qty = (item.return_quantity.to_i <= 0 ? 1 : item.quantity)
                            %li{class: ('premium-design' if premium == true)}
                              -if premium != true
                                =check_box_tag 'return_items[]',item.id, false,class:'hide'
                              %table{id:"check_#{item.id}", 'data-notice': "#{item_status[item.id]}", class: ("img-check" if premium != true)}
                                %tr
                                  %td
                                    =image_tag(item.display_image(:small),alt: design.title, width:'151', height:'173')
                                %tr
                                  %td Qty : #{qty}
                                %tr
                                  %td Price : #{@order.currency_code} #{get_price_in_currency(item.snapshot_price_currency(1), @order.currency_rate)}
                              %div{id:"done_step_#{item.id}"}
                                %label

              -line_items.each do |line_item|
                -design   = line_item.design
                -designer = design.designer
                -designer_order = line_item.designer_order
                .row.item_block.hide{id: "item_#{line_item.id}"}
                  .row
                    .large-2.medium-4.small-5.columns.text-center
                      = designer.name
                      %br
                      = link_to designer_design_path(designer, design) do
                        =image_tag(line_item.display_image(:small),alt: design.title)
                    .large-10.medium-8.small-7.columns
                      .row
                        .columns= link_to design.title, designer_design_path(designer, design)
                      .row
                        .columns
                          = "Price: #{@order.currency_code}"
                          %span{id: "item_price_#{line_item.id}"}= get_price_in_currency(line_item.snapshot_price_currency(1),@order.currency_rate)
                      - if line_item.variant.present?
                        .row
                          - option_type_value = line_item.variant.option_type_values.first
                          = "#{option_type_value.option_type.p_name} : #{option_type_value.p_name}"
                      .row
                        .small-8.columns
                          = select_tag "quantity_#{line_item.id}", options_for_select(1..line_item.quantity), class: 'quantity', data: {item_id: line_item.id}
                      .row
                        .columns
                          %span= "Total: #{@order.currency_code}"
                          %span{id: "total_#{line_item.id}"}= get_price_in_currency(line_item.snapshot_price_currency(1),@order.currency_rate)
                  %br
                  .row
                    .large-12.small-12.columns
                      .notice_div= " * Last Date of Return : #{last_date.strftime('%a, %d %b, %Y')}" if last_date.present?
                  .row
                    .large-12.small-12.columns
                      %b Select Reason
                    .large-12.small-12.columns
                      .small-12.columns{style:'margin-left:-0.4em;'}=select_tag "reason_#{line_item.id}", options_for_select(ORDER_RETURN_REASONS.keys), {include_blank: '--Select_Reason--', class: 'item_reason', data: {return_reason_hash: ORDER_RETURN_REASONS}}

                  .row.hide{id: "reason_details_panel_#{line_item.id}"}
                    .large-12.small-12.columns
                      %b Select Reason Details
                    .large-12.small-12.columns
                      .small-12.columns{style:'margin-left:-0.4em;'}=select_tag "reason_details_#{line_item.id}", options_for_select([]), {class: 'item_reason_details'}

                  .row.hide{id: "upload_panel_#{line_item.id}"}
                    .large-12.small-12.columns
                      %b Upload Product Image
                    .large-3.small-3.columns
                      =file_field_tag "uploadimage_#{line_item.id}_1", accept: "image/*;", type: "file", class: 'item_image', data: { max_file_size: 6.megabytes }, rel:'tooltip',title:'Please upload image of product having defect.'
                    .large-12.small-12.columns
                      %b Upload Outer Package Image
                    .large-3.small-3.columns
                      =file_field_tag "uploadimage_#{line_item.id}_2", accept: "image/*;", type: "file", class: 'item_image', data: { max_file_size: 6.megabytes }, rel:'tooltip',title:'Please upload outer package image.'
                    .large-12.small-12.columns.notice_div * Upload jpg/png/jpeg image less than 6MB.
              -if items.present?
                .large-6.medium-8.small-12.columns.small-centered.text-center.submit_reason.hide
                  =f.text_area :notes, required: true,placeholder: 'Reason Description', rows: 4, cols: 20,maxlength: "1000"
                  =f.select :type_of_refund, options_for_select(['Coupon','Wallet','Refund']),{:include_blank => '--select return type--'},{class:'form-control, refund_type_dropdown',required: true, data: {bank_require: require_bank_info.include?(@order.pay_type), otp_required: eligible_for_automated_refund, max_automated_cod_amount: AUTOMATED_COD_REFUND['max_refund_amount'].to_i}}
                  
                  -if require_bank_info.include?(@order.pay_type)
                    .bank_details_form.hide
                      %br
                      =render partial:'users/bank_details_form'

                  -# -if eligible_for_automated_refund
                    #otp_verification_form.hide
                      %br
                      =render  partial:'users/return_otp_verification_form', locals: {f: f}

                  .bank_details_not_needed
                    = f.submit 'Submit', class: 'button tiny radius', id: 'return_products',data: { disable_with: "Please wait..." }
  
  -if @return.type_of_refund == 'Refund' && !eligible_for_automated_refund && (require_bank_info.include? @return.pay_type) && @return.account_holder_name.blank? && @return.user_paypal_email.blank? && (['payment_complete','ticket_raised'].exclude? @return.state)
    %br
    =render partial:'users/bank_details_form'

  -unless @return.new_record?
    -if @return.type_of_refund != 'Refund' || (['payment_complete','ticket_raised'].include? @return.state) || (@return.type_of_refund == 'Refund' && (@return.reverse_pickup? || ((require_bank_info.exclude? @return.pay_type) || ((require_bank_info.include? @return.pay_type) && (@return.account_holder_name.present? || @return.user_paypal_email.present?)))))
      .row.panel_block
        -unless @return.new_record?
          .panel_heading.returns_heading
            -if @return.reverse_pickup?
              %h6 Pickup Details
            -elsif (@return.new? && @return.app_source.present?) || @return.images_approved?
              %h6 Fill Tracking Details
            -else
              %h6 Return Details
        .panel_content
          = render  partial:'users/returns_tracking_details',:locals => {returns: @return,order: @order}
        .row.panel_footer
          .small-4.columns.text-center{style: 'border-right: 1px solid gray;'}
            Placed On
            %p=@return.created_at.strftime('%d %b, %Y')
          .small-4.columns.text-center{style: 'border-right: 1px solid gray;'}
            =(@return.type_of_refund == 'Replacement' ? 'Total' : 'You will receive')
            %p
              =@order.currency_code
              =get_price_in_currency(@return.discount,@order.currency_rate)
          .small-4.columns.text-center
            Requested for
            %p=@return.type_of_refund
  %br
  .row
    .listing_panel_block
      %a{href: '#return_policy'}
        %p
          How To Return ?
          %span.collapse
            [+]
          %span.expand
            [-]
      #return_policy.content
        = render :partial => '/orders/return_policy', :locals => {:order => @order}

:javascript
  $(document).on('click', '.user_return_panel .listing_panel_block a', function(e) {
    var collapse;
    e.preventDefault();
    collapse = $(this).attr('href');
    $(this).find('span').toggle();
    $(collapse).slideToggle('fast');
  });


  $( document ).ready(function() {
    $('input:checkbox').removeAttr('checked');
  });