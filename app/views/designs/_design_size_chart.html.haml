#modal-size-chart
  .modal-dialog.modal-sm-size
    .modal-content
      .btn_close_top.button{onClick: 'closeSizeChart()'} &times;
      .modal-header{style: 'text-align: center; padding: 10px'}
        .header-flex
          %h4#exampleModalLabel.modal-title
            - unless size_chart['is_variant']
              = "#{@design['designable_type'].underscore.titleize}"
            Size Chart
          %div.unit-toggle-switch
            %label.dimension-switch.switch-wrapper
              .unit-label.unit-label-in.active in
              .switch
                %input.unitToggle{ type: "checkbox" }
                %span.slider
              .unit-label.unit-label-cm cms
      .modal-body{style: "text-align: -webkit-center; clear: both"}
        - unless size_chart['is_variant']
          .row.size-label Ready-Made Measurements
          .row.size-label All values in inches-#{@design['designable_type'].underscore.titleize}-(Ready garment may have 0.5 to 1 inch difference)
        .size-box-modal  
          .row.table-responsive.table-inches
            %table.table.table-size-bordered.std_size_chart
              %thead
                %tr
                  -if @design['designable_type'] == 'Lehenga'
                    -size_chart["heads"].each do |head|
                      %th.head_style= head
                  -else
                    -size_chart["heads"].each do |head|
                      %th.head_style= head
              %tbody{style:'text-align: center;'}
                - size_chart["Sizes"].each_with_index do |size, index|
                  - next if !size_chart['is_variant'] && availabel_sizes.exclude?(size['size'].to_s) && !@design['designable_type'].to_s.downcase.eql?("saree")

                  %tr
                    %td.head_style{ data: td_data(size['size'], convert: false) }= td_value(size['size'])

                    - size['columns'].each_with_index do |val, col_index|
                      - header = size_chart["heads"][col_index] rescue nil
                      - should_convert = !skip_conversion_header?(header)
                      %td.head_style{ data: td_data(val, convert: should_convert, header: header) }= td_value(val)
          - if size_chart['image'].present?
            .size_chart_modal_image
              %img{src: IMAGE_PROTOCOL + size_chart['image']}
          - elsif false#'SalwarKameez' == @design['designable_type']
            .size_chart_modal_image
              =image_tag("kameez.jpg")
          - elsif 'Kurti' == @design['designable_type']
            .size_chart_modal_image
              =image_tag("stitching/kurti.jpg")