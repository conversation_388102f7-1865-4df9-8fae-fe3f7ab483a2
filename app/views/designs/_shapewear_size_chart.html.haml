.size-chart-text
  %span.size-text= 'Sizing Guide'
  %button#size-chart-btn.btn-view-size{"data-reveal-id" => "shapewearChartModal",onClick: 'openSizeChart()'} Size Chart
  #shapewearChartModal.reveal-modal{data: {reveal:"", v_offset: "0"}, style: 'padding: 0px;overflow: scroll; height: 100%; top: 0; position: fixed;'}
    #shapewear-size-chart
      .modal-dialog.modal-sm-size
        .modal-content
          .btn_close_top.button{onClick: 'closeSizeChart()'} &times;
          .modal-header{style: 'text-align: center; padding: 10px'}
            .header-flex
              %h4#exampleModalLabel.modal-title
                Shapewear Size Chart 
              %div.unit-toggle-switch
                %label.dimension-switch.switch-wrapper
                  .unit-label.unit-label-in.active in
                  .switch
                    %input.unitToggle{ type: "checkbox" }
                    %span.slider
                  .unit-label.unit-label-cm cms
          .modal-body{style: "text-align: -webkit-center; clear: both"}
            .row.size-label Ready-Made Measurements
            .row.size-label Size in inches
            %table.table.table-size-bordered
              - header_title_array = ["Size", "Brand Size", "To fit waist range (inch)", "To fit hip range (inch)"]
              %thead
                %tr 
                - header_title_array.each do |title|
                  %th{style:'text-align: center;'}= title
              %tbody{style:'text-align: center;'}
              - shapeware_size_array = [["28", "S", "28-30", "34-36"], ["30", "M", "30-32", "36-38"], ["32", "L", "32-34", "38-40"], ["34", "XL" , "34-36" , "40-42"], ["36", "XXL", "36-38", "42-44"]] 
              - shapeware_size_array.each do |value_array|
                %tr
                  - value_array.each_with_index do |val, idx|
                    - if idx >= 2 && range?(val)
                      - td_data_hash = td_data(val)
                      %td{ data: { inch: td_data_hash[:inch], cm: td_data_hash[:cm] }, style:'text-align: center;' }= td_data_hash[:inch]
                    - else
                      %td{ style:'text-align: center;' }= val