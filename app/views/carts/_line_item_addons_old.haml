.line-item
  %ul.addons-notes{"data-accordion" => ""}
    - design = line_item.design
    - line_item.line_item_addons.each do |li_addon|
      -apply_class = li_addon.add_paid_option_types(line_item.design) > 0 ? 'active' : ''
      %li{class: apply_class}
        - snapshot_price = get_price_with_symbol(li_addon.snapshot_price_currency(@rate), @hex_symbol)
        - @total_addon += li_addon.snapshot_price.to_f
        -# %a{:href => "#lia_#{li_addon.id}"}
        -#   = "#{li_addon.addon_type_value.try(:name)} :"
        -#   %span.text-right.item-price-font= li_addon.free_stitching? ? get_price_with_symbol(0, @hex_symbol) : snapshot_price
        .content{id: "lia_#{li_addon.id}", class: apply_class}
          - addon_details = generate_addon_details(li_addon, design)
          - addon_details.each do |detail|
            .addon-breakdown
              - if detail[:addon_note].present?
                .item-addon-name= "+ " + detail[:addon_note]
                .item-price-font= detail[:addon_price]
