- if account_signed_in?
  - checkout_url = new_order_path
- elsif guest_logged_in?
  - if session[:guest_shipping_address].present?
    - checkout_url = new_order_url(protocol: Rails.application.config.partial_protocol)
  - else
    - checkout_url = collect_addresses_path
- else
  - if browser.device.mobile?
    - checkout_url = accounts_guest_login_path
  -else
    - checkout_url = new_order_path
= link_to checkout_url, class: ('add_place_order button small' unless sticky_checkout), id: 'checkout_button', data: {turbolinks: 'false'} do
  - if sticky_checkout
    .cart_checkout_button.button
      CHECKOUT
  - else
    CHECKOUT