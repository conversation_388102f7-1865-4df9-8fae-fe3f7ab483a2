.cart_mini_info.bordered_block
  %div
    -delivery_time, show_rts_message, available_in_warehouse = @cart.get_delivery_time(@shipping_address.try(:country), @shipping_address.try(:city))
    -if show_rts_message
      .notice_rts Your cart has Ready To Ship and Non-Ready To Ship items, so the delivery date has changed.
  - @total_line_item_count = 0
  - @total_addon = 0
  .product-details-container.heading
    .title Item
    .title Quantity
    .title Price
    .title Sub-total
  - for line_item in @cart.line_items
    - @total_line_item_count += line_item.quantity
    .item_block.product-details-container{id: "item_#{line_item.id}"}
      .item-image
        = link_to designer_design_path(line_item.design.designer, line_item.design) do
          - if line_item.design.master_image.present?
            = image_tag(IMAGE_PROTOCOL + line_item.design.master_image.photo(:large), alt: line_item.design.title)
          - else
            = image_tag('default_image.jpg', alt: line_item.design.title)
      .quantity= "#{line_item.quantity}"
      -# .item-title= line_item.design.title.titleize
      -# .item-designer= "Sold by #{line_item.design.designer.name}"
      .value-details.d-flex
        - price = get_price_with_symbol(line_item.snapshot_price_currency(@rate), @hex_symbol)
        %div= price
      -# - if line_item.variant.present?
      -#   .item-variant
      -#     - option_type_value = line_item.variant.option_type_values.first
      -#     = "#{option_type_value.option_type.p_name} : #{option_type_value.p_name}"
      .value-details.d-flex.quantity_total
        - total = get_price_with_symbol(line_item.total_currency(@rate), @hex_symbol)
        %div= total
      - if line_item.line_item_addons.present?
        = render partial: 'carts/line_item_addons_old', locals: {line_item: line_item}
      - if (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present? && line_item.buy_get_free == 1
        %div
          - bmgnx_msg = Promotion.bmgnx_offer_message(bmgnx_hash)
          =link_to bmgnx_msg, '/buy-m-get-n-free', title: "View More - #{bmgnx_msg} Products", class: 'b1g1'
  #totals_block
    .total-pricing-section
      #estd_days.text-right.items-price
        %span Delivery
        %span= "#{delivery_time} days"
      .no-of-items.text-right.items-price
        %span Total Items
        %span= "#{@total_line_item_count}"
      .item-total.text-right.items-price
        - item_total = @cart.items_total_without_addons(rate)
        %span Item Total
        %span= get_price_with_symbol(item_total, @hex_symbol)
      - if addons_charges > 0
        %div.text-right.items-price
          %span Customisation
          %span= get_price_with_symbol(addons_charges , @hex_symbol)
      - if shipping_charges >= 0 || @cart.ready_to_ship_designs?
        .shipping-charges-container
          #shipping_charges_order_page.shipping
            .h5.text-right#shipping_charge.items-price
              %span Shipping
              %span= get_price_with_symbol(shipping_charges, @hex_symbol)
      - if discounts > 0
        #discount_order_page.discounts.h5.text-right.items-price
          %span Discounts
          %span= get_price_with_symbol(discounts, @hex_symbol)
      - if prepaid_discount > 0
        .prepaid_discount.hide
          .discounts
            .h5.text-right.items-price
            %span Prepaid Discount
            %span= "#{@prepaid_payment_promotion.percent}%) : -#{get_price_with_symbol(prepaid_discount, @hex_symbol)}"
      - if mastercard_discount.to_f > 0
        .mastercard_discount.hide
          .discounts
            .h5.text-right.items-price
            %span Mastercard Discount
            %span= "#{Promotion.mastercard_discount_percent.to_i}%) : -#{get_price_with_symbol(mastercard_discount, @hex_symbol)}"
      - if cod_charges > 0
        .cod_charges.hide
          .discounts
            .h5.text-right.items-price
            %span COD Charges
            %span= "#{get_price_with_symbol(cod_charges, @hex_symbol)}"
      - if session[:gift_wrap] && GIFT_WRAP_PRICE.to_f >= 0
        .h5.text-right.items-price.gift_wrap
          %span Gift Wrap Charges
          %span= "#{get_price_with_symbol(get_price_in_currency(GIFT_WRAP_PRICE.to_f), @hex_symbol)}"
          #foo{"data-lat" => "#{addons_charges}"}
      - if tax_details.present? && tax_details[:tax_enable]
        .taxes.text-right.items-price
          %span Total Tax
          %span= "#{get_price_with_symbol(tax_details[:tax_amount], @hex_symbol)}"
      - if wallet_discount > 0
        #wallet_discount_order_page.h5.text-right.items-price.discounts
          %span Wallet discount
          %span= "#{get_price_with_symbol(wallet_discount, @hex_symbol)}"
    %div.total
      #grand_total_without_cod.text-right.grand_total.items-grand-total{:value => grandtotal}
        %span Grand Total
        %span= get_price_with_symbol(grandtotal, @hex_symbol)
        - price_in_currency = grandtotal
        - if @country_code == 'US' || @country_code == 'AU' || @country_code == 'GB'
          .paypal_message_pdp{:data => { "pp-message": "", "pp-amount":"#{sprintf('%.2f', price_in_currency)}" ,"pp-layout":"text", "pp-buyercountry":@country_code}}
      .text-right.grand_total_with_cod.items-price.hide.items-grand-total
        %span Grand Total
        %span= get_price_with_symbol(cod_total + cod_charges + referral_amount, @hex_symbol)
      / - if @total_addon != 0 && @cart.mirraw_payable_addons?
      /   .notice_class= "*Due to Festival Rush, estimated time to deliver stitched product is 25 days from date of order."
