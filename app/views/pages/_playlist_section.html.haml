.playlist_view
    .title-block 
        - if playlist.name.present?
            %h1.category-title #{playlist.name}
        - if category.present?
            = link_to "View All", store_search_path(category.name, sort: "new"), class: "cat-name view_all"
        - elsif playlist.search_query.present?
            %a{ href: "/search?#{playlist.search_query}", class: "cat-name view_all" } View All
        - elsif playlist.designer_id.present?
            %a{ href: "/search?designer_id=#{playlist.designer_id}", class: "cat-name view_all" } View All
    .swiper
        .swiper-wrapper
            - search_data['designs'].each do |design|
                - if design.present? && design['state'] == 'in_stock'
                    .swiper-slide
                        .product-container
                            %a{href: "#{design['design_path']}"}
                                .product-image
                                    - if design['sizes'] && design['sizes']['long'].present?
                                        = image_tag(IMAGE_PROTOCOL + design['sizes']['long'], alt: design['title'])
                                    -else
                                        = image_tag(asset_path('default_image.jpg'), alt: design['title'])
                                .overlay
                                    %h5.truncate #{design['title']}
                                    .price-tag.d-flex
                                        - if design['discount_percent'] > 0
                                            .discount_price=get_price_with_symbol(design['discount_price'], design['hex_symbol'])
                                            .actual_price.product_price_wo_discount=get_price_with_symbol(design['price'], design['hex_symbol'])
                                            .design-col2.details_block.percent_disc= "(#{design['discount_percent']}% OFF)"
                                        - else
                                            .discount_price=get_price_with_symbol(design['discount_price'], design['hex_symbol'])
        .swiper-button-prev
            &#x2039;
        .swiper-button-next
            &#x203A;