- case row[:type]
- when :header
  .matrix-row.header
    .matrix-cell Features
    - row[:plans].each do |plan|
      .matrix-cell{ class: ("highlight" if plan[:highlight]) }
        .plan-name= plan[:name]
        .plan-price= plan[:price]
        - if plan[:badge]
          %span.badge= plan[:badge]

- when :group
  .matrix-row.group-title
    .matrix-cell{ colspan: row[:colspan] || 7 }
      = image_tag(row[:icon], alt: "#{row[:title]} icon", height: 20, width: 20)
      = row[:title]

- when :feature
  .matrix-row
    .matrix-cell
      = image_tag(row[:icon], class: row[:icon_class], height: 16, width: 16)
      = row[:label]

    - row[:values].each do |val|
      .matrix-cell
        - if val == true
          = image_tag('subscription_plans/marron-tick.svg', class: 'tick-icon')
        - elsif val == false
          = image_tag('subscription_plans/light-cross.svg', class: 'cross-icon')
        - else
          %span= val
