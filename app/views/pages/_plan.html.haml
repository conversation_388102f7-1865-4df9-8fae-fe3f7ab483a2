.plan{ class: ("featured" if plan[:featured]) }
  - if plan[:badge].present?
    .popular-badge#popular= plan[:badge]

  .plan-tab-container
    - plan[:tabs].each_with_index do |tab, i|
      .plan-tab{ id: tab[:id], class: (i.zero? ? "active" : nil) }= tab[:name]

  - plan[:tabs].each_with_index do |tab, i|
    .plan-content{ id: "#{tab[:id]}-content", style: (i.zero? ? nil : "display:none;") }
      .plan-header
        = image_tag(tab[:icon], class: "#{tab[:id]}-icon")
        = tab[:name]

      .plan-price
        %h3= tab[:price]
        %span /month
      .plan-cycle= tab[:cycle]

      %ul.features
        - tab[:features].each do |feature|
          %li
            = image_tag('subscription_plans/marron-tick.svg', alt: 'Tick Icon', class: 'tick-icon')
            = feature

      - if tab[:exclusive].present?
        .exclusive
          .title
            = image_tag('subscription_plans/gold-sparkle.svg', alt: 'sparkle Icon', class: 'sparkle-icon', height: 16, width: 16)
            Exclusive Perks
          %ul
            - tab[:exclusive].each do |perk|
              %li
                = image_tag('subscription_plans/gold-ruby.svg', alt: 'ruby Icon', class: 'ruby-icon')
                %div= perk

  .plan-footer
    %button
      = image_tag('subscription_plans/white-lightning.svg', class: 'lightning-icon', height: 16, width: 16)
      Pay Now
