- content_for :page_specific_css do
    = stylesheet_link_tag 'playlist'
= javascript_include_tag 'pages/show_playlist'

#playlists
    %h4.title #{@page.name}
    .categories
        - PAGE_CATEGORIES.each do |category|
            = link_to store_search_path(category, sort: "new"), class: "category-pill" do
                = category.upcase
    - @playlist_ids.each do |playlist_id|
        .playlist-card{ id: "playlist-#{playlist_id}", data: { id: playlist_id} }
            .spinner 
                = image_tag('preloader.gif', alt: 'loading', width: 24, height: 24, class: 'mirraw-loader')