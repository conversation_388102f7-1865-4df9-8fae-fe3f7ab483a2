= javascript_include_tag 'selectize'

- content_for :page_specific_css do
  = stylesheet_link_tag 'orders'
=render partial: '/layouts/initialise_grading'
- if @order
  - currency_details = currency_params(@order)

  - if @track && @order.app_source.downcase != 'desktop'
    - fb_design_id = []
    %script{type: 'text/javascript'}
      window.addEventListener('load', function(){
      - @order.line_items.each do |item|
        - fb_design_id.push(item.design_id)
        unbxdTrack("order",{"pid":"#{item.design_id}","qty":"#{item.quantity}","price":"#{item.snapshot_price_currency(1)}"});
        Unbxd.track("order", {"pid": "#{item.design_id}", "qty": "#{item.quantity}", "price": "#{item.snapshot_price_currency(1)}"}})
      });
    -# :javascript
      addEvent(window, 'load', function(){
        if (typeof fbq !== 'undefined'){
          fbq('track', 'Purchase', {value: '#{@total}', currency: 'INR', content_ids: #{fb_design_id}, content_type : 'product', phone: '#{@phone_number}', city: '#{@city}', country: '#{@country}'.toLowerCase(), email: '#{@email}'});
        }
      });
      if(typeof ga != 'undefined'){
          gaOrder(#{@ga_hash.to_json});
      }

    = render partial: 'orders/adwords_conversion', locals: {adwords_total: @adwords_total}

  - if is_android?
    .row#get_app_link
      .panel.info.radius
        %h1
          GET THE APP NOW!
          %span.close#close_link &times;
        %p 
          Get realtime updates on your order, Live chat with customer support & avail app-only offers
        %a.success{:href => "https://play.google.com/store/apps/details?id=com.mirraw.android&utm_source=download_app_order_Ack&utm_medium=mirraw_mobile_web", :target => "_blank", rel: "noopener"} 
          Download Now &nbsp;
          %i.fi-social-android
  - elsif browser.platform.ios?
    .row#get_app_link
      .panel.info.radius
        %h1
          GET THE APP NOW!
          %span.close#close_link &times;
        %p 
          Get realtime updates on your order, Live chat with customer support & avail app-only offers
        %a.success{:href => "https://itunes.apple.com/app/apple-store/id1112569519?pt=118123691&ct=mobile_orderAck_Button&mt=8", :target => "_blank", rel: "noopener"} 
          Download Now &nbsp;
          %i.fi-social-apple

  .row#order_show_block
    =render partial: 'layouts/flash_msg'
    .large-12.medium-12.small-12.columns.small-centered
      %h4.text-center.order-acknowledgement-font-size
        %svg#Layer_1{class: "custom-svg", xmlns: "http://www.w3.org/2000/svg", "xmlns:xlink" => "http://www.w3.org/1999/xlink", version: "1.1", viewBox: "0 0 117.72 117.72", x: "0px", y: "0px"}
          %g.st0
            %path.st0{d: "M58.86,0c9.13,0,17.77,2.08,25.49,5.79c-3.16,2.5-6.09,4.9-8.82,7.21c-5.2-1.89-10.81-2.92-16.66-2.92 c-13.47,0-25.67,5.46-34.49,14.29c-8.83,8.83-14.29,21.02-14.29,34.49c0,13.47,5.46,25.66,14.29,34.49 c8.83,8.83,21.02,14.29,34.49,14.29s25.67-5.46,34.49-14.29c8.83-8.83,14.29-21.02,14.29-34.49c0-3.2-0.31-6.34-0.9-9.37 c2.53-3.3,5.12-6.59,7.77-9.85c2.08,6.02,3.21,12.49,3.21,19.22c0,16.25-6.59,30.97-17.24,41.62 c-10.65,10.65-25.37,17.24-41.62,17.24c-16.25,0-30.97-6.59-41.62-17.24C6.59,89.83,0,75.11,0,58.86 c0-16.25,6.59-30.97,17.24-41.62S42.61,0,58.86,0L58.86,0z M31.44,49.19L45.8,49l1.07,0.28c2.9,1.67,5.63,3.58,8.18,5.74 c1.84,1.56,3.6,3.26,5.27,5.1c5.15-8.29,10.64-15.9,16.44-22.9c6.35-7.67,13.09-14.63,20.17-20.98l1.4-0.54H114l-3.16,3.51 C101.13,30,92.32,41.15,84.36,52.65C76.4,64.16,69.28,76.04,62.95,88.27l-1.97,3.8l-1.81-3.87c-3.34-7.17-7.34-13.75-12.11-19.63 c-4.77-5.88-10.32-11.1-16.79-15.54L31.44,49.19L31.44,49.19z"}
        %div.order-ack-title Order Acknowledgement  

      - if @order.satisfies_cashback_conditions? && @order.cashback_available? && !@order.cashback_rewarded?
        %p.text-center.row.cashback-reminder
          - if @order.international?
            %strong= t('cashback.reminder.international.html', price: "#{@order.currency_code} #{get_price_in_currency(@order.other_details['loyalty_rewards_credit'].to_f, @order.currency_rate)}")
          - else
            %strong= t('cashback.reminder.domestic.html', price: "#{@order.currency_code} #{get_price_in_currency(@order.other_details['loyalty_rewards_credit'].to_f, @order.currency_rate)}")
      
      #order-info
        .order-info-details
          %svg.svg-icon{style: "width: 1em; height: 1em;vertical-align: middle;fill: currentColor;overflow: hidden;", version: "1.1", viewbox: "0 0 1024 1024", xmlns: "http://www.w3.org/2000/svg"}
            %path{d: "M284.171126 772.335453l253.131559 0 0-50.346603L284.171126 721.988851 284.171126 772.335453 284.171126 772.335453 284.171126 772.335453zM284.171126 596.157081l455.656742 0L739.827867 545.811485 284.171126 545.811485 284.171126 596.157081 284.171126 596.157081 284.171126 596.157081zM587.943294 17.276367 436.057713 17.276367l0 50.345596L587.943294 67.621963 587.943294 17.276367 587.943294 17.276367 587.943294 17.276367zM284.171126 419.978708l455.656742 0 0-50.345596L284.171126 369.633112 284.171126 419.978708 284.171126 419.978708 284.171126 419.978708zM891.681228 17.276367 739.827867 17.276367c0-0.001007-75.942287-4.531929-75.942287 50.345596 0 34.56389-21.325541 46.628226-41.378397 50.345596l-213.28562 0c-21.847101-2.706471-49.106137-13.466908-49.106137-50.345596 0-61.758486-75.943294-50.345596-75.943294-50.345596l0 0L132.317765 17.276367c-27.976935 0-50.639602 22.531774-50.639602 50.345596l0 906.031434c0 27.814829 22.662667 50.345596 50.639602 50.345596l506.230897 0 50.639602 0 25.303691 0 227.828874-226.523969 0-25.139571 0-50.346603L942.32083 67.62297C942.32083 39.80814 919.65917 17.276367 891.681228 17.276367L891.681228 17.276367 891.681228 17.276367zM689.189272 973.653397 689.189272 797.475025c0-13.889794 11.315224-25.139571 25.303691-25.139571l177.189272 0L689.189272 973.653397 689.189272 973.653397 689.189272 973.653397zM891.681228 721.989857 689.189272 721.989857c-27.976935 0-50.639602 22.531774-50.639602 50.346603l0 201.317944L157.622463 973.654404c-13.989475 0-25.303691-11.249777-25.303691-25.172798L132.318772 92.795768c0-13.890801 11.315224-25.172798 25.303691-25.172798l121.397507 0c16.011273 5.445162 30.161847 19.075183 30.488073 50.345596 0.553779 55.008418 75.943294 50.313376 75.943294 50.313376l253.098332 0c0-0.001007 75.942287 0.22856 75.942287-57.61823 0-28.661608 18.94429-39.454265 37.890594-43.041749l113.995994 0c13.988468 0 25.303691 11.281997 25.303691 25.172798L891.682235 721.989857 891.681228 721.989857 891.681228 721.989857z"}
          Order Details
        .order-details
          .left
            Order No :  
            %strong= @order.number
          .center
            Ordered On :  
            %strong= @order.created_at.in_time_zone.strftime('%d %b %Y')
          .right
            Expected Delivery Date :  
            - if @order.expected_delivery_date.present?
              %strong= @order.expected_delivery_date.strftime('%d %b %Y')
            - else
              Not Available
      -show_domestic_pipeline = true
      - if @order.national_id_required?
        %p.document-upload.row
          %span.za-label For South Africa deliveries, National ID is mandatory for customs clearance
          %span.za-note Note : National ID is required only for customs clearance and will not be stored or saved by us — your privacy and data security are our top priority. 
          = link_to @order.document_upload_url, target: '_blank' do
            %span.upload-button Upload Document
      - if @order.international? || @all_line_items.map(&:stitching_required).include?('Y')
        = render :partial => '/orders/order_status_pipeline'
        -show_domestic_pipeline = false
      = render partial: 'orders/designer_orders', locals: {currency_details: currency_details, retry_state: @retry_state,show_domestic_pipeline: show_domestic_pipeline}
      = render partial: 'orders/summary', locals: {currency_details: currency_details}
      = render partial: 'orders/order_acknowledgement_address'
      -if @order.cancellable?
        #cancel_panel.row.text-center
          %a.button.tiny.cancel_button{data: {'reveal-id' => 'cancel-confirmation-modal'}} Cancel Order
          #cancel-confirmation-modal.reveal-modal{data: {reveal: ''}, aria: {hidden: true, labelledby: 'cancel-confirmation-modal-title'}, role: 'dialog'}
            %h4#cancel-confirmation-modal-title Do you really want to cancel this order?

            %p.cancel-reason-request
              %strong Please provide a reason for cancellation.
            %ul.selectize
              - t('order.cancellation.reasons').each_with_index do |reason, index|
                %li.selectize-option
                  = radio_button_tag "cancellation-reason-option", reason
                  = label_tag "cancellation-reason-option_#{reason}", reason
              %li.selectize-option.selectize-option-last
                = radio_button_tag "cancellation-reason-option", "Other"
                = label_tag "cancellation-reason-option_Other", "Other", class: 'selectize-other-option-toggle'
                = text_field_tag "cancellation-reason-other-input", '', class: 'selectize-other-option-toggle is-hidden'
            %div.cancel-order-reason-select-warning.is-hidden
              %em Please select one of the reasons for cancelling.

            / %p.cancel-helper-info
            /   %em If you select "Yes", then an OTP will be sent to your mobile number which you will have to enter in the next step.
            %hr
            %a.success.button.yes_button Yes 
            %a.alert.button.no_button{aria: {label: 'Close'}} No
            %a.close-reveal-modal{aria: {label: 'Close'}} &#215;

          #otp-verification-modal.reveal-modal{data: {reveal: ''}, aria: {hidden: true, labelledby: 'otp-verification-modal-title'}, role: 'dialog'}
            %a.close-reveal-modal{aria: {label: 'Close'}} &#215;
            #otp-form-and-content
              Enter verifcation code that has been sent to following number
              to confirm the cancellation of the order
              #otp-show-phone-details.row{type: 'text', value: @order.billing_phone, readonly: true}
                = @order.billing_phone
              #otp-error-msg.error-msg-for-otp
                *Incorrect OTP Please try again.
              %form#otp-form
                = text_field_tag 'cod-otp', '', class: 'cod-otp-input', autocomplete: 'off', required: true, type: 'tel', maxlength: '5'
                .row
                  = submit_tag 'Confirm', id: 'otp-submit-button', class: 'button radius success cod-otp-modal-buttons'
                .otp-resend-message.text
                  .row
                    Didn't receive verification code yet?
                    %button#resend-otp.button-as-text{type: 'button'}
                      %span Resend
      - elsif ['cancel','new','pending','confirmed','cancel_complete'].exclude? @order.state
        #return_panel.row.text-center
          .columns.bordered_block
            .row
              -if current_account.present? && current_account.user?
                %button.button.tiny.policy_button{"data-reveal-id" => "return_policy"} Return
                #return_policy.reveal-modal{data: {reveal:"", v_offset: "0"}, style: 'padding: 0px;'}
                  .modal-dialog.modal-sm-size
                    .modal-content{style: 'padding:4%;font-size: 12px;'}
                      %a{class:"close-reveal-modal",'aria-label'=> 'Close', style: 'position: absolute; top: 10px; right: 15px; font-size: 20px; color: #999; text-decoration: none;'} &#215
                      .modal-header
                        %h6.modal-title{style:'text-decoration:underline;'} Return Policy
                      .modal-body
                        = render :partial => '/orders/return_policy', :locals => {:order => @order}
                      .modal-footer{style: 'text-align:center;'}
                        - if @order.domestic?
                          =link_to 'I Agree', user_order_return_items_path(current_account.try(:user),order_number: @order.number),class: 'button tiny', data: {turbolinks: 'false'}
                        %a.button.tiny.close-reveal-modal.close-button{href: "#", role: "button"} Close
              -else
                =link_to 'Return', new_account_session_url(protocol: Rails.application.config.partial_protocol), class: 'button tiny radius', style: 'background-color: #670b19;', data: {turbolinks: 'false'}
                
  -# - if @newly_added_products.present? and is_domestic?
  -#   =render partial: 'pages/newly_added_products', locals: {designs: @newly_added_products}   #commented out as we dont need newly added products as of now

  -# - if @track && JABBER_REVIEW_ENABLED  
    #InstantFeedbackWidget
      #InstantFeedbackWidgetInner.stjr-widget
    
    :javascript
      (function (src, callback) {

        var s, r, t; r = false;
        s = document.createElement('script');
        s.type = 'text/javascript';
        s.src = src;
        s.onload = s.onreadystatechange = function () {
          if (!r && (!this.readyState || this.readyState == 'complete')) {
            r = true; 
            callback();
          }
        };

        t = document.getElementsByTagName('script')[0];
        t.parentNode.insertBefore(s, t);
      }
      ('https://biz.sitejabber.com/js/widget.1573845672.js', function () {

        new STJR.InstantFeedbackWidget(
          {
            id: 'InstantFeedbackWidget',
            url: 'mirraw.com',
            user: {
              first_name: "#{@order.name.split(' ')[0]}",
              last_name:  "#{@order.name.split(' ')[1]}"
            },
            order_id: "#{@order.number}"
          }).render();
      }));

:javascript

  function closeAddonForm(id){
    $('body').css('overflow', 'scroll');
    $('.fixed').css('display', 'block');
    $('#modal_'+id).css('display', 'none');
  }

  function loadAddonForm(event, id){
    event.preventDefault();
    var frametarget = $(this).attr('href');
    $('body').css('overflow', 'hidden');
    $('.fixed').css('display', 'none'); // ios fix
    $('#modal_'+id).css('display', 'block');
  }

  $(document).ready(function(){

    setTimeout(function(){
      $("#get_app_link").slideDown('slow');
    }, 500);
    
    $("#close_link").on('click', function(){
      $("#get_app_link").slideUp('slow');
    });
  });

  $(document).on('click','.policy_button',function(){
    $('body').addClass('modal-open');
  });

  $(document).on('click', '.close-reveal-modal', function(e) {
    return $('body').removeClass('modal-open');
  });

  $(document).on('click', '.circle_stage_domestic,.uc_base', function(){
    id  = $(this).attr('id')
    background =  id.split("_")[1]
    text_id = "#status_text_"+id.split("_")[2]
    if( background == 'yellowgreen')
      note_text = "Your order has been successfully " + id.split("_")[0] + "."
    else
      note_text = "Your Order Will Be " + id.split("_")[0] + " Shortly."
    if (!$(text_id).text().includes('Your order has been canceled'))
      $(text_id).text(note_text) ;
  });

  $(document).on('click', '#cancel-confirmation-modal .no_button', function(event) {
    event.preventDefault();
    $('#cancel-confirmation-modal').foundation('reveal', 'close');
  });

  let cancellationReason;
  $(function() {
    cancellationReason = new Selectize($('.selectize'));
    $('.cancel-order-reason-select-warning').hide();
  });

  $(document).on('click', '#cancel-confirmation-modal .yes_button', function(event) {
    event.preventDefault();

    if (cancellationReason.isValid()) {
      $.ajax({
        type: 'POST',
        url: '/user/cancel_order',
        data: {
          order_number: "#{@order.number}"
        },
        success: function() {
          $('#cancel-confirmation-modal').foundation('reveal', 'close');
        }
      });
    } else {
      $('.cancel-order-reason-select-warning').show();
    }
  });

  $(document).on('click', '#otp-verification-modal #resend-otp', function(event) {
    event.preventDefault();

    $('#otp-error-msg').css('display', 'none');
    $('.cod-otp-input').val('');

    $.ajax({
      type: 'POST',
      url: '/otp/deliver',
      data: {
        order_number: "#{@order.number}"
      },
      dataType: 'script'
    });
  });

  $(document).on('submit', '#otp-form', function(event) {
    event.preventDefault();

    $.ajax({
      type: 'POST',
      url: '/otp/verify',
      data: {
        order_number: "#{@order.number}",
        code: $('#cod-otp').val(),
        order_cancel_reason: cancellationReason.value()
      },
      dataType: 'script',
      error: function() {
        $('#otp-error-msg').css('display', 'inline');
      }
    });
  });
- purchase_event_trigger = trigger_ga4_purchase_event(@order)
- if purchase_event_trigger
  - coupon_discount_amount = @order.discount_currency(currency_details[:rate]) || 0
  - tax_amount = @order.total_tax_currency(currency_details[:rate]) || 0
  - gift_wrap = @order.order_addon.present? ? @order.order_addon.gift_wrap_price_currency(currency_details[:rate]) : 0
  - if (market_rate = CurrencyConvert.countries_marketrate[@country_code]).present?
    - gift_wrap = (gift_wrap *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
    - coupon_discount_amount = (coupon_discount_amount *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
    - tax_amount = (tax_amount *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  - percent_off = (coupon_discount_amount * 100.0 / @totalvalue)
  :javascript
    window.dataLayer = window.dataLayer || [];
    var gads_items_id = #{@googe_add_hash_new.to_json};
    var customer_details = {
      name: "#{@name}",
      email: "#{@email}",
      city: "#{@city}",
      country: "#{@country}",
      state: "#{@state}",
      zipcode: "#{@pincode}",
      phonenumber: "#{@phone_number}"
    }
    var prehash_customer_details = {
      name: "#{@name_without_hash}",
      email: "#{@email_without_hash}",
      timestamp: "#{@timestamp}",
      total: "#{@item_total}",
      signature: "#{@signature}"
    }
    var global_currency = "#{@symbol}"
    var global_value = "#{@item_total}"
    var ga4_purchase_params = #{@ga_hash_new.to_json}; 
    var customizationTotal = 0;
    ga4_purchase_params.gift_wrap = #{gift_wrap}
    ga4_purchase_params.payout = #{@payout}
    ga4_purchase_params.coupon_discount = #{coupon_discount_amount};
    ga4_purchase_params.tax = #{tax_amount};
    ga4_purchase_params.items.forEach(function (item) {
      customizationTotal += item.item_customization * item.quantity;
    });
    ga4_purchase_params.customization = customizationTotal
    ga4_purchase_params.value = (#{@totalvalue} + #{gift_wrap}) - #{coupon_discount_amount} + customizationTotal;
    ga4_purchase_params.item_ids = gads_items_id.item_ids
    dataLayer.push({ ecommerce: null });
    dataLayer.push({
      event: "ga4_purchase",
      ecommerce: ga4_purchase_params
    });
