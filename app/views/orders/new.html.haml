- content_for :page_specific_css_body do
  = stylesheet_link_tag 'orders'
- content_for :page_specific_js_body do
  = javascript_include_tag 'orders'

= Gon::Base.render_data
=render partial: '/layouts/initialise_grading'
- autocomplete = request.env['HTTP_USER_AGENT'].present? && request.env['HTTP_USER_AGENT'].downcase.match('firefox') ? 'off' : 'on'
- discounts = @cart.total_discounts_currency(@rate,@country_code)
- prepaid_discount = @cart.prepaid_discount.to_i
- cod_charges = get_price_in_currency(@cod_charges_inr||0) || 0 #Cart.cod_charge_currency(@rate)
- item_total = @cart.items_total_without_addons(@rate)
- platform_fee = @cart.get_platform_fee(@country_code, 'mobile', @rate, @subscription_plan.present?, @active_subscription.present?).round(2)
- addons_charges = @cart.addons_total(@rate)
- wallet_details = @cart.wallet_details(@country_code)
- wallet_referral = wallet_details[:referral_amount].round(2)
- wallet_discount = wallet_details[:total].round(2)
- total_before_shipping = item_total + addons_charges - discounts
- order_country = @canceled_order.present? ? @canceled_order.country : @shipping_address.country
- shipping_charges = 0
- essential_shipping_charges, essential_total, all_essential = Order.get_essential_shipping(@cart.line_items) if order_country == 'India' || @country_code == 'IN'
- prepaid_shipping_promo = (@country_code == 'IN' && DOMESTIC_PREPAID_SHIPPING_PROMOTION && @billing_address.country == 'India')  ? 'available' : ""
- shipping_charges += (order_country == 'India' || @country_code == 'IN') ? (all_essential ? essential_shipping_charges : @cart.shipping_cost_currency(order_country, @rate, total_before_shipping - essential_total)+ essential_shipping_charges) : @cart.shipping_cost_currency(order_country, @rate)
- prepaid_promotion = @country_code == 'IN' && @billing_address.country == 'India'
- grandtotal = total_before_shipping + shipping_charges
- if session[:gift_wrap] && GIFT_WRAP_PRICE.to_f >= 0
  - grandtotal += get_price_in_currency(GIFT_WRAP_PRICE.to_f)
- if @prepaid_failed_retry && (@cart.cod?(@cod_charges_inr,@billing_address.country,@billing_address.pincode,@rate) || (@billing_address != @shipping_address))
  = render partial: 'prepaid_fail_popup', locals: {you_pay: get_price_with_symbol((grandtotal).round(2), @symbol)}
- tax_percent, tax_amount, grandtotal, tax_enable = @cart.get_total_with_tax(@country_code, grandtotal)
- tax_details = {tax_percent: tax_percent.to_f, tax_amount: tax_amount.to_f, tax_enable: tax_enable,grandtotal: grandtotal.to_f }
- cod_total = grandtotal
- grandtotal -= wallet_discount
- grandtotal = grandtotal.round(2)
-# :javascript
  if(typeof ga != 'undefined'){
    gaOrder(#{@ga_hash.to_json})
  }


/code for modal
- if(COD_CONFIRMATION_MESSAGE != 'false') && @shipping_address.country.downcase == 'india'
  = render partial: 'orders/cod_verification_modal'
= render partial: 'layouts/payment_steps'
.row#create_orders_block
  .large-12.medium-12.small-12.columns.small-centered{style:'margin-top:47px'}
    .row.flash-message.flash-msg
      - zurbs_flash_classes = {'notice' => 'success', 'alert' => 'warning', 'error' => 'alert'}
      - flash.each do |key, msg|
        .alert-box.radius{data: {alert: ''}, class: zurbs_flash_classes[key]}
          = msg
          %a{href: '#', class: 'close'} &times;
    .row
      .checkout_message_icons 
        .payment_images  
          = image_tag('payment_image_group.jpg', class: 'lazy img-responsive') 
        - if is_domestic?
          .payment_images  
            = image_tag('upi.png', class: 'lazy img-responsive')
        -else 
          .payment_images  
            = image_tag('paypal.png', class: 'lazy img-responsive')      
    .row
      .large-6.medium-12.small-12
    = form_for @order, :html => {:autocomplete => autocomplete} do |f|
      .row
        .large-6.medium-12.small-12.columns
          .row
            .order_total.bordered_block.columns
              .left You Pay :
              #you_pay_grand_total.right.grand_total.top= get_price_with_symbol((grandtotal).round(2), @hex_symbol)
              #you_pay_grand_total_with_cod.right.grand_total_with_cod.hide= get_price_with_symbol(grandtotal + cod_charges + wallet_referral, @hex_symbol)

          - if @dom_offers.present?
            = render partial: 'orders/offer_tab'
            %br
          - if @juspay_ec && @juspay_payment_options.present? && @billing_address.country == 'India'
            = render partial: 'orders/payments/dom_payments', locals: {f: f, shipping: shipping_charges, grandtotal: grandtotal, prepaid_shipping_promo: prepaid_shipping_promo, prepaid_discount: prepaid_discount, mastercard_discount: @mastercard_discount}
            #action_buttons.sticky-mobile.sticky-button
              = f.submit 'PLACE  ORDER', data: {disable_with: "Wait.."}, class: 'add_checkout button small expand'
          - else
            = render partial: 'orders/payments/int_payment_options', locals: {f: f, shipping: shipping_charges, grandtotal: grandtotal, prepaid_shipping_promo: prepaid_shipping_promo, prepaid_discount: prepaid_discount, prepaid_promotion: prepaid_promotion, domestic: prepaid_promotion} 
            #action_buttons.sticky-mobile-int.sticky-button{:style => "display: none;"}
              = f.submit 'PLACE  ORDER', data: {disable_with: "Wait.."}, class: 'add_checkout button small expand'
          .row.credit_card_information
            %h5 Is it safe to use my Credit Card here ?
            %p All transactions on Mirraw are 100% safe. We work with secured payment partners to process your payment. All sensitive information is protected from being viewed by anyone.
        .large-6.medium-12.small-12.columns
          -if account_signed_in? && @user.wallet.present? && @symbol == @user.wallet.currency_symbol
            = render partial: 'orders/wallet_info', locals: {total: grandtotal, cart: @cart, shipping_charges: shipping_charges, wallet_discount: wallet_discount, symbol: @symbol, country_code:@country_code, billing_address:@billing_address, shipping_address: @shipping_address, rate: @rate, referral_amount: wallet_referral,cod_charges: cod_charges, prepaid_discount: prepaid_discount, shipping: shipping_charges}
          - if @cart.ready_to_ship_designs? && Country.get_express_delivery_countries.include?(@shipping_address.country) && EXPRESS_DELIVERY.to_i >= 0 && !(@cart.warehouse_available_designs? && RTS_ALLOWED_COUNTRIES.include?(@shipping_address.country.try(:downcase))) || EXPRESS_SHIPPING_COUNTRY_CODE.include?(@country_code)
            - express_country_charge = Country.get_express_delivery_charge(@shipping_address.country)
            = render partial: 'orders/express_delivery', locals: {shipping: shipping_charges, grandtotal: grandtotal, express_shipping: express_country_charge}
          = render partial: 'carts/cart_mini_info', locals: {grandtotal: grandtotal, discounts: discounts, shipping_charges: shipping_charges, addons_charges: addons_charges, cod_charges: cod_charges, wallet_discount: wallet_discount, rate: @rate, country_code: @country_code, referral_amount: wallet_referral, prepaid_discount: prepaid_discount, mastercard_discount: @mastercard_discount, tax_details: tax_details, cod_total: cod_total, platform_fee: platform_fee}
          
          - if @shipping_address.country.downcase != 'india' && Promotion.free_shipping_on_country?(@shipping_address.country)
            = render partial: 'carts/promotion_offers', locals: {offers: @offers, continue_shopping: false}
          = render partial: 'orders/order_address'
          = f.hidden_field :shipping_address, value: @shipping_address.id
          = f.hidden_field :billing_address, value: @billing_address.id
          = hidden_field_tag 'prepaid_discount', 0
          = hidden_field_tag 'mastercard_discount', 0
          .row.sticky-row
            - if @juspay_ec && @juspay_payment_options.present? && @billing_address.country == 'India'
              #action_buttons.sticky-desktop.sticky-button
                = f.submit 'PLACE  ORDER', data: {disable_with: "Wait.."}, class: 'add_checkout button small expand'
            - else 
              #action_buttons.sticky-desktop-int.sticky-button{:style => "display: none;"}
                = f.submit 'PLACE  ORDER', data: {disable_with: "Wait.."}, class: 'add_checkout button small expand'

          - unless @user.account.terms_of_service
            = check_box_tag(:newsletter_confirmation, 'send_newsletter', true)
            = label_tag(:newsletter_confirmation, 'I agree to receive news letters from Mirraw')

          %ul
            - if CUSTOM_DUTY_COUNTRY['enable'] && @customs_charge_message.present?
              %li{style: 'white-space: normal'}
                = @customs_charge_message
- if @razorpay_form
  = render partial: 'razorpay_payment'
- if @retry_paypal_form
  = render partial: 'paypal_success'

:javascript
  var getCookie = function(cname){
    var name = cname + "=";
    var ca = document.cookie.split(';');
    for(var i=0; i<ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0)==' ') c = c.substring(1);
        if (c.indexOf(name) == 0) return c.substring(name.length,c.length);
    }
    return "";
  }

  $(window).on('load',function(){
    CheckOrders.app.initVar();
    CheckOrders.app.set_cod_restrictions();
    if(getCookie('payment_failure_count') >= 2){
      $('.bank_deposit_order_text').html('If you are facing issues in PayPal Payment, place <span>Bank Deposit</span> Order. An email with Account details will be sent to you.');
      $('#bank_deposit_order_box').slideDown(500,function(){
        setTimeout(function(){ $('#bank_deposit_order_box').slideUp(500); }, 10000);
      });
    }
    $('.bank_deposit_order_text_close').on('click',function(){
      $('#bank_deposit_order_box').fadeOut(1000)
    });
  });


-# :javascript
  addEvent(window, 'load', function(){
    fbq('track', 'InitiateCheckout'); 
  })
- shipping_currency = shipping_charges
- pro_totals, max_total, shipping = get_cart_total_information(@country_code, @rate)
- coupon_discount_amount = pro_totals.find { |item| item[:title] == "Coupon Discounts" }&.dig(:amount) || 0
- tax_amount = pro_totals.find { |item| item[:title] == "Tax" }&.dig(:amount) || 0
- total_amount = pro_totals.find { |item| item[:title] == "Item Total" }&.dig(:amount) || 0
- gift_wrap = session[:gift_wrap] && @country_code != 'IN' ? get_price_in_currency(GIFT_WRAP_PRICE.to_f) : 0
- if (market_rate = CurrencyConvert.countries_marketrate[@country_code]).present?
  - gift_wrap = (gift_wrap *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  - shipping_currency = (shipping_currency *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  - tax_amount = (tax_amount *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  - coupon_discount_amount = (coupon_discount_amount *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
-if coupon_discount_amount != 0
  -discount_percent = (coupon_discount_amount*100.0/@totalvalue)
-else
  -discount_percent = 0
:javascript
  window.dataLayer = window.dataLayer || [];
  function getTax(){
    return ga4_checkout_params.tax = #{tax_amount}
  }
  function getShipping(){
    return ga4_checkout_params.shipping = #{shipping_currency}
  }
  var total =  (#{@totalvalue} + #{gift_wrap})- #{coupon_discount_amount}
  var ga4_checkout_params = #{@ga_hash_new.to_json};
  var customizationTotal = 0;
  var gads_items_id = #{@googe_add_hash_new.to_json};
  ga4_checkout_params.items.forEach(function (item) {
    customizationTotal += item.item_customization * item.quantity;
  });
  ga4_checkout_params.customization = customizationTotal
  ga4_checkout_params.shipping = #{shipping_currency}
  ga4_checkout_params.value = total + customizationTotal
  ga4_checkout_params.gift_wrap = #{gift_wrap}
  ga4_checkout_params.tax = #{tax_amount}
  ga4_checkout_params.coupon_discount = #{coupon_discount_amount}
  ga4_checkout_params.item_ids = gads_items_id.item_ids
  $('.add_checkout').on('click', function(e) {
    var paytype
    if (gon.country_code == 'IN')
      payType = $('[name=payment_option]').filter(":checked").val()
    else
      payType = $('input[name="order[pay_type]"]:checked').val()
    if (payType == undefined)
      payType = "credit / debit card"
    var ga4_payment_params = #{@ga_hash_new.to_json};
    ga4_payment_params.items.forEach(function (item) {
      customizationTotal += item.item_customization * item.quantity;
    }); 
    ga4_payment_params.customization = customizationTotal
    ga4_payment_params.payment_type = payType;
    ga4_payment_params.tax = #{tax_amount}
    ga4_payment_params.shipping = #{shipping_currency}
    ga4_payment_params.value =  (#{@totalvalue} + #{gift_wrap}) - #{coupon_discount_amount} + customizationTotal
    ga4_payment_params.gift_wrap = #{gift_wrap}
    ga4_payment_params.coupon_discount = #{coupon_discount_amount}
    dataLayer.push({ ecommerce: null });
    dataLayer.push({
      event: "ga4_add_payment_info",
      ecommerce: ga4_payment_params
    });
  });