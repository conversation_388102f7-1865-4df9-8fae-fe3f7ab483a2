- content_for :page_specific_css_body do
  = stylesheet_link_tag 'orders'
- content_for :page_specific_js_body do
  = javascript_include_tag 'orders'
  = javascript_include_tag 'desktop_address'
= Gon::Base.render_data
=render partial: '/layouts/initialise_grading'
= render partial: 'layouts/payment_steps'
- data = order_summary_data(order: @order,cart: @cart,rate: @rate,country_code: @country_code,cod_charges_inr: @cod_charges_inr,billing_address: @billing_address,shipping_address: @shipping_address,session: session)
- country_data = prepare_country_data(@order, @country_code)
= form_for @order, :html => {:autocomplete => data[:autocomplete]} do |f|
  -# .row.flex-row
  -#   .left-column.large-4.medium-6.small-12.columns
  -#     =f.text_field :paypal_smart_pay, value: (@paypal_smartpay ? 1 : 0), hidden: true
  -#     =f.text_field :paypal_inline_cc, value: (@show_inline_cc ? 1 : 0), hidden: true
  .checkout-header
    %div
      %a{href: '/', 'aria-label' => "Home"}
        = image_tag('logo-red.png', alt: '', class: 'logo-img')
    .payment-support
      %img= image_tag("checkout/payment_red.png", :align => "right", :style => 'background:none')
  .shipping-payment-text
    .span
      shipping and payment
  .checkout-container
    .left-column
      .col-lg-6.col-md-6.col-sm-6.nopadding.padding_right.billing_details
        #error_explanation.hide
          .notice{style: 'color: black;'}
    
        - if @order.errors.any?
          #error_explanation
            - if @cod == false
              .notice= 'Cash on delivery (COD) not available'
            %b Oops! Correct the following:
            %ul
              - @order.errors.keys.each do |key|
                - if key == :pay_type && @order.errors[key].first.present?
                  %li.notice Payment Method: Please select appropriate payment method
                - else
                  %li.notice= @order.errors[key].first
        = hidden_field_tag 'form_type', params[:action]
        .payment_box_style
          .checkout_head
            %h4 1. Billing Details
            .change_address
              %a{:href => "#", :id => "open_modal"} Change Address
            .clr
          - # TODO check if billing-form class is neeeded
          .payment_row
            %ul#billing_details.billing-form
              .billing-fields
                %li.form-field
                  = f.label :billing_first_name, "First name"
                  = f.text_field :billing_first_name, required: true, label: false
                %li.form-field
                  = f.label :billing_last_name, "Last name"
                  = f.text_field :billing_last_name, required: true, label: false
                  %li#seamless-account-email.form-field
                    = f.label :billing_email, "Email"
                    = f.text_field :billing_email, label: false
                  %li.form-field
                    = f.label :billing_country, "Country / Region"
                    %input{id: 'order_shipping_restrict', value: country_data[:shipping_restrict], type: 'hidden'}
                    = f.select :billing_country, options_for_select(country_data[:countries], :disabled => country_data[:disabled_value].first, :selected => @order.billing_country), :data => {:restrict => country_data[:shipping_restrict]}, :include_blank => "Please Select", label: false
                  %li.form-field
                    #showpincodefields
                      .pin-code-field
                        = f.label :billing_pincode, "Pincode / Zip / Postal Code"
                        .pin-code-field-helper
                          = f.text_field :billing_pincode, label: false
                          #pincode_format_notice.order-field-format-notify-text
                          .clearfix
                  .order-address-line-field
                    - if country_or_currency_eligble_for_multiline_address?
                      %li.form-field
                        = f.label :billing_street_line_1, 'Address Line 1'
                        = f.text_field :billing_street_line_1, placeholder: 'Flat No, House No, Floor, Building Name ', minlength: 3, label: false
                      %li.form-field
                        = f.label :billing_street_line_2, 'Address Line 2'
                        = f.text_field :billing_street_line_2, placeholder: 'Colony, Street, Locality, Landmark', minlength: 3, label: false
                    - else
                      %li.form-field
                        = f.label :billing_street, "Street Address"
                        = f.text_area :billing_street, minlength: 3, label: false, class: 'street-address'
                  %li.form-field
                    = f.label :billing_city, "City"
                    = f.text_field :billing_city, minlength: 3, label: false
                  - if country_data[:billing_states].present?
                    %li.form-field
                      = f.label :billing_state, "State  / Province"
                      = f.select :billing_state, country_data[:billing_states], :include_blank => "Please Select", label: false
                  - else
                    %li.form-field
                      = f.label :billing_state, "State / Province"
                      = f.text_field :billing_state, label: false
                  %li.mobile-field.form-field
                    = f.label :billing_phone, "Mobile No"
                    .number-field
                      = text_field 'dial_code', '', style: 'text-align: center;', id: 'phone-code1', class: 'input-phone-addon phone', autocomplete: 'none', label: 'dial_code'
                      = f.text_field :billing_phone, class: 'phone', label: false
                -#
                  For some weird reason, `@seamless_account` becomes `nil` sometimes.
                  This is to protect that scenario until it is fixed. :)
                - if !account_signed_in? && @seamless_account
                  = hidden_field_tag :exists, @seamless_account[:exists]
                  %li.seamless-account-password{class: "#{'is-hidden' if !@seamless_account[:seamless]}"}
                    = label_tag :password, t('account.password.type')
                    = password_field_tag :password, @seamless_account[:password]
                  %li#seamless-account-check.address{class: "#{'is-hidden' if @seamless_account[:exists]}"}
                    = check_box_tag :seamless, 'seamless', @seamless_account[:seamless]
                    = label_tag :seamless, t('account.seamless.ask')
                    .clr
      
                %li.address.form-field
                  .same-address-container
                    = label_tag "order_billing_country", ''
                    = check_box_tag(:ship_to_same_address, "1", @ship_to_same_address)
                    = label_tag(:ship_to_same_address, "Ship to Same Address", :class => 'ship_to_label')
                    .clr
                %p.notice.ship_to_same_address_note{:style => 'display:none'}
                  For Cash on Delivery Orders, Billing Address and Shipping Address should be same.
          - style = @ship_to_same_address ? "display:none" : ""
        .payment_box_style.shipping-show{:style => style}
          .checkout_head
            %h4  Shipping details
            .clr
          .shipping-form
            %ul#billing_details.billing-form
              .billing-fields
                %li.form-field
                  = f.label :first_name
                  = f.text_field :first_name, required: true, label: false
                %li.form-field
                  = f.label :last_name
                  = f.text_field :last_name, required: true, label: false
                %li.form-field
                  = f.label :country, "Country/Region"
                  = f.select :country, options_for_select(country_data[:ship_countries], :disabled => country_data[:disabled_value].first, :selected => @order.country), :include_blank => "Please Select", label: false
                %li.form-field
                  #showpincodefieldsshipping
                    .pin-code-field
                      = f.label :pincode, "Pincode / Zip / Postal Code"
                      .pin-code-field-helper
                        = f.text_field :pincode, label: false
                        #pincode_format_notice_shipping.form-field
                        .clearfix
                - if country_or_currency_eligble_for_multiline_address?
                  .order-address-line-field
                    %li.form-field
                      = f.label :street_line_1, 'Address Line 1'
                      = f.text_field :street_line_1, placeholder: 'Flat No, Floor, Building Name', minlength: 3, label: false
                    %li.form-field
                      = f.label :street_line_2, 'Address Line 2'
                      = f.text_field :street_line_2, placeholder: 'Colony, Street, Locality. Landmark', minlength: 3, label: false
                -else
                  .order-address-line-field
                    %li.form-field
                      = f.label :street, "Street Address"
                      = f.text_area :street, minlength: 3, label: false, class: 'street-address'
                %li.form-field
                  = f.label :city
                  = f.text_field :city, minlength: 3, label: false
                - if country_data[:buyer_states].present?
                  %li.form-field
                    = f.label :buyer_state, "State / Province"
                    = f.select :buyer_state, country_data[:buyer_states], :include_blank => "Please Select", label: false
                - else
                  %li.form-field
                    = f.label :buyer_state, "State / Province"
                    = f.text_field :buyer_state, label: false
                %li.mobile-field.form-field
                  = f.label :phone, "Phone"
                  - unless @order.try(:phone).present?
                    .number-field
                      = text_field 'dial_code', '', style: 'text-align: center;', id: 'phone-code2', class: 'input-phone-addon phone', autocomplete: 'non e', label: 'dial_code'
                      = f.text_field :phone, class: 'phone', label: false
        .credit_card_information
          %h5 Is it safe to use my Credit Card here ?
          %p All transactions on Mirraw are 100% safe. We work with secured payment partners to process your payment. All sensitive information is protected from being viewed by anyone.
        #how_to_pay
          - if country_or_currency_indian?
            %h5 How can I pay? I am in India
            %ul
              %li Credit Card / Debit Card / Netbanking (India only)
              -# Cash Before Delivery is currently disabled
              -# %li
              -#   = "Cash Before Delivery (India only - "
              -#   = link_to 'learn more', 'https://mirraw.freshdesk.com/support/solutions/articles/**********-what-is-cash-before-delivery-', target: '_blank'
              -#   = ")"
              %li Cash On Delivery (Limited Pincodes)
              %li
                = "Bank Deposit ("
                = link_to 'learn more', pages_faq_url, target: '_blank'
                = ")"
              %li Paytm
          - else
            %h5 How can I pay? I am outside India
            %ul
              %li Paypal
              %li Credit Card
              %li
                = "Bank Deposit ("
                = link_to 'learn more', pages_faq_url, target: '_blank'
                = ")"
    .right-column
      .payment-container
        %div
          - if (@cart.ready_to_ship_designs? && Country.get_express_delivery_countries.include?(country_data[:country].name) && EXPRESS_DELIVERY.to_i >= 0 && !(@cart.warehouse_available_designs? && RTS_ALLOWED_COUNTRIES.include?(country_data[:country].name)) ) || EXPRESS_SHIPPING_COUNTRY_CODE.include?(@country_code)
            - express_country_charge = Country.get_express_delivery_charge(country_data[:country].name)
            = render partial: 'orders/express_delivery', locals: {shipping: data[:shipping_charges], grandtotal: data[:grandtotal], express_shipping: express_country_charge, tax_details: data[:tax_details]}
        .payment-and-items
          .order_total
            %h4
              3. Payment Method
            .total-payable-amount
              .payment-content AMOUNT PAYABLE:
              .payment-amount
                #you_pay_grand_total.grand_total.top= get_price_with_symbol((data[:grandtotal]).round(2), @hex_symbol)
                #you_pay_grand_total_with_cod.grand_total_with_cod.hide= get_price_with_symbol(data[:grandtotal] + data[:cod_charges] + data[:wallet_referral], @hex_symbol)
          - if @dom_offers.present?
            = render partial: 'orders/offer_tab'
            %brdiscounts
          - if @juspay_ec && @juspay_payment_options.present? && @order.billing_country == 'India'
            = render partial: 'orders/payments/dom_payments', locals: {f: f, shipping: data[:shipping_charges], grandtotal: data[:grandtotal], prepaid_shipping_promo: data[:prepaid_shipping_promo], prepaid_discount: data[:prepaid_discount], mastercard_discount: @mastercard_discount, order: @order}
            #action_buttons.sticky-mobile.sticky-button
              = f.submit 'PLACE  ORDER', data: {disable_with: "Wait.."}, class: 'add_checkout button small expand'
          - else
            = render partial: 'orders/payments/int_payment_options', locals: {f: f, shipping: data[:shipping_charges], grandtotal: data[:grandtotal], prepaid_shipping_promo: data[:prepaid_shipping_promo], prepaid_discount: data[:prepaid_discount], prepaid_promotion: data[:prepaid_promotion], domestic: data[:prepaid_promotion], order: @order} 
            #action_buttons.sticky-mobile-int.sticky-button{:style => "display: none;"}
              = f.submit 'PLACE  ORDER', data: {disable_with: "Wait.."}, class: 'add_checkout button small expand'
        .last-section
          - if country_or_currency_indian? && Wallet.cashback_percent > 0
            .discount-container
              .payment-text Pay Online and Get #{Wallet.cashback_percent}% Cashback on orders above #{Wallet.min_order_total_for_cashback}
          -if account_signed_in? && @user.wallet.present? && @symbol == @user.wallet.currency_symbol && @billing_address.present? && @shipping_address.present?
            = render partial: 'orders/wallet_info', locals: {total: data[:grandtotal], cart: @cart, shipping_charges: data[:shipping_charges], wallet_discount: data[:wallet_discount], symbol: @symbol, country_code:@country_code, billing_address:@billing_address, shipping_address: @shipping_address, rate: @rate, referral_amount: data[:wallet_referral],cod_charges: data[:cod_charges], prepaid_discount: data[:prepaid_discount], shipping: data[:shipping_charges]}
          - if is_mobile_view?
            = render partial: 'carts/cart_mini_info', locals: {grandtotal: data[:grandtotal], discounts: data[:discounts], shipping_charges: data[:shipping_charges], addons_charges: data[:addons_charges], cod_charges: data[:cod_charges], wallet_discount: data[:wallet_discount], rate: @rate, country_code: @country_code, referral_amount: data[:wallet_referral], prepaid_discount: data[:prepaid_discount], mastercard_discount: @mastercard_discount, tax_details: data[:tax_details], cod_total: data[:cod_total]}
          - else
            = render partial: 'carts/cart_mini_info_old', locals: {grandtotal: data[:grandtotal], discounts: data[:discounts], shipping_charges: data[:shipping_charges], addons_charges: data[:addons_charges], cod_charges: data[:cod_charges], wallet_discount: data[:wallet_discount], rate: @rate, country_code: @country_code, referral_amount: data[:wallet_referral], prepaid_discount: data[:prepaid_discount], mastercard_discount: @mastercard_discount, tax_details: data[:tax_details], cod_total: data[:cod_total]}
          -# - if country != 'india' && Promotion.free_shipping_on_country?(@country)
          -#   = render partial: 'carts/promotion_offers', locals: {offers: @offers, continue_shopping: false}
          
       
          = hidden_field_tag 'prepaid_discount', 0
          = hidden_field_tag 'mastercard_discount', 0
          .sticky-row
            - if @juspay_ec && @juspay_payment_options.present? && @order.billing_country == 'India'
              #action_buttons.sticky-desktop.sticky-button
                = f.submit 'PLACE  ORDER', data: {disable_with: "Wait.."}, class: 'add_checkout button small expand'
            - else 
              #action_buttons.sticky-desktop-int.sticky-button{:style => "display: none;"}
                = f.submit 'PLACE  ORDER', data: {disable_with: "Wait.."}, class: 'add_checkout button small expand'
          %ul
            - if CUSTOM_DUTY_COUNTRY['enable'] && @customs_charge_message.present?
              %li{style: 'white-space: normal'}
                = @customs_charge_message
#change_address.modal_wrapper{:style => "display: none;"}
  .modal_box.scrollbar-thin
    .modal_header
      Click to select new address
      %a.close_btn{:href => "#"} ×
    .modal_body
      - if @user.present?
        - @user.addresses.each_with_index do |address, i|
          .col-md-5.col-sm-5.col-xs-12.saved_address{:id => "saved_address_#{i}"}
            %span
              = "#{address.name} - #{address.street_address}, #{address.city}, #{address.pincode} #{address.state}, #{address.country} #{address.phone}"
      - else
        No Address found
