- content_for :page_specific_css do
  = stylesheet_link_tag 'orders_red'
.order-items
  .order-ack-header
    %svg{viewbox: "0 0 24 24", xmlns: "http://www.w3.org/2000/svg"}
      %path{d: "M22 8a.76.76 0 0 0 0-.21v-.08a.77.77 0 0 0-.07-.16.35.35 0 0 0-.05-.08l-.1-.13-.08-.06-.12-.09-9-5a1 1 0 0 0-1 0l-9 5-.09.07-.11.08a.41.41 0 0 0-.*********** 0 0 0-.********** 0 0 0-.********* 0 0 0 0 .1A.76.76 0 0 0 2 8v8a1 1 0 0 0 .52.87l9 5a.75.75 0 0 0 .13.06h.1a1.06 1.06 0 0 0 .5 0h.1l.14-.06 9-5A1 1 0 0 0 22 16V8zm-10 3.87L5.06 8l2.76-1.52 6.83 3.9zm0-7.72L18.94 8 16.7 9.25 9.87 5.34zM4 9.7l7 3.92v5.68l-7-3.89zm9 9.39v-5.68l7-3.91v5.69z"}
    Order Items
  - @order.designer_orders.each do |designer_order|
    .item
      .item-status
        -if show_domestic_pipeline
          =render :partial => '/orders/domestic_order_status',locals: {designer_order: designer_order}
        = designer_order.designer.name
      .order-right
        - @total_addons_price = 0 
        - designer_order.line_items.each do |line_item|
          .order-right-d
            .image-details
              .status
                %span
                  - if retry_state == false
                    = designer_order.state.try(:humanize)
              = link_to designer_design_path(line_item.design.designer, line_item.design), target: '_blank'  do
                - if line_item.design.master_image.present?
                  = image_tag(IMAGE_PROTOCOL + line_item.design.master_image.photo(:small), alt: line_item.design.title, class: 'image-cover')
                - else
                  = image_tag('default_image.jpg', alt: line_item.design.title, class: 'image-cover')
            .details
              -if @track
                .line_item_details.panel{ unbxdattr:"order", unbxdparam_sku:line_item.design.id, unbxdparam_price:line_item.design.price, unbxdparam_qty:line_item.quantity }
                %h6= line_item.design.title
                %p= "Price: #{get_price_with_symbol(line_item.snapshot_price_currency(currency_details[:rate]), currency_details[:hex_symbol])}"
                %p= "Quantity : #{line_item.quantity}"
                %p= "Notes : #{line_item.note_without_bmgn}" if line_item.note_without_bmgn.present?
                %p= "Total : #{get_price_with_symbol(line_item.total_currency(currency_details[:rate]), currency_details[:hex_symbol])}"
              -else
                %h6= line_item.design.title
                %p= "Price: #{get_price_with_symbol(line_item.snapshot_price_currency(currency_details[:rate]), currency_details[:hex_symbol])}"
                %p= "Quantity : #{line_item.quantity}"
                %p= "Notes : #{line_item.note_without_bmgn}" if line_item.note_without_bmgn.present?
                %p= "Total : #{get_price_with_symbol(line_item.total_currency(currency_details[:rate]), currency_details[:hex_symbol])}"
              - if (@addons = get_addons(line_item.id))
                - @line_item = line_item
                = render :partial => 'orders/order_addon', locals: {line_item: @line_item}
              - if !@order.international? && line_item.return.present?
                %button#return-track-button{href: '#', "data-reveal-id" => 'returnStatusModal' } Track Return
                #returnStatusModal.reveal-modal{"data-options" => "closeOnBackgroundClick:true", "aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", role: "dialog"}
                  %a.close-reveal-modal{"aria-label" => "Close" , style:"text-align:right"} &#215
                  %h4
                    .reveal-model.modal-title{style:'font-size:large; font-weight:600; text-align:center'}
                      Return Status
                      %hr
                      .reveal-model.modal-body
                        = render :partial => '/line_items/return_status', locals: {line_item: line_item}
        .row
          / - if @total_addons_price != 0 && @cart.mirraw_payable_addons?
          /   .notice_class= "*Due to Festival Rush, estimated time to deliver stitched product is 25 days from date of order."

