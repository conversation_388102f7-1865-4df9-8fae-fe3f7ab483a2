- if (@order.cod? || @order.cbd?) && (COD_NOTE_MESSAGE != 'false')
  .row
    .columns
      .alert-box.radius.info
        = "#{COD_NOTE_MESSAGE}"
.total-section
  .order-ack-header 
    %svg#Layer_1{"data-name" => "Layer 1", viewbox: "0 0 117.78 122.88", xmlns: "http://www.w3.org/2000/svg"}
      %path{d: "M70.71,116.29H7.46a7.48,7.48,0,0,1-5.27-2.19L2,113.87a7.43,7.43,0,0,1-2-5V7.46A7.45,7.45,0,0,1,2.19,2.19L2.42,2a7.42,7.42,0,0,1,5-2H91.88a7.48,7.48,0,0,1,7.46,7.46V66.63a3.21,3.21,0,0,1-.06.63,28.75,28.75,0,1,1-28.57,49ZM85.18,82.12h2.89a2,2,0,0,1,1.43.59,2.06,2.06,0,0,1,.6,1.44V94.77h9.59a2,2,0,0,1,2,2v3a2.12,2.12,0,0,1-.6,1.44l-.08.07a2,2,0,0,1-1.35.52H84a1,1,0,0,1-1-1V84a2,2,0,0,1,.59-1.29,2,2,0,0,1,1.43-.6Zm7.75-16.47V7.46a1.1,1.1,0,0,0-1.05-1H7.46a1.08,1.08,0,0,0-.66.23l-.08.08a1.06,1.06,0,0,0-.31.74V108.84a1,1,0,0,0,.23.65l.09.08a1,1,0,0,0,.73.32H65A28.75,28.75,0,0,1,89,65.38a28,28,0,0,1,3.9.27Zm12.36,12.22A23,23,0,1,0,112,94.13a22.92,22.92,0,0,0-6.73-16.26Zm-84.5-3.78h9A1.18,1.18,0,0,1,31,75.27v9a1.18,1.18,0,0,1-1.18,1.18h-9a1.18,1.18,0,0,1-1.18-1.18v-9a1.18,1.18,0,0,1,1.18-1.18Zm22,9.28a3.65,3.65,0,0,1,0-7.18h9.58a3.65,3.65,0,0,1,0,7.18Zm-22-61.22h9A1.18,1.18,0,0,1,31,23.33v9a1.18,1.18,0,0,1-1.18,1.18h-9a1.18,1.18,0,0,1-1.18-1.18v-9a1.18,1.18,0,0,1,1.18-1.18Zm22,9.27a3.33,3.33,0,0,1-3-3.58,3.34,3.34,0,0,1,3-3.59H78.25a3.34,3.34,0,0,1,3,3.59,3.33,3.33,0,0,1-3,3.58ZM18.34,54.1a2,2,0,0,1,.38-2.82,2.23,2.23,0,0,1,3-.09l2.1,2.17L29.07,48a1.93,1.93,0,0,1,2.82.3,2.23,2.23,0,0,1,.18,3l-7,7.14a1.94,1.94,0,0,1-2.82-.3l-.16-.19a1.94,1.94,0,0,1-.31-.26L18.34,54.1Zm24.4,2.69a3.34,3.34,0,0,1-3-3.59,3.34,3.34,0,0,1,3-3.59H78.25a3.34,3.34,0,0,1,3,3.59,3.34,3.34,0,0,1-3,3.59Z"}
    Order Summary
  %p
    %span State:
    %strong= @order.customer_state
  %p
    %span Item Total:
    %strong= get_price_with_symbol(@order.item_total_without_addons(currency_details[:rate]), currency_details[:hex_symbol])
  %p
    %span Discounts:
    %strong= get_price_with_symbol(@order.total_discount_currency(currency_details[:rate]), currency_details[:hex_symbol])
  - addons = @order.addons_total(currency_details[:rate])
  - if addons > 0
    %p
      %span Customisation:
      %strong= get_price_with_symbol(addons, currency_details[:hex_symbol])
  %p
    - shipping_cost = @order.shipping_currency(currency_details[:rate])
    - shipping_cost += @order.express_delivery_currency(currency_details[:rate]) if @order.express_delivery?
    %span Shipping Charges:
    %strong= get_price_with_symbol(shipping_cost, currency_details[:hex_symbol])
  %p
    %span COD Charges:
    %strong= get_price_with_symbol(@order.cod_charge_currency(currency_details[:rate]), currency_details[:hex_symbol])
  %p
    - if (order_addon = @order.order_addon).present?
      %span Gift Wrap Charges :
      %strong= get_price_with_symbol(@order.order_addon.gift_wrap_price_currency(currency_details[:rate]), currency_details[:hex_symbol])
  %p
    %span Total Tax:
    %strong= get_price_with_symbol(@order.total_tax_currency(currency_details[:rate]), currency_details[:hex_symbol])
  %hr/
  %p
    %strong Total:
    %strong= get_price_with_symbol(@order.total_currency(currency_details[:rate]), currency_details[:hex_symbol])
  -if @order.state == 'dispatched'
    %p
      %span Courier Company :
      = @order.courier_company
    %p
      %span Tracking Number :
      = @order.tracking_number

  - if RAKHI_PRE_ORDER[0] == 'true'
    %p
      - rakhi_pre = @order.rakhi_pre_order
      - if @order.country == 'India' && rakhi_pre['rakhi_with_other_designs'].present?
        .panel.callout.radius= "Your Order has multiple items, only Rakhi will be delivered between #{RAKHI_PRE_ORDER[1]}."
      - elsif rakhi_pre['rakhi_all_schedule'].present? || rakhi_pre['rakhi_with_other_designs'].present?
        .panel.callout.radius= "Your order will be delivered between #{RAKHI_PRE_ORDER[1]}."
