%div
  .bordered_block
    %h4 2. Shipping Options
    .shipping_options
      - delivery_message = 'Get your order delivered within '
      - country = @shipping_address.present? ? @shipping_address.country : Country.country_name_by_code[@country_code.upcase]
      - delivery_days = @cart.get_delivery_time(country, @shipping_address.try(:city),'regular')[0].to_i
      - delivery_days = (is_domestic? ? DOMESTIC_SHIPPING_TIME : INTERNATIONAL_SHIPPING_TIME) if delivery_days == 0
      - total_without_tax = tax_details[:grandtotal] - tax_details[:tax_amount]
      .row.shipping_option
        .small-8.medium-8.large-8.columns.shipping_radio_button
          %label
            - regular_shipping_price = get_price_with_symbol(shipping, @hex_symbol)
            - total_tax = get_price_with_symbol(@cart.get_total_with_tax(@country_code,(total_without_tax))[1], @hex_symbol)
            - grand_total = @cart.get_total_with_tax(@country_code,(total_without_tax))[2]
            - total_regular_delivery = get_price_with_symbol((grand_total.to_f + total_tax.to_f), @hex_symbol)
            = radio_button_tag(:delivery_type, 'regular', true, data: {grandtotal: grand_total, shipping: regular_shipping_price, total: total_regular_delivery, delivery_days: delivery_days, total_tax: total_tax})
            = 'Standard Shipping'
        #regular_shipping.right.shipping_option_price
          = shipping > 0 ? regular_shipping_price : 'FREE'
      .row
        .delivery_message #{delivery_message} #{delivery_days} days
      #express_delivery_division
        - delivery_time = EXPRESS_SHIPPING_COUNTRY_CODE.include?(@country_code) ? @cart.get_delivery_time(country, @shipping_address.try(:city),'express')[0].to_i : READY_TO_SHIP_DESIGNS.to_i + 1
        .row.shipping_option
          .small-8.medium-8.large-8.columns.shipping_radio_button
            %label
              - express_shipping_price = get_price_with_symbol(shipping + get_price_in_currency(express_shipping), @hex_symbol)
              - total_tax = get_price_with_symbol(@cart.get_total_with_tax(@country_code,total_without_tax+get_price_in_currency(express_shipping))[1], @hex_symbol)
              - grand_total = @cart.get_total_with_tax(@country_code,(total_without_tax + get_price_in_currency(express_shipping)))[2]
              - total_express_delivery = get_price_with_symbol((grand_total.to_f + total_tax.to_f), @hex_symbol)
              = radio_button_tag(:delivery_type, 'express', false, data: {grandtotal: grand_total, extraa_shipping: express_shipping, shipping: express_shipping_price, total: total_express_delivery, delivery_days: delivery_time, total_tax: total_tax})
              = 'Express Shipping'
          #express_shipping.right.shipping_option_price
            = express_shipping_price
        .row
          .delivery_message 
            = "#{delivery_message} #{delivery_time} days"
      #express_delievery_cannot_be_applied_for_wallet{style:"display:none;"}
        Express delivery can not be applied for Wallet.
