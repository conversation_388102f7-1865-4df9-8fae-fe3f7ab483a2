- if browser.opera? && browser.version.to_i <= 16
  - opera_browser = true
  -class_style = 'square_stage'
- else
  -class_style = 'circle_stage'
#order-status-ack
  .text-center
    #order-ack-header
      %svg#Layer_1{space: "preserve", style: "enable-background:new 0 0 115.35 122.88", version: "1.1", viewbox: "0 0 115.35 122.88", x: "0px", xmlns: "http://www.w3.org/2000/svg", "xmlns:xlink" => "http://www.w3.org/1999/xlink", y: "0px"}
        %g
        %path{d: "M25.27,86.92c-1.81,0-3.26-1.46-3.26-3.26s1.47-3.26,3.26-3.26h21.49c1.81,0,3.26,1.46,3.26,3.26s-1.46,3.26-3.26,3.26 H25.27L25.27,86.92L25.27,86.92z M61.1,77.47c-0.96,0-1.78-0.82-1.78-1.82c0-0.96,0.82-1.78,1.78-1.78h4.65c0.04,0,0.14,0,0.18,0 c1.64,0.04,3.1,0.36,4.33,1.14c1.37,0.87,2.37,2.19,2.92,4.15c0,0.04,0,0.09,0.05,0.14l0.46,1.82h39.89c1,0,1.78,0.82,1.78,1.78 c0,0.18-0.05,0.36-0.09,0.55l-4.65,18.74c-0.18,0.82-0.91,1.37-1.73,1.37l0,0l-29.18,0c0.64,2.37,1.28,3.65,2.14,4.24 c1.05,0.68,2.87,0.73,5.93,0.68h0.04l0,0h20.61c1,0,1.78,0.82,1.78,1.78c0,1-0.82,1.78-1.78,1.78H87.81l0,0 c-3.79,0.04-6.11-0.05-7.98-1.28c-1.92-1.28-2.92-3.46-3.92-7.43l0,0L69.8,80.2c0-0.05,0-0.05-0.04-0.09 c-0.27-1-0.73-1.69-1.37-2.05c-0.64-0.41-1.5-0.59-2.51-0.59c-0.05,0-0.09,0-0.14,0H61.1L61.1,77.47L61.1,77.47z M103.09,114.13 c2.42,0,4.38,1.96,4.38,4.38s-1.96,4.38-4.38,4.38s-4.38-1.96-4.38-4.38S100.67,114.13,103.09,114.13L103.09,114.13L103.09,114.13z M83.89,114.13c2.42,0,4.38,1.96,4.38,4.38s-1.96,4.38-4.38,4.38c-2.42,0-4.38-1.96-4.38-4.38S81.48,114.13,83.89,114.13 L83.89,114.13L83.89,114.13z M25.27,33.58c-1.81,0-3.26-1.47-3.26-3.26c0-1.8,1.47-3.26,3.26-3.26h50.52 c1.81,0,3.26,1.46,3.26,3.26c0,1.8-1.46,3.26-3.26,3.26H25.27L25.27,33.58L25.27,33.58z M7.57,0h85.63c2.09,0,3.99,0.85,5.35,2.21 s2.21,3.26,2.21,5.35v59.98h-6.5V7.59c0-0.29-0.12-0.56-0.31-0.76c-0.2-0.19-0.47-0.31-0.76-0.31l0,0H7.57 c-0.29,0-0.56,0.12-0.76,0.31S6.51,7.3,6.51,7.59v98.67c0,0.29,0.12,0.56,0.31,0.76s0.46,0.31,0.76,0.31h55.05 c0.61,2.39,1.3,4.48,2.23,6.47H7.57c-2.09,0-3.99-0.85-5.35-2.21C0.85,110.24,0,108.34,0,106.25V7.57c0-2.09,0.85-4,2.21-5.36 S5.48,0,7.57,0L7.57,0L7.57,0z M25.27,60.25c-1.81,0-3.26-1.46-3.26-3.26s1.47-3.26,3.26-3.26h50.52c1.81,0,3.26,1.46,3.26,3.26 s-1.46,3.26-3.26,3.26H25.27L25.27,60.25L25.27,60.25z"}
      Order Status 
  - if (['new','pending','fraud'].include? @order.state)
    -stages = ['Order Placed-yellowgreen','Order Confirmed-#D4A025-order_confirmed','In Warehouse-white',"Quality Check-white",'Ready To Ship-white','Order Dispatched-white']
  - else
    -stages=@order.order_status(@all_line_items)
  -if is_mobile_view?
    .row
      .columns
        .row
          - if (['new','pending','fraud', 'cancel'].include? @order.state)
            -stages = ['Order Placed-yellowgreen','Order Confirmed-#D4A025-order_confirmed','In Warehouse-white',"Quality Check-white",'Ready To Ship-white','Order Dispatched-white']
          - else
            -stages=@order.order_status(@all_line_items)
          .row#order_status
            -stages.each_with_index do |status,index|
              -values = status.split('-')
              #circle_div
                - if values[2].present?
                  - circle_width = 50
                  - margin_left = 30
                  - margin_top = 0
                - else
                  - circle_width = 25
                  - margin_left = 43
                  - if opera_browser
                    - margin_top = -8
                  - else
                    - margin_top = -13
                %span.span-24{class: "#{class_style}",style: "width:#{circle_width}px;height:#{circle_width}px;background-color:#{values[1]};margin-left: #{margin_left}px;"}
                  - if values[2].present?
                    %span
                      = image_tag("#{values[2]}.png", :alt => "#{values[0]}", :class => "step_image", :width => "36", :height => "36")
                  - else
                    .step_number=(index+1)
                -unless stages.count == index+1
                  %span.vertical_line{style:"background-color:#{values[1] == '#D4A025' ? 'white' : values[1]};margin-top: #{margin_top}px;"}
              .row#notes_div
                - color = (cookies[:theme] == 'black_theme') ? values[1] :  (values[1] == 'white' ? '#383737' : values[1])
                .span.status_note{style: "color: #{color};"}
                  %b=values[0]
                  %br
                  -if values[1] == 'yellowgreen'
                    done
                  -elsif values[1] == 'white'
                    pending
                  -else
                    processing
  - else
    #bar-progress.mt-5.mt-lg-0
    .progress-bar-container
      .steps-wrapper
        - stages.each_with_index do |status, index|
          - values = status.split('-') 
          .step
            %span.number-container
              %span.circle{class: "#{values[1]}"}
                - if values[2].present?
                  = image_tag("#{values[2]}.png", :alt => "#{values[0].truncate(13)}", :class => "step_image", :width => "36", :height => "36")
                - else
                  %span.step-number= index + 1
            %span.step-label= values[0]
          - unless index == stages.size - 1
            %span.vertical-line
