= javascript_include_tag 'int_payment_options'
= javascript_include_tag 'accordian'
= javascript_include_tag 'ga_payment_info'
- options = Design.country_code != 'IN' ? Order::INT_PAYMENT_OPTIONS : Order::DOM_PAYMENT_OPTIONS
- international_billing = @billing_address.present? ? @billing_address.international? : @order.billing_country.downcase !='india'
- billing_pincode = @billing_address.present? ? @billing_address.pincode : @order.billing_pincode
- grandtotal_with_prepaid_discount = get_price_with_symbol(get_price_in_currency(grandtotal) - prepaid_discount, @symbol)
- shipping_val = get_price_with_symbol(shipping, @symbol)
- grandtotal_without_shipping = get_price_with_symbol(get_price_in_currency(grandtotal) - shipping - prepaid_discount, @symbol)

= f.text_field :pay_type, type: "hidden", label: false, id: 'pay_type'
.row.dom-payments
  %dl.accordion{"data-accordion": ""}
    -# - if @saved_cards.present?
    -#   %dd.accordion-navigation{class: "saved_card", data: {active: "SAVED_CARD", prepaid_shipping_promo: prepaid_shipping_promo, shipping: shipping, grandtotal: grandtotal, prepaid_discount: prepaid_discount, mastercard_discount: mastercard_discount, symbol: @symbol}}
    -#     %a{href: "#saved_cards"} Saved Cards
    -#     .payment-error
    -#     %div.content{id: "saved_cards"}
    -#       = render partial: "orders/payments/saved_cards" 
    -country = @billing_address.present? ? @billing_address.country : order.billing_country
    - options.keys.each do |val|
      - pay_type = val.parameterize.underscore
      - id = 'order_pay_type_' + pay_type
      - if !international_billing && val == Order::PAYPAL && PAYPAL_SMARTPAY_SWITCH == 0
        - display_paytype = false
      - elsif international_billing && [Order::CBD, Order::PAYU_MONEY].include?(val)
        - display_paytype = false
      - elsif val == Order::PAYTM && (PAY_WITH_PAYTM_DISABLED  || international_billing)
        - display_paytype = false
      - elsif val == Order::COD && ((!@cart.cod?(@cod_charges_inr,country,billing_pincode,@rate) || !@cart.allowed_cod_country(Country.country_name_by_code[@country_code.upcase].downcase)) || (@billing_address != @shipping_address))
        - display_paytype = false
      - elsif val == Order::CBD && !@cart.cbd?(billing_pincode)
        - display_paytype = false
      - elsif val == Order::BANK_DEPOSIT && (international_billing || Design.country_code != 'IN') && cookies[:payment_failure_count].to_i < 2
        - display_paytype = false
      - elsif val == Order::PAYPAL_CREDIT && Design.country_code != 'US' && Design.country_code != 'AU' && Design.country_code != 'GB'
        - display_paytype = false
      - else
        - display_paytype = true
      - if display_paytype
        - if is_mobile_view?
          %dd.accordion-navigation.payment_options{class: "#{pay_type}", data: {active: "#{id}", pay_type: "#{pay_type}", prepaid_shipping_promo: prepaid_shipping_promo, shipping: shipping, grandtotal: grandtotal, prepaid_discount: prepaid_discount, symbol: get_symbol_from(@hex_symbol)}}
            - if val == 'Pay Online'
              %a{class:  "payment-text-block", href: "##{id}"} #{val}
              .payment-error
              %div.content{id: "#{id}"}
                = render partial: 'orders/payments/paypal_smartpay_box'
            - else
              %input.payment_options.inline-options.payment.right{ id: "#{id}", type: "radio", :pay_type => val,:name => "order[pay_type]" ,:class => "payment-radio-options", value: "#{val}", data: {pay_type: "#{pay_type}",shipping: shipping_val, grandtotal: get_price_with_symbol(grandtotal, @hex_symbol), prepaid_shipping_promo: prepaid_shipping_promo, grandtotal_without_shipping: grandtotal_without_shipping,grandtotal_with_prepaid_discount: grandtotal_with_prepaid_discount, prepaid_promotion: prepaid_promotion, domestic: domestic}}
              %label.payment-option{for: "#{id}"}
                #{val}
        - else
          .payment_options{class: "#{pay_type}", data: {active: "#{id}", pay_type: "#{pay_type}", prepaid_shipping_promo: prepaid_shipping_promo, shipping: shipping, grandtotal: grandtotal, prepaid_discount: prepaid_discount, symbol: get_symbol_from(@hex_symbol)}}
            - if val == 'Pay Online'
              .payment-error
              %div.content{id: "#{id}"}
                = render partial: 'orders/payments/paypal_smartpay_box'
            - else
              %input.payment_options.inline-options.payment.right{ id: "#{id}", type: "radio", :pay_type => val,:name => "order[pay_type]" ,:class => "payment-radio-options", value: "#{val}", data: {pay_type: "#{pay_type}",shipping: shipping_val, grandtotal: get_price_with_symbol(grandtotal, @hex_symbol), prepaid_shipping_promo: prepaid_shipping_promo, grandtotal_without_shipping: grandtotal_without_shipping,grandtotal_with_prepaid_discount: grandtotal_with_prepaid_discount, prepaid_promotion: prepaid_promotion, domestic: domestic}}
              %label.payment-option{for: "#{id}"}
                #{val}

            -# - if opt[:gateway_name] == "Cash On Delivery"
            -#   %label{:style => "font-size:12px;padding-left:10.4px;color:gray;line-height:1.0;background-color: #f4f4f4;padding-bottom: 5px;padding-right: 40px;"} Safety Tip: Avoid cash on delivery. Use any of the above payment methods
:javascript
  $(document).ready(function(){ 
  ACCORDIAN.methods.onChangeHandler()
  INT_PAYMENT_OPTIONS.methods.formSubmitHandler()
  })
