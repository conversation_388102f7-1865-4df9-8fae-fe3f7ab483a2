= javascript_include_tag 'payment'
%input#merchant-id{type: "hidden", value: ENV['JUSPAY_MERCHANT_ID']}
%input#card-token{type: "hidden", name: "card_token"}
%input#juspay-form{type: "hidden", name: "juspay_form", value: 1}
%input#card-error{type: "hidden", name: "card_error"}
%input#apiURL{type: "hidden", value: "#{ENV['JUSPAY_API_URL']}"}
= f.text_field :pay_type, type: "hidden", label: false, id: 'pay_type'
.row.dom-payments
  .dom-payment-error
  %dl.accordion{"data-accordion": ""}
    -country = @billing_address.present? ? @billing_address.country : order.billing_country
    -pincode= @billing_address.present? ? @billing_address.pincode : order.billing_pincode
    - if @saved_cards.present?
      %dd.accordion-navigation{class: "saved_card", data: {active: "SAVED_CARD", prepaid_shipping_promo: prepaid_shipping_promo, shipping: shipping, grandtotal: grandtotal, prepaid_discount: prepaid_discount, mastercard_discount: mastercard_discount, symbol: get_symbol_from(@hex_symbol)}}
        %a{href: "#saved_cards"} Saved Cards
        .payment-error
        %div.content{id: "saved_cards"}
          = render partial: "orders/payments/saved_cards"
    - @juspay_payment_options[:payment_method_types].each do |opt|
      - data = {active: "#{opt[:gateway_name]}", prepaid_shipping_promo: prepaid_shipping_promo, shipping: shipping, grandtotal: grandtotal, prepaid_discount: prepaid_discount, mastercard_discount: mastercard_discount, symbol: get_symbol_from(@hex_symbol)}
      - unless opt[:gateway_name] == "Cash On Delivery" && (!@cart.cod?(@cod_charges_inr,country,pincode,@rate) || (@billing_address != @shipping_address))
        %dd.accordion-navigation{class: "#{opt[:gateway_name].downcase}", data: data}
          - if "#{opt[:type]}" == 'accordion'
            %a{class:  "payment-text-block", href: "##{opt[:gateway_name]}"} #{opt[:name]}
            - if PAYMENT_OFFERS.key?(opt[:name])
              %label{:class => "offer-payment"} #{PAYMENT_OFFERS[opt[:name]]["text"]}
            .payment-error
            %div.content{id: "#{opt[:gateway_name]}"}
              = render partial: "orders/payments/#{opt[:gateway_name].downcase}", locals: {payment_methods: opt[:payment_methods], data: data}
          - else
            %input.inline-options.payment.right{:id => "#{opt[:name].downcase.split.join()}",:type => "radio", :name => 'payment_option', :class => "payment-radio-options", data: {pay_type: "#{opt[:gateway_name]}", prepaid_shipping_promo: prepaid_shipping_promo,  shipping: shipping, grandtotal: grandtotal, prepaid_discount: prepaid_discount, mastercard_discount: mastercard_discount, symbol: get_symbol_from(@hex_symbol)},:value => "#{opt[:gateway_name]}"}
            %label.payment-option{for: "#{opt[:name].downcase.split.join()}"}
              #{opt[:name]}
            -# - if opt[:gateway_name] == "Cash On Delivery"
            -#   %label{:style => "font-size:12px;padding-left:10.4px;color:gray;line-height:1.0;background-color: #f4f4f4;padding-bottom: 5px;padding-right: 40px;"} Safety Tip: Avoid cash on delivery. Use any of the above payment methods
            - if PAYMENT_OFFERS.key?(opt[:name])
              %label{:class => "offer-payment"} #{PAYMENT_OFFERS[opt[:name]]["text"]}
:javascript
  window.onpageshow = function(event) {
    if (event.persisted) {
    window.location.reload();
  }
  };