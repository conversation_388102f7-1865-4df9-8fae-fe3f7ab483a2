- content_for :page_specific_css do
  = stylesheet_link_tag 'returns'
- content_for :page_specific_js do
  = javascript_include_tag 'returns'
.container
  .user_return_panel
    - if @orders.present?
      - @orders.each do |order|
        -next unless order.line_items.present?
        .row.panel_block
          .panel_heading.columns
            Order Number : 
            %b
              = "#{order.number}"
            %span.right
              -case order.state.downcase
              -when 'sane'
                .label.success= 'Confirmed'
              -when 'pending','new','confirmed'
                .label.alert= 'Pending'
              -when 'cancel'
                .label.warning= 'Canceled'
              -else
                .label.success= order.state.titleize
          -order.line_items.each do |item|
            - designer_order = item.designer_order
            - designer       = designer_order.designer
            - design         = item.design
            .panel_content.columns
              .design-details
                .design-image
                  = image_tag(item.display_image(:thumb),alt: design.title)
                .design-title{style: 'vertical-align: top'}
                  = link_to design.title, designer_design_path(designer, design)
                  %br
                  = "Price    : #{order.currency_code} #{item.snapshot_price_currency(order.currency_rate)}"
                  %br
                  = "Quantity : #{item.quantity}"
          %div.text-center.columns
            %br
            -if order.cancellable?
              %a.button.button.tiny.policy_button{href: '#', "data-reveal-id" => "cancel_order_#{order.number}"} Cancel Order
              .cancel_modal.reveal-modal{id: "cancel_order_#{order.number}","data-options" => "closeOnBackgroundClick:true", "aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", role: "dialog"}
                %a.close-reveal-modal.close_modal_btn.close_cross{"aria-label" => "Close", href: "#", role: "button"} &#215
                #cancellation_message Do You Really Want To Cancel This Order?
                %hr
                =link_to 'Yes',user_cancel_cod_order_path(@user,order),class: 'success button yes_button'
                %button.no_button.button.alert{"aria-label" => "Close",data: {id: order.number}} No
  
            -elsif order.returnable?
              %button.button.tiny.policy_button.return_order{id: "policy_button_#{order.number}","data-reveal-id" => "policy_#{order.number}"} Return
              .reveal-modal{data: {reveal:"", v_offset: "0"}, id: "policy_#{order.number}", style: 'background:#efeeee; padding: 0px;'}
                .modal-dialog.modal-sm-size
                  .modal-content{style: 'padding:4%;font-size: 12px;'}
                    %a{class:"close-reveal-modal button",'aria-label'=> 'Close', style: 'float: right; padding: 0px 10px; font-size: 35px; border-radius: 50%;'} &#215
                    .modal-header
                      %h6.modal-title{style:'text-decoration:underline;'} Return Policy
                    .modal-body
                      = render :partial => '/orders/return_policy', :locals => {:order => order}
                    .modal-footer{style: 'text-align:center;'}
                      - if order.domestic?
                        =link_to 'I Agree', user_order_return_items_path(current_account.try(:user),order_number: order.number),class: 'button tiny'
                      %a.button.tiny.close-reveal-modal.close-button{href: "#", role: "button"} Close
            = link_to 'Track Order', order_url(order.number), :target => '_blank',class: 'button success tiny track_order'
      .row
        %h5= paginate @orders
        %h6= link_to 'View Older Orders', orders_path(current_account.user,old: true)
    - else
      .no-order-heading
        %h3 No orders found
        %p Browse through our fabulous Ethnic products. Checkout our gorgeous products from exclusive designers. Shop now !
        #action_buttons.empty-cart-action-buttons
          = link_to 'CONTINUE SHOPPING',"/store/women" , class: 'add_cont_shop no-order-found'
