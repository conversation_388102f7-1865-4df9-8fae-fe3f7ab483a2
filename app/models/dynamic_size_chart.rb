
class DynamicSizeChart < ActiveRecord::Base
  include Imageable

  serialize :size_chart,Array
  belongs_to :category
  belongs_to :designer

  def to_h
    if size_chart.size > 1
      size_chart_hash = {
        image: outline(:large),
        heads: size_chart[0],
        Sizes: size_chart.drop(1).collect do |size|
          {
            size: size[0],
            columns: size.drop(1)
          }
        end
      }.compact
    end
  end

  def outline(style)
    if outline_file_name.present?
      "#{self.image_file_base_url}#{self.class.name.tableize}/"\
      "#{id}/#{image_file('outline', style)}"
    end
  end

end