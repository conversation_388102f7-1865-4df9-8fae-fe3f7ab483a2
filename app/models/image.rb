# == Schema Information
#
# Table name: images
#
#  id                        :integer          not null, primary key
#  kind                      :string(255)
#  design_id                 :integer
#  created_at                :datetime
#  updated_at                :datetime
#  photo_file_name           :string(255)
#  photo_content_type        :string(255)
#  photo_file_size           :integer
#  photo_updated_at          :datetime
#  photo_processing          :boolean
#  original_photo_processing :boolean          default(FALSE)
#

class Image < ActiveRecord::Base
  include Imageable
  belongs_to :unbxd_item

  default_scope { where('photo_file_name is NOT NULL') }
  scope :sort_by_master_image, -> { order("CASE WHEN kind = 'master' THEN 0 ELSE 1 END, id ASC") }

  # Styles in which images are stored
  IMAGE_STYLES = [:thumb, :small, :large, :original, :small_m, :small_mo , :large_m, :zoom, :thumb_m, :long_webp, :luxe_webp, :zoom_webp, :thumb_webp]

  # Mobile alternatives for existing styles
  MOBILE_IMAGE_STYLES = {thumb: :thumb, small: :small, small_m: :small_m, small_mo: :small_mo ,
    large: :large_m, original: :zoom, original_img: :original, large_desktop: :large, long: :long, long_webp: :long_webp, luxe_webp: :luxe_webp, zoom_webp: :zoom_webp, thumb_webp: :thumb_webp}

  # Build image url based on style
  #
  # == Parameters:
  # style::
  #   String
  #
  # == Returns:
  # String
  #
  def photo(style)
    url = "#{self.image_file_base_url}#{self.class.name.tableize}/"\
    "#{id}/#{image_file('photo', style)}"
    if style.to_s.include?('webp') && (img = self.photo_file_name).present?
      name = img.clone
      extenstion = name.match(/(\.jpe?g\z)|(\.png\z)|(\.gif\z)|(\.jfif\z)|(\.webp\z)/i).to_s
      url = url.gsub(extenstion, '.webp')
    end
    return url
  end
end
