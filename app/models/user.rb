# == Schema Information
#
# Table name: users
#
#  id              :integer          not null, primary key
#  email           :string(255)
#  first_name      :string(255)
#  last_name       :string(255)
#  image_url       :string(255)
#  birthday        :datetime
#  gender          :string(255)
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  visit_mail_sent :date
#  history         :text
#

class User < ActiveRecord::Base
  extend FriendlyId
  has_one :juspay_customer
  has_one :default_address, -> { where.not default: 0 }, class_name: 'Address'
  has_one :account, as: :accountable
  has_many :addresses
  has_many :carts
  has_many :stitching_measurements
  has_many :orders
  has_many :returns, through: :orders
  has_many :wishlists
  has_one :bank_detail
  has_many :payment_gateway_customers
  has_many :reviews
  belongs_to :wallet
  has_one :notification
  has_many :unordered_wishlist_designs, -> { where(wishlists: {wish: true, state: 'added'}).order('wishlists.updated_at desc') }, through: :wishlists, source: :design
  has_many :kyc_documents
  has_many :sent_gift_card_orders, foreign_key: :sender_user_id, class_name: 'GiftCardOrder'
  has_many :received_gift_card_orders, foreign_key: :recipient_user_id, class_name: 'GiftCardOrder'

  has_many :user_subscriptions, dependent: :destroy
  has_many :subscription_plans, through: :user_subscriptions
  has_one :active_subscription,
        -> { where(status: 'active').where('end_date IS NULL OR end_date >= ?', Date.current) },
        class_name: 'UserSubscription'
        
  include JuspayCustomerApi
  include RequestAccessor
  rattr_accessor :app_source, default: 'mobile'

  friendly_id :full_name, use: :slugged

  has_attached_file :full_size_photo, styles: {small: "225x257#", zoom: "800x1100"}
  validates_attachment_size :full_size_photo, less_than: 4.megabytes
  validates_attachment_content_type :full_size_photo, content_type: ['image/jpeg', 'image/png', 'image/jpg']

  acts_as_followable
  acts_as_follower
  DESIGN_ORDER_RETURN_DAYS = SystemConstant.get('DESIGN_ORDER_RETURN_DAYS').to_i
  # Provides complete name of user
  def full_name
    (name = "#{first_name} #{last_name}".strip).blank? ? self.email.split(/[@,.,_,\d]/)[0] : name
  end

  def designers_count
    self.following_by_type_count('Designer')
  end

  def users_count
    self.following_by_type_count('User')
  end

  #Provide the review of user
  def review_for(design_id)
    self.reviews.find_by_design_id(design_id)
  end

  def wishlisted_designs(wishlist_ids)
    if wishlist_ids.present?
      Design.for_ids_with_order(wishlist_ids).includes(:designer, :master_image,:categories,:variants)
    else
      unordered_wishlist_designs.includes(:designer, :master_image)
    end
  end

  def un_ordered_wishlist_ids(type, all)
    if type == 'wish'
      Wishlist.where(user_id: id).unordered.wished.select([:id, :design_id]).group_by(&:design_id)
    elsif type == 'like' && all
      Wishlist.where(user_id: id).unordered.where.not(like: nil).select([:id, :design_id, :like]).group_by(&:design_id)
    else
      Wishlist.where(user_id: id).unordered.liked.select([:id, :design_id]).group_by(&:design_id)
    end
  end

  def liked_designs
    liked_ids = Wishlist.where(user_id: id).unordered.liked.order('updated_at desc').pluck(:design_id).uniq
    Design.for_ids_with_order(liked_ids).includes(:designer, :master_image)
  end

  def assign_wallet(country_code=nil)
    if account.api_data.present? || account.present?
      currency_convert_id = country_code.present? ? CurrencyConvert.currency_convert_cache_by_country_code(country_code).id : account.api_data.currency_convert_id
      wallet = Wallet.create(currency_convert_id: currency_convert_id)
      update_attribute(:wallet_id, wallet.id)
      wallet
    end
  end

  def wallet_amounts
    self.wallet ||= assign_wallet
  end

  def can_review(design_id)
    if design_id.present? && design_id > 0
      orders.where(state: USER_CONFIRMED_ORDER_STATE).includes(designer_orders: :line_items)
            .where('line_items.design_id' => design_id, 'line_items.status' => [nil, ''], designer_orders: {state: ['pickup_done', 'pickedup', 'dispatched', 'completed']})
            .order('orders.created_at').last.try(:id).to_i
    else
      orders.exists?(state: USER_CONFIRMED_ORDER_STATE) ? 1 : 0
    end
  end

  def payu_hashes
    merchant_key = ENV.fetch('PAYU_MERCHANT_KEY')
    salt = ENV.fetch('PAYU_SALT')
    if (api_data = account.api_data).present? && ALLOWED_APP_VERSIONS[45..-1].include?(api_data.app_version)
      {
        payment_related_details_for_mobile_sdk: Digest::SHA2.new(512).hexdigest("#{merchant_key}|payment_related_details_for_mobile_sdk|#{merchant_key}:#{id}|#{salt}"),
        vas_for_mobile_sdk: Digest::SHA2.new(512).hexdigest("#{merchant_key}|vas_for_mobile_sdk|#{merchant_key}:#{id}|#{salt}"),
        delete_user_card: Digest::SHA2.new(512).hexdigest("#{merchant_key}|delete_user_card|#{merchant_key}:#{id}|#{salt}"),
      }
    else
      {
        payment_related_details_for_mobile_sdk: Digest::SHA2.new(512).hexdigest("#{merchant_key}|payment_related_details_for_mobile_sdk|default|#{salt}")
      }
    end
  end

  def reward_pending_gift_cards
    if received_gift_card_orders.where(state: 'sane').exists?
      self.wallet ||= Wallet.create(currency_convert_id: CurrencyConvert.currency_convert_memcached.find{|c| c.country_code == Design.country_code}.id)
      wallet.reward_pending_gift_cards
    end
  end

  def self.find_or_create_from_email(email)
    user = find_or_initialize_by(email: email)
    user.save! if user.new_record?
    user
  end

  include ForceDowncaseWriters.new(:email)
end
