# == Schema Information
#
# Table name: orders
#
#  id                              :integer          not null, primary key
#  pay_type                        :string(255)
#  buyer_id                        :integer
#  created_at                      :datetime
#  updated_at                      :datetime
#  total                           :integer
#  shipping                        :integer
#  state                           :string(255)
#  name                            :string(255)
#  email                           :string(255)
#  phone                           :string(255)
#  street                          :string(255)
#  city                            :string(255)
#  buyer_state                     :string(255)
#  country                         :string(255)
#  pincode                         :string(255)
#  number                          :string(255)
#  notes                           :text
#  billing_name                    :string(255)
#  billing_street                  :string(255)
#  billing_email                   :string(255)
#  billing_phone                   :string(255)
#  billing_city                    :string(255)
#  billing_state                   :string(255)
#  billing_country                 :string(255)
#  billing_pincode                 :string(255)
#  ccavenue_authdesc               :string(255)
#  ccavenue_nb_order_no            :string(255)
#  ccavenue_nb_bid                 :string(255)
#  ccavenue_card_category          :string(255)
#  ccavenue_bank_name              :string(255)
#  track                           :integer
#  visit_mail_sent                 :date
#  gharpay_order_id                :string(255)
#  gharpay_status                  :string(255)
#  discount                        :integer
#  coupon_id                       :integer
#  courier_company                 :string(255)
#  tracking_number                 :string(255)
#  confirmed_at                    :datetime
#  pickup                          :datetime
#  paid_amount                     :integer
#  credit_amount                   :integer
#  items_received_status           :boolean
#  items_received_on               :datetime
#  user_id                         :integer
#  payment_state                   :string(255)
#  state_code                      :string(255)
#  country_code                    :string(255)
#  utm_source                      :string(255)
#  utm_medium                      :string(255)
#  utm_campaign                    :string(255)
#  referral_url                    :string(255)
#  utm_term                        :string(255)
#  utm_content                     :string(255)
#  paypal_payer_id                 :string(255)
#  paypal_txn_id                   :string(255)
#  paypal_payment_type             :string(255)
#  paypal_ipn_track_id             :string(255)
#  paypal_mc_gross                 :string(255)
#  paypal_mc_currency              :string(255)
#  paypal_num_cart_items           :string(255)
#  fedex_shipment_error            :string(255)
#  cod_charge                      :integer
#  zipdial_transaction_token       :string(255)
#  zipdial_verify_image            :string(255)
#  zipdial_number                  :string(255)
#  cod_verify_msg_count            :integer          default(0)
#  mirraw_addon_charges            :integer          default(0)
#  pending_at                      :datetime
#  ready_for_dispatch_at           :datetime
#  cancel_mail_sent_at             :datetime
#  cancel_mail_sent_count          :integer          default(0)
#  ccavenue_order_status           :string(255)
#  ccavenue_failure_message        :string(255)
#  ccavenue_payment_mode           :string(255)
#  ccavenue_bank_status_code       :string(255)
#  ccavenue_bank_status_message    :string(255)
#  ccavenue_customer_identifier    :string(255)
#  ccavenue_currency               :string(255)
#  ccavenue_merchant_data          :text
#  cart_id                         :integer
#  ccavenue_payment_link           :string(255)
#  ccavenue_api_error              :string(255)
#  ccavenue_invoice_id             :string(255)
#  ccavenue_payment_link_qr_code   :text
#  ccavenue_payment_link_valid_for :datetime
#  ccavenue_payment_link_status    :string(255)
#  payu_mihpayid                   :string(255)
#  payu_payment_category_mode      :string(255)
#  payu_status                     :string(255)
#  payu_error                      :string(255)
#  payu_bankcode                   :string(255)
#  payu_bank_ref                   :string(255)
#  payu_unmapped_status            :string(255)
#  payu_error_message              :string(255)
#  payu_name_on_card               :string(255)
#  payu_card_num                   :string(255)
#  payu_payment_issuing_bank       :string(255)
#  payu_card_type                  :string(255)
#  payment_gateway                 :string(255)
#  attempted_payment_gateway       :string(255)
#  ip_address                      :string(255)
#  currency_rate                   :float
#  currency_code                   :string(255)
#  paid_currency_rate              :integer
#  paid_currency_code              :string(255)
#  transaction_fee                 :integer
#  transaction_fee_currency_code   :string(255)
#  amount_sent_payment_gateway     :float
#

class Order < ActiveRecord::Base
  has_paper_trail
  acts_as_taggable
  acts_as_paranoid

  # To Show per page only 10 records
  paginates_per 10
  # Types of Payments & their string representation
  COD = 'Cash On Delivery'
  CBD = 'Cash Before Delivery'
  PAYPAL = 'PayPal'
  PAYU_MONEY = 'Payu Money'
  PAYU_GPAY = 'Payu Gpay'
  BANK_DEPOSIT = 'Bank Deposit'
  PAYPAL_CREDIT = 'PayPal Paylater'
  PAYPAL_SMART_PAY = 'paypal_smartpay'
  PAYMENT_GATEWAY = 'Credit/Debit Card/Net Banking'
  PAYPAL_V2 = 'paypal_v2'
  PAY_WITH_AMAZON = 'Pay with Amazon'
  PAYTM = 'Paytm'
  G2A_HASH = Digest::SHA256.new.hexdigest([Rails.application.config.g2a[:api_hash],Rails.application.config.g2a[:merchant_email],Rails.application.config.g2a[:api_secret_key]].join)
  PAYPAL_ALLOWED_CURRENCIES = ['AUD', 'CAD', 'CZK', 'DKK', 'EUR', 'HKD', 'ILS', 'MXN', 'NOK', 'NZD', 'PHP', 'PLN', 'GBP', 'SGD', 'SEK', 'CHF', 'THB', 'USD', 'INR'] + PAYPAL_PRESENTMENT_CURRENCIES
  STRIPE_ALLOWED_CURRENCIES = [
    'usd', 'aed', 'afn', 'all', 'amd', 'ang', 'aoa', 'ars', 'aud', 'awg', 'azn',
    'bam', 'bbd', 'bdt', 'bgn', 'bif', 'bmd', 'bnd', 'bob', 'brl', 'bsd', 'bwp',
    'bzd', 'cad', 'cdf', 'chf', 'clp', 'cny', 'cop', 'crc', 'cve', 'czk', 'djf',
    'dkk', 'dop', 'dzd', 'egp', 'etb', 'eur', 'fjd', 'fkp', 'gbp', 'gel', 'gip',
    'gmd', 'gnf', 'gtq', 'gyd', 'hkd', 'hnl', 'hrk', 'htg', 'huf', 'idr', 'ils',
    'inr', 'isk', 'jmd', 'jpy', 'kes', 'kgs', 'khr', 'kmf', 'krw', 'kyd', 'kzt',
    'lak', 'lbp', 'lkr', 'lrd', 'lsl', 'mad', 'mdl', 'mga', 'mkd', 'mmk', 'mnt',
    'mop', 'mro', 'mur', 'mvr', 'mwk', 'mxn', 'myr', 'mzn', 'nad', 'ngn', 'nio',
    'nok', 'npr', 'nzd', 'pab', 'pen', 'pgk', 'php', 'pkr', 'pln', 'pyg', 'qar',
    'ron', 'rsd', 'rub', 'rwf', 'sar', 'sbd', 'scr', 'sek', 'sgd', 'shp', 'sll',
    'sos', 'srd', 'std', 'szl', 'thb', 'tjs', 'top', 'try', 'ttd', 'twd', 'tzs',
    'uah', 'ugx', 'uyu', 'uzs', 'vnd', 'vuv', 'wst', 'xaf', 'xcd', 'xof', 'xpf',
    'yer', 'zar', 'zmw', 'eek', 'lvl', 'svc', 'vef', 'ltl'
  ]

  COUNTRY_DIAL_CODE = Country.pluck(:name,:dial_code).to_h

  WALLET = WALLET_PAYMENT

  NON_PRIORITY_COUNTRIES = Country.where('priority IS NULL').order('name ASC').pluck(:name)
  PRIORITY_COUNTRIES = Country.where('priority IS NOT NULL').order('priority ASC').pluck(:name)
  JP_WALLET = 'WALLET'
  JP_NETBANKING = 'NET_BANKING'
  JP_UPI = 'UPI'
  JP_CARD = 'CARD'
  JP_PAYPAL = 'PAYPAL'
  JP_SAVED_CARD = 'SAVED_CARD'
  # Remove payu money option temporary: PAYU_MONEY
  # Remove GHARPAY opriont temporary : GHARPAY

  PAYMENT_OPTIONS = SystemConstant.get_config('payment_option')
  INT_PAYMENT_OPTIONS = PAYMENT_OPTIONS.select{|option, values| values['int_enable'] }.sort_by{|k,v| v['int_position']}.to_h
  DOM_PAYMENT_OPTIONS = PAYMENT_OPTIONS.select{|option, values| values['dom_enable'] }.sort_by{|k,v| v['dom_position']}.to_h

  COD_DISCOUNT_STATES = ['new', 'pending', 'confirmed', 'sane']
  COD_CHARGE = COD_CHARGE.to_i
  DELAYED_JOB_QUEUE = "mobile-#{self.name}"

  EU_COUNTRIES = Country.where{time_zone =~ '%europe%'}.pluck('lower(name)')
  EU_COUNTRY_CODE = Country.where{time_zone =~ '%europe%'}.pluck('lower(iso3166_alpha2)')
  PRIORITY_WISE_COUNTRIES = Country.order(:priority).order(:name).pluck(:name)

  # Attributes that require currency conversion
  # Needs to always be above Priceable
  PRICE_ATTR = [:discount, :cod_charge, :shipping, :additional_discount, :express_delivery, :total_tax, :subscription_fee, :platform_fee]
  EXPIRY_DATE = Date.parse(REFERRALS_EXPIRY)

  serialize :order_notification, Hash

  include Priceable
  include PaypalApi
  include PayuUtilities
  include PaypalV2Utilities
  include PaypalCreditCard
  include CouponValidation
  include PaytmUtilities
  include JuspayApi
  extend PaypalExpress
  include Taxes

  #set true for cod_otp validation
  attr_accessor :validate_cod_otp, :skip_filter, :paypal_retry_count, :billing_first_name, :billing_last_name, :first_name, :last_name

  validates_acceptance_of :newsletter_checkbox

  has_one :transaction_order
  has_one :payment_gateway_transaction 
  has_one :gokwik_data
  has_many :shipments, -> { where(service_type: nil) }
  has_many :export_shipments, -> { where(designer_order_id: nil, service_type: nil) }, class_name: 'Shipment'
  has_many :reverse_shipments, -> { where(designer_order_id: nil, service_type: 'ReverseShipment') }, class_name: 'Shipment'
  has_many :designer_orders, dependent: :destroy
  has_many :line_items, :dependent => :destroy, :through => :designer_orders
  has_many :returns
  has_many :events, as: :eventable
  has_many :return_designer_orders, :through => :returns
  has_many :designs,:through => :line_items
  has_many :wallet_transactions
  has_many :order_discounts
  has_one :delivery_nps_info,as: :element

  belongs_to :coupon
  belongs_to :user
  belongs_to :cart
  belongs_to :stylist
  has_many :survey_answers, :as => :surveyable
  has_and_belongs_to_many :reviews
  has_one :order_addon
  validates :number, presence: true
  validates :pay_type, presence: true
  validates :designer_orders, presence: true

  validates :billing_name, presence: true, on: :create
  validates :billing_email, presence: true, format: { with: /\A[^@\s]+@([^@\s]+\.)+[^@\s]+\z/}, on: :create
  validates :billing_pincode, presence: true
  validates :billing_street, presence: true, length: {maximum: 255}, on: :create
  validates :billing_city, presence: true
  validates :billing_state, presence: true
  validates :billing_country, presence: true
  validates :billing_phone, :presence => {:message => "Billing Details: Please specify a Phone Number"}, :on => :create
  validate :otp_verified_cod_order?, if: :validate_cod_otp

  validates :name, presence: true
  validates :pincode, presence: true
  validates :phone, :presence => {:message => "Shipping Details: Please specify a Phone Number"}
  validates :street, presence: true, length: {maximum: 255}
  validates :city, presence: true
  validates :buyer_state, presence: true
  validates :country, presence: true
  validate :validate_phone_number, if: :new_record?
  scope :non_zero_orders, -> { select{ |order| order.item_total(1) > 0 } }

  before_save :update_triggers, unless: :skip_filter
  after_save :post_save_triggers, unless: :skip_filter
  after_create :update_design_quantity, :assign_gokwik_data
  after_create lambda { |order| broadcast(:order_created, order.id) }

  # (1..2) signifies total number of address lines for each street address
  def assign_warehouse_addresses_to_designer_orders
    self.designer_orders.each do |dos|
      dos.set_warehouse_address if dos.warehouse_address_id.blank?
    end
  end

  def self.street_address_lines
    @street_address_lines ||= (1..3).collect do |index|
      %w(billing_street street).collect do |st_address|
        "#{st_address}_line_#{index}"
      end
    end.flatten
  end

  def update_triggers
    self.set_totals
    if new_record?
      if Wallet.cashback_percent > 0 && (Design.country_code != 'IN' || prepaid?)
        self.other_details['loyalty_rewards_credit'] = Wallet.cashback_for_amount(total)
      end
      if self.icn_term.to_s.match('share_and_earn_user_') && self.actual_country_code == 'IN' && SHARE_AND_EARN_REWARD > 0
        self.other_details['share_and_earn_credit'] = SHARE_AND_EARN_REWARD
        self.other_details['share_and_earn_referrer'] = self.icn_term.split('share_and_earn_user_')[1]
      end
    end
    true
  end

  def set_totals
    apply_subscription_discount_on_items
    build_totals
    cod_updates if self.cod? && COD_DISCOUNT_STATES.include?(self.state)
  end


  def update_design_quantity
    Order.sidekiq_delay(queue: 'critical').decrease_design_quantity(self.id)
    Order.sidekiq_delay(queue: 'critical').update_order_related_details(self.id)
  end

  def assign_gokwik_data
    if self.cart.present? && (gd = self.cart.gokwik_data).present?
      gd.update_attribute(:order_id, self.id)
    end
  end

  def post_save_triggers
    if self.new? && self.created_at == self.updated_at
      Order.sidekiq_delay.post_new_order(self.id)
      unless [PAYU_GPAY, PAYMENT_GATEWAY, PAYPAL, WALLET_PAYMENT, PAYTM, JP_NETBANKING, JP_WALLET, JP_UPI, JP_CARD, JP_SAVED_CARD, JP_PAYPAL, PAYPAL_CREDIT, PAYPAL_V2].include?(self.pay_type)
        self.cart.update_attribute(:used, true)
        #self.wallet_deductions(self.cart)
      end

      # self.designer_orders.each do |designer_order|
      #   designer_order.line_items.each do |item|
      #     wishlist = item.design.wishlist_for_user(self.user_id)
      #     wishlist.update_columns(state: 'ordered') if wishlist.present?
      #   end
      # end
      design_ids = self.designer_orders.collect do |designer_order|
        designer_order.line_items.collect(&:design_id)
      end.flatten.compact
      Wishlist.unordered.where(design_id: design_ids,user_id: user_id).update_all(state: 'ordered') if design_ids.present? && user_id.present?

      #Order.delay(priority: 2).find_and_delete_previous_related_orders(id)
    end
    if self.plus_size? && self.tag_list.exclude?('Plus Size')
      self.tag_list.add('Plus Size')
      self.skip_filter = true
      self.save
    end
    if (coupon = self.coupon) && coupon.limit == 1
      coupon.update_column(:coupon_used_on_id, self.id)
    end
    if (Time.current < EXPIRY_DATE) && REFERRAL_TYPES.include?('first_new_order') && (user = self.user).present? && user.referral && (user.orders.pluck(:cart_id).uniq.length == 1) && (referral = ReferredPerson.find_by_email(user.email).referral).present?
      referral.update_attribute(:order_id, self.id) unless referral.credited
    end
  end

  def validate_phone_number
    {phone: :country, billing_phone: :billing_country}.each do |number, country|
      phone = public_send(number)
      country = public_send(country)
      if !phone.blank? && !country.blank?
        if country.downcase == "india" && phone.length < 10
          errors.add("#{number}", "Please specify 10 digit Phone Number")
        elsif country.downcase != "india" && phone.length < 8
          errors.add("#{number}", "Please specify a Correct Phone Number(too short)")
        elsif country.downcase != "india" && phone.length > 20
          errors.add("#{number}", "Please specify a Correct Phone Number(too long)")
        end
      end
    end
  end


  def set_subscription_details(subscription_params, rate, country_code)
    if (subscription_params[:subscription_applied].to_s == 'true' && subscription_params[:subscription_plan_id].present?)
      self.subscription_fee =  (SubscriptionPlan.for_id(subscription_params[:subscription_plan_id])["price"] * rate).round(2)
      self.other_details['subscription_plan_id'] = subscription_params[:subscription_plan_id]
    elsif self.user.present? && self.user.active_subscription.blank?
      self.platform_fee = (self.cart.get_platform_fee(country_code, subscription_params[:app_source] || 'mobile', rate) * rate).round(2)
    end
  end

  # Method to be run in delayed job only
  # Dummy method actual method with main mirraw
  # Required to be kept in current state! Do not be smart
  #
  def self.cancel_orders(ids)
  end

  def self.decrease_design_quantity(id)
  end

  def self.update_order_related_details(id)
  end

  def self.cod_charge_currency(rate)
    (COD_CHARGE / rate).round(2)
  end

  # def platform_fee_currency(rate)
  #   (self.platform_fee / rate).round(2)
  # end

  # def subscription_fee_currency(rate)
  #   (self.subscription_fee / rate).round(2)
  # end
  # Provides total value of all designer orders
  #
  # == Returns:
  # Integer
  #
  def designer_orders_total
    self.total + self.discount.to_i - (
      self.cod_charge.to_i + self.shipping.to_i +
      self.mirraw_addon_charges)
  end

  # Percentage value of discount offered over designer order total
  #
  # == Returns:
  # Integer
  #
  def discount_percent
    self.discount.to_i / self.designer_orders_total.to_f
  end

  # Proportionate value of discount offered over total discount
  #
  # == Returns:
  # Integer
  #
  def discount_for(value)
    value * self.discount_percent
  end

  # Checks whether order pay type is COD
  #
  # == Returns:
  # Boolean
  #
  def cod?
    self.pay_type == COD
  end

  def eligible_for_automated_refund?
    AUTOMATED_COD_REFUND['enable'] && self.cod? && self.domestic?
  end

  def cancel_unless_confirmed?
    !(new_record? || cod? || bank_deposit? || total.to_i <= 0)
  end

  def add_log_details(error)
    log={}
    log["time"]=DateTime.now
    log["status"]=error.response.ack
    log["error"]=error.response.raw[:L_LONGMESSAGE0]
    self.update_column(:paypal_error,log.to_json)
  end

# Delivery Time ->>> Expected delivery time
  def expected_delivery_date
    return delivery_nps_info.promised_delivery_date.to_date if confirmed_at.present? && delivery_nps_info.present?
  end

  # dial2verify api
  def send_cod_verification(notify_host_url)
    if self.get_cod_token_increment_verification_count(notify_host_url)
      # self.sidekiq_delay_until(Time.now.advance(:minutes => 10)).send_zipdial_verify_sms
      Order.sidekiq_delay_until(Time.now.advance(:minutes => 10)).enqueue_job("Order", self.id, "send_zipdial_verify_sms")
      # self.sidekiq_delay_until(Time.now.advance(:minutes => 90)).clear_zipdial_details
      Order.sidekiq_delay_until(Time.now.advance(:minutes => 90)).enqueue_job("Order", self.id, "clear_zipdial_details")
      # self.sidekiq_delay_until(Time.now.advance(:hours => 5)).resend_verification_token_last_time_not_confirmed
      Order.sidekiq_delay_until(Time.now.advance(:hours => 5)).enqueue_job("Order", self.id, "resend_verification_token_last_time_not_confirmed")
    elsif self.send_cod_verification_email_increment_verification_count
      # self.sidekiq_delay_until(Time.now.advance(:days => 1)).cancel_unless_confirmed
      Order.sidekiq_delay_until(Time.now.advance(:days => 1)).enqueue_job("Order", self.id, "cancel_unless_confirmed")
    end
  end

  def resend_verification_token_last_time_not_confirmed
    if self.pending?
      self.get_cod_token_increment_verification_count
      # self.sidekiq_delay.send_zipdial_verify_sms
      Order.sidekiq_delay.enqueue_job("Order", self.id, "send_zipdial_verify_sms")
      # self.sidekiq_delay_until(Time.now.advance(:minutes => 90)).clear_zipdial_details
      Order.sidekiq_delay_until(Time.now.advance(:minutes => 90)).enqueue_job("Order", self.id, "clear_zipdial_details")
      # self.sidekiq_delay_until(Time.now.advance(:minutes => 90)).cancel_unless_confirmed
      Order.sidekiq_delay_until(Time.now.advance(:minutes => 90)).enqueue_job("Order", self.id, "cancel_unless_confirmed")
    end
  end

  # this method will send cod confirmation email after the dial2verify api response
  def send_cod_verification_email_increment_verification_count
    # OrderMailer.sidekiq_delay.request_cod_confirmation(self)
    Order.sidekiq_delay.enqueue_job("OrderMailer", nil, "request_cod_confirmation", {self.class.to_s => self.id})
    self.cod_verify_msg_count += 1
    self.save!
  end

  def self.convey_status_update_to_designer(id)
  end

  def get_cod_token_increment_verification_count(notify_host_url)
    if self.get_cod_token(notify_host_url)
      self.cod_verify_msg_count += 1
      self.save!
    else
      false
    end
  end

  # def get_cod_token
  def get_cod_token(notify_host_url)
    # return false

    z2v_token = Rails.application.config.dial2verify_token
    phone = self.get_mobile_num(self.phone)
    if phone && phone != 0 && phone.length == 12
      cod_api_params = DIAL2VERIFY_COD_API.sub('{phone}', phone).sub('{z2v_token}', z2v_token).sub('{notify_host_url}', notify_host_url)
      res = HTTParty.get(cod_api_params)
      res_hash = JSON.parse(res.body)
      if res_hash.present? && res_hash['APIStatus'] == 'Success'
        self.zipdial_transaction_token = res_hash['SessionId']
        self.zipdial_verify_image = res_hash['ImageUrl']
        self.zipdial_number = res_hash['VerificationNode']
        return true
      end
    else
      return false
    end
  end

  def get_mobile_num(phone_num)
    phone = phone_num.gsub(' ', '').gsub('+','').gsub('-','')
    if phone.length >= 10 && phone.length <= 12
      if phone.length == 11 && phone[0] == '0'
        phone = phone[1..-1]
      elsif phone.length == 12 && phone[0..1] == '91'
        phone = phone[2..-1]
      end
      if phone.length == 10
        phone = "91" + phone
      end
    else
      return 0
    end
  end

  def get_total_values_for_return
    order_discount, order_total, order_total_only_bmgn_products = (self.discount.to_i + self.additional_discount.to_i + (self.referral_discount.to_f * self.currency_rate.to_f)), 0, 0
    if self.discount.to_i > 0 && (coupon = self.coupon).present? && coupon.coupon_type == 'COFF'
      order_discount = order_discount - self.discount.to_i
    end
    if order_discount > 0
      self.line_items.each do |li|
        current_line_item_total = (li.snapshot_price * li.quantity).to_f
        order_total += current_line_item_total
        order_total_only_bmgn_products += current_line_item_total if li.buy_get_free == 1
      end
    end
    return order_total,order_discount,order_total_only_bmgn_products
  end

  def clear_zipdial_details
    if self.state == 'pending'
      self.zipdial_transaction_token = nil
      self.zipdial_verify_image = nil
      self.zipdial_number = nil
      self.save!
    end
  end

  def send_zipdial_verify_sms
    if self.pending? && Rails.env.production?
      phone = self.get_mobile_num(self.phone)
      if phone && phone != 0 && phone.length == 12 && self.zipdial_number.present?
        template = "Hi, We have received your COD order of amount Rs. #{self.total}. Please give us a missed call on #{self.zipdial_number} within the next 1 hour to confirm your order. Thanks, Mirraw.com"
        template = URI.encode(template)
        sms_api_params = SMS_API.sub('{phone}', phone).sub('{template}', template)
        res = HTTParty.get(sms_api_params)
      end
    end
  end

  # Checks whether order pay type is CBD
  #
  # == Returns:
  # Boolean
  #
  def cbd?
    self.pay_type == CBD
  end

  # Checks whether order pay type is Bank Deposit
  #
  # == Returns:
  # Boolean
  #
  def bank_deposit?
    self.pay_type == BANK_DEPOSIT
  end

  # Checks whether order pay type is Payment Gateway
  #
  # == Returns:
  # Boolean
  #
  def payment_gateway?
    self.pay_type == PAYMENT_GATEWAY
  end

  # Checks whether order state is cancel
  #
  # == Returns:
  # Boolean
  #
  def cancel?
    self.state == 'cancel'
  end

  # Check whether order is domestic or international
  #
  # == Returns:
  # Boolean
  #
  def domestic?
    self.country.downcase == 'india'
  end

  # Check whether order is international
  #
  # == Returns:
  # Boolean
  #
  def international?
    !domestic?
  end

  # Determines weather order is to be paid with payu
  #
  # == Returns:
  # Boolean
  #
  def payu_paid?
    !international? && self.payment_gateway?
  end
  #  capture razor pay payment details
  def self.process_razorpayment(order_id, razorpay_payment_id)
    #actual method in desktop
  end

  def get_subscription_plan
    if self.other_details['subscription_plan_id'].present?
      SubscriptionPlan.for_id(self.other_details['subscription_plan_id'])
    end
  end
  # Cost of all LineItems
  #
  # == Parameters:
  #   rate::
  #     Integer
  #
  # == Returns:
  # Integer
  #
  def item_total(rate)
    amount = 0
    self.designer_orders.each do |designer_order|
      designer_order.line_items.each do |item|
        amount += item.total_currency(rate)
      end
    end
    amount.round(CurrencyConvert.round_to)
  end

  def item_total_without_addons(rate)
    (self.designer_orders.flatten.sum {|designer_order| designer_order.line_items.flatten.sum{ |item| item.total_currency_without_addons(rate)}}).round(CurrencyConvert.round_to)
  end

  def item_total_with_subscription_fee(rate)
    item_total_without_addons(rate) + subscription_fee_currency(rate)
  end
  def addons_total(rate)
    (self.designer_orders.flatten.sum {|designer_order| designer_order.line_items.flatten.sum{ |item| item.addons_total(rate)}}).round(CurrencyConvert.round_to)
  end

  def plus_size?
    self.line_items.any?{|li| li.is_product_plus_size?}
  end

  def free_shipping_available?
    if cart.present?
      status = cart.free_shipping_available?(currency_rate, country)
      self.other_details['free_shipping'] = status
      return status
    else
      self.other_details['free_shipping'] || self.other_details['free_shipping'] == "true"
    end
  end

  # Provides shipping cost
  #
  # == Parameters:
  #   weight::
  #     Integer
  #
  # == Returns:
  # Integer
  #
  def shipping_cost(weight, final_price = nil)
    final_price = CurrencyConvert.to_currency(self.actual_country_code, final_price ).round(2)
    if (self.billing_country == 'India' && (self.pay_type.downcase != 'cod' && self.pay_type.downcase != 'cash on delivery' && self.pay_type.downcase != 'mirraw wallet') && DOMESTIC_PREPAID_SHIPPING_PROMOTION) && ((['mobile','desktop'].include?(self.app_source.to_s.downcase))  || verify_shipping_promo_version_for_app?)
      # This shipping promotion is dependent on the actual_country_code in case of mobile
      # And effective based on the billing_country in case of app
      if (['mobile','desktop'].include?(self.app_source.to_s.downcase) && self.actual_country_code == 'IN') || !['mobile','desktop'].include?(self.app_source.to_s.downcase)
        return 0
      end
    end
    if self.country == 'India' && final_price.present?
       essential_shipping_charges, essential_total, all_essential = Order.get_essential_shipping(self.designer_orders.collect(&:line_items).flatten)
       if all_essential
        return essential_shipping_charges
      else
        return (DOMESTIC_SHIPPING_CHARGES.find {|k,v| (final_price - essential_total ) < k.to_i}.try(:last) || 0).to_f + essential_shipping_charges
      end
    end
    if !weight.present? || weight == 0
      weight = get_weight
    end
    value = 0
    if self.international?
      if (coupon.present? && coupon.is_shipping? && (total_currency(1) + (refund_discount.to_f)*(currency_rate || 1)) >= coupon.min_amount)
        value = 0 
      elsif self.free_shipping_available?
        if shipping_categories_available? || cart_has_bmgn_products?
          weight = get_category_wise_weight
          value = Order.shipping_cost_for(weight, self.country)
        end
      else
        value = Order.shipping_cost_for(weight, self.country)
      end
    end
    value
  end

  def cart_has_bmgn_products?
    value = false
    self.designer_orders.each do |designer_order|
      designer_order.line_items.each do |line_item|
        return value = true if [1,2,3].include?(line_item.buy_get_free) && PromotionPipeLine.bmgnx_hash.present?
      end
    end
    value
  end

  def shipping_categories_available?
    value = false
    self.designer_orders.each do |designer_order|
      designer_order.line_items.each do |line_item|
        return value = true if (line_item.design.categories.pluck(:id) & EXCLUDE_FREE_SHIPPING_CATEGORIES).present?
      end
    end
    value 
  end

  def get_category_wise_weight
    value = 0
    self.designer_orders.each do |designer_order|
      designer_order.line_items.each do |line_item|
        value += line_item.weight if (line_item.design.categories.pluck(:id) & EXCLUDE_FREE_SHIPPING_CATEGORIES).present? || [1,2,3].include?(line_item.buy_get_free)
      end
    end
    value
  end

  def self.get_essential_shipping(line_items)
    # essentails_shipping = 0
    essentail_shipping, essential_designers_total, all_essential  = 0, 0, false
    designer_hash = Cart.get_essential_designers_total(line_items)
    designer_hash.each do |k, v|
      if v[:essential]
        essentail_shipping += ESSENTIAL_DESIGNERS["shipping_cost"].to_i if( v[:sum] < ESSENTIAL_DESIGNERS["total_below_x"].to_i && v[:sum] > 0 )
        essential_designers_total += v[:sum]
      end
    end
    if designer_hash.any?{|k,v| v[:essential] == false} #condtion for find if 1 non essentails exits
      all_essential = false
     else
      all_essential = true #all esssentials
    end
    return essentail_shipping, essential_designers_total, all_essential
  end

  def get_free_items_bmgnx(bmgnx_hash = PromotionPipeLine.bmgnx_hash)
    total_quantity, bmgnx_items_hash = 0, {}
    self.line_items.each do |item|
      if item.buy_get_free == 1
        bmgnx_items_hash[item.id] = [item.snapshot_price_currency(1), item.quantity]
        total_quantity += item.quantity
      end
    end
    if bmgnx_items_hash.present?
      bmgnx_items_hash = bmgnx_items_hash.sort_by {|k,v| -v[0]}.to_h
      factor = ((total_quantity + bmgnx_hash[:n]) / (bmgnx_hash[:m] + bmgnx_hash[:n])).to_i
      mx, nx, count = factor * bmgnx_hash[:m], factor * bmgnx_hash[:n], 0
      # total_charged_quantity are items with charge as full amount (mx)  
      total_charged_quantity = total_quantity > (mx + nx) ? (total_quantity - nx) : mx  
      # remove first mx items and keep only last nx items which are eligible for offer
      bmgnx_items_hash.each do |k,v|
        remaining = total_charged_quantity - count
        if count < total_charged_quantity
          if v[1] <= remaining
            count += v[1]
            bmgnx_items_hash.delete(k)
          else
            count += remaining
            v[1] -= remaining
            break
          end
        end
      end
    end
    bmgnx_items_hash
  end

  
  def build_by(params)
    if self.cart_id.present? && self.cart.present?
      self.billing_email = self.user.account.email if self.user.present? && self.user.account.present?
      self.build_designer_orders(self.cart_id)
      self.discount = self.cart.discount_for(:order)
      # just using arbitrary column to store theme details
      self.ccavenue_customer_identifier = AbTesting.current_theme
      coupon = self.cart.coupon
      if coupon.present? && coupon.is_shipping? && (self.cart.total_currency(1) >= coupon.min_amount)
        self.coupon = coupon
        coupon.increment!(:use_count)
      end
      self.pay_type = params[:pay_type]
      if self.discount > 0 || cart.free_stiching_coupon?
        self.coupon = self.cart.coupon
        self.coupon.use_count += 1
        self.coupon.save
      end
      ActiveRecord::Associations::Preloader.new.preload(self.cart.line_items,:line_item_addons)
      self.additional_discount = self.cart.additional_discounts(Design.country_code)
      # to disable wallet for cod in app change the or condition
      if !self.cod? && (wallet_details = cart.wallet_details(params[:country_code]))[:total] > 0
        self.referral_discount = wallet_details[:referral_amount]
        self.refund_discount = wallet_details[:return_amount]
      else
        self.cart.delete_wallet if cart.wallet_id.present?
      end
    end
    if params[:billing_address].present?
      self.build_address_by_address_id(params[:billing_address], 'billing')
    end
    order_discount = self.order_discounts.present? ? self.order_discounts.inject(0){|sum,i| sum + i.amount} : 0
    self.discount += order_discount if self.discount.present?
    self.email = self.billing_email
    self.cod_charge = Order.domestic_cod_charge(self.country,self.billing_pincode, self.cart) if self.cod?
    #self.cod_charge = COD_CHARGE if self.cod?
    #check if cart was verified for cod order
    self.validate_cod_otp = true if cod? && (['mobile','desktop'].include?(self.app_source.to_s.downcase) || verify_otp_for_app?)
    self.payment_gateway = params[:payment_gateway]
    self.currency_rate = params[:rate]
    self.geo = self.billing_country.try(:downcase) == 'india' ? 'domestic' : 'international'
    self.currency_code = CurrencyConvert.currency_convert_cache_by_country_code(params[:country_code]).symbol
    self.country_code = params[:country_code]
    self.actual_country_code = Design.country_code
    if self.country.present?
      country = Country.find_by_name(self.country)
      if country.present?
        if self.buyer_state.present?
          self.state_code = State.where(country_id: country.id, name: self.buyer_state).pluck(:iso3166_alpha2).first
        end
      end
    end
    self.currency_rate_market_value = CurrencyConvert.countries_marketrate[self.country_code]
    self.cod_available = self.cart.cod?(self.cod_charge,self.country,self.billing_pincode,self.currency_rate) if self.billing_pincode && self.cart.present?
    self.number = generate_unique_order_number
    self.record_current_bmgn_offer
    self
  end


  def copy_billing_details_to_shipping
    self.name = self.billing_name
    self.email = self.billing_email
    self.phone = self.billing_phone
    self.street = self.billing_street
    self.city = self.billing_city
    self.buyer_state = self.billing_state
    self.country = self.billing_country
    self.pincode = self.billing_pincode
  end

  def billing_first_name
    billing_name.to_s.split(' ', 2).first
  end
  
  def billing_last_name
    billing_name.to_s.split(' ', 2).last
  end
  

  # Copy line_items from cart
  #
  # TODO need to update coupon use count
  #
  # == Parameters:
  #   id::
  #     Integer - Cart ID
  #
  # Returns:
  # Self
  #
  def check_not_picked_up
    designer_orders.all?{|designer_order| %w(new pending).include?(designer_order.state)}
  end

  def build_designer_orders(id)
    Cart.designer_values(id).each do |designer_id, values|
      designer_order = designer_orders.build(designer_id: designer_id)
      designer_order.line_items = values[:line_items]
      designer_order.designer_payout_status = 'unpaid'
      designer_order.scaled_discount = values[:scaled_discount]
      designer_order.discount = values[:discount]
      # designer_order.order_id = self.id
      if designer_order.discount > 0
        designer_order.coupon = self.cart.coupon
        designer_order.coupon.use_count += 1
        designer_order.coupon.save
      end
      active_promotions = PromotionPipeLine.active_promotions
      var_hash = Promotion.discount_promotion(active_promotions,Design.country_code).try(:variables_hash)
      designer_order.promotion_discount = JSON.parse(var_hash)['global_discount_percent'].to_i if var_hash.present? && designer_order.sale_on_country?(active_promotions, Design.country_code)
      designer_order.save
      # self.designer_orders << designer_order
    end
    self
  end

  def self.generate_thirdwatch_payload(session_id, client_ip_address, user_agent, order_id)
  end

  # Populate address details based on address_id and type
  #
  # == Parameters:
  #   address_id::
  #     Integer - Address ID
  #   type::
  #     String - (only billing, shipping)
  #
  # == Returns:
  # Self
  #
  def build_address_by_address_id(address_id, type)
    if (address = self.user.addresses.find_by_id(address_id))
      address.attributes.except('id', 'landmark', 'user_id', 'created_at',
        'updated_at', 'default'
      ).each do |key, value|
        key = key.sub('_address', '') if key.starts_with? 'street_address'
        key =
          if type == 'billing'
            "billing_#{key}"
          elsif key == 'state'
            'buyer_state'
          else
            key
          end
        self.send("#{key}=", value)
      end
    end
  end

  state_machine initial: :new do
    state :cancel
    state :pending
    state :followup
    state :sane
    state :cancel
    state :confirmed
    state :complete
    state :fraud
    state :reject
    state :dispatched
    state :partial_dispatch
    state :pickedup
    state :cancel_complete
    state :ready_for_dispatch

    event :move_to_pending do
      transition :cancel => :pending
    end

    before_transition :to => [:pending] do |order|
      order.waiting!

      if order.pending_at.blank?
        order.pending_at = DateTime.current
        order.save
      end

      if order.state == 'cancel'
        order.calculate_shipping_for_pending_and_sane if order.international?
        order.update_design_quantity
      end
    end
  end

  state_machine :payment_state, initial: :pending_payment do
    state :failed
    state :completed
    state :processing
    state :void

    event :waiting do
      transition [:failed, :pending_payment] => :pending_payment
    end
  end

  def calculate_shipping_for_pending_and_sane
    if self[:notes].present? && self[:notes].include?('system_shipping_cost')
      shipping_cost = self[:notes].split(/system_shipping_cost/)[1][/\d+/].to_i
      self.update_column(:shipping, shipping_cost)
      self.shipping = shipping_cost
    end
  end

  def paypal_update_cc_order(response)
    paypal_payment = response.payment_info.first
    self.payment_gateway = 'paypal'
    if paypal_payment.present?
      case paypal_payment.payment_status
      when 'Completed'
        self.add_notes('SUCCESS', false)
        self.save
        if self.cart.present?
          self.cart.update_column(:used, true)
          #self.wallet_deductions(self.cart)
        end
        Order.sidekiq_delay.cancel_unless_confirmed_mobile(self.id)
        Order.sidekiq_delay.mail_order_details_to_buyer(self.id)
      when "Pending"
        update_column(:ccavenue_api_error, 'PPPENDING')
        Order.sidekiq_delay.paypal_pending_mobile(self.id)
      when "Denied"
        update_column(:ccavenue_api_error, 'PPDENIED')
        Order.sidekiq_delay.cancel_unless_confirmed_mobile(self.id)
      when "Failed"
        update_column(:ccavenue_api_error, 'PPFAILED')
        Order.sidekiq_delay.cancel_unless_confirmed_mobile(self.id)
      else
        update_column(:ccavenue_api_error, "PPUNKNOWN-#{paypal_payment.payment_status}")
      end
    else
      update_column(:ccavenue_api_error, 'PPUNKNOWN-PAYMENT_INFO_MISSING')
    end
  end

  def self.notify_airbrake(title: nil, message: nil, params1: {})
  end

  def self.send_stitching_info_mail_to_user_from_mobile(order_id)
  end

  def self.paypal_update_order(id)
  end

  # Method to be run in delayed job only
  # Dummy method actual method with main mirraw
  # Required to be kept in current state! Do not be smart
  #
  # == Parameters:
  #   id::
  #     Integer
  #
  # == Returns:
  # Boolean
  #
  def self.post_new_order(id)
  end

  # Provides shipping cost
  #
  # == Parameters:
  #   weight::
  #     Integer
  #
  # == Returns:
  # Integer
  #
  def self.shipping_cost_for(weight, country)
    cost = weight > 0 ? 250 : 0
    cost += weight > 250 ? 250 : 0
    if (left = (weight - 500)) > 0
      cost += ((left/250.00).ceil) * SHIPPING_ITEM_VALUE
    end

    if (shipping_multiplier = Country.find_by_namei(country).try(:shipping_multiplier)).present?
      cost = cost*shipping_multiplier
    end
    cost
  end

  # Order state in humanize form
  #
  # == Parameters:
  #   state::
  #     String
  #
  # == Returns:
  # String
  #
  def customer_state
    case self.state
    when 'new', 'pending', 'fraud'
      'Under Process'
    when 'sane', 'confirmed'
      'Confirmed'
    when 'ready_for_dispatch'
      'Ready for Dispatch'
    when 'pickedup', 'dispatched', 'partial_dispatch'
      'Dispatched'
    when 'complete'
      'Delivered'
    when 'cancel', 'cancel_complete', 'reject'
      'Canceled'
    else
      'Under process'
    end
  end

  def designer_orders_discount_currency(rate)
    self.designer_orders.inject(0) do |sum, designer_order|
      sum + designer_order.scaled_discount_currency(rate)
    end
  end

  def total_discount_currency(rate)
    (self.discount_currency(rate) + self.designer_orders_discount_currency(rate) + self.additional_discount_currency(rate) + self.wallet_discount(self.currency_rate.to_f/rate.to_f, !self.paypal_currency_allowed?)).round(CurrencyConvert.round_to)
  end

  def wallet_discount(rate, conversion)
    discount = (self.referral_discount.to_f + self.refund_discount.to_f)
    discount *= rate if conversion
    return discount.round(2)
  end

  def total_currency(rate)
    total = self.item_total(rate) + self.shipping_currency(rate) -
      self.total_discount_currency(rate) + self.cod_charge_currency(rate) + self.total_tax_currency(rate)
    total += self.subscription_fee_currency(rate) if subscription_fee_applied?
    total += self.platform_fee_currency(rate) if self.platform_fee > 0
    total += order_addon.gift_wrap_price_currency(rate) if order_addon.present?
    total += express_delivery_currency(rate) if express_delivery?
    total < 0 ? 0 : total.round(CurrencyConvert.round_to)
  end

  def add_notes(note, to_save, current_account = nil)
    first = current_account.nil? ? 'System' : current_account.email.split('@')[0]
    note_content = "#{Date.today.strftime("%m/%d")} : #{first} : #{note}"
    self.notes = if self[:notes].blank?
      ''
    else
      self[:notes] + ' ... '
    end
    self.notes = self[:notes] + note_content
    self.save if to_save
  end

  def add_notes_without_callback(note, note_type, current_account = nil)
    Event.where(note_type: (note_type.presence || 'other'), notes: note, done_by: (current_account.try(:name) || 'System'), event_timestamp: Time.current, account_id: current_account.try(:id), eventable_id: self.id, eventable_type: 'Order').first_or_create
  end

  def ga_action_data_hash(action_name)
    action_data = {}
    if action_name == 'purchase'
      action_data['id'] = self.number
      action_data['affiliation'] = self.utm_source.present? ? self.utm_source : 'Mirraw Mobile Web'
      action_data['revenue'] = self.total_currency(1)
      action_data['tax'] = '0'
      action_data['shipping'] = self.shipping_currency(1)
      action_data['coupon'] = self.coupon.try(:code)
      action_data['tax'] = '0'
    end
    action_data
  end

  def self.create_payment(cart, shipping_address, rate, symbol)
    create_paypal_payment(cart, shipping_address, rate, symbol)
  end

  def domestic_paypal?(country_code, currency_symbol)
    country_code == 'IN' && ['inr', 'rs'].include?(currency_symbol.try(:downcase)) && country.downcase == 'india' && billing_country.downcase == 'india'
  end

  def add_to_payment_gateway_details(value, update_now: false)
    new_details = [payment_gateway_details, value].compact.join("\n")
    if update_now
      update_column(:payment_gateway_details, new_details)
    else
      self.payment_gateway_details = new_details
    end
  end

  def self.store_paypal_smartpay_related_details(payment_response)
  end

  # Gives array of chunks of given address
  #
  # == Parameters
  #  street(String)         :: street value to be chunked
  #  chunk_size(Integer)    :: size of each chunk
  #  n(Integer)             :: number of chunks to be returned
  #
  # == Returns
  #  Array
  #
  def chunked_street(street, chunk_size, n = 2)
    street.scan(/^.{1,#{chunk_size}}\b/).first(n).map(&:strip)
  end

  def create_cod_request
    self.designer_orders.each do |designer_order|
      designer_order.line_items.each do |item|
        design = item.design
        pincode = self.billing_pincode
        cod = design.designer.can_cod?(pincode)
        CodRequest.where(ip: self.ip_address, app_source: ['Mobile', 'Desktop'],
                  pincode: pincode, design_id: design.id, is_cod_available: cod).first_or_create
      end
    end
  end

  def g2a_checksum(rate)
    g2a_args = [
      id,
      '%.2f'%total_currency(rate),
      currency_code,
      Rails.application.config.g2a[:api_secret_key]
    ]
    Digest::SHA256.new.hexdigest(g2a_args.join)
  end

  def g2asetup!(return_url, cancel_url, shipping_address)
    ActiveRecord::Associations::Preloader.new.preload(cart.line_items,design: :designer)
    g2a_params = calculate_g2a_params(return_url, cancel_url, self.currency_rate , shipping_address)
    response = HTTParty.post(
      Rails.application.config.g2a[:checkout_create_url],
      verify: false,
      body: g2a_params,
      headers: {'Authorization' => "#{Rails.application.config.g2a[:api_hash]};#{Order.g2a_hash}",'Content-type'=>'application/x-www-form-urlencoded'}
      )
    if response["status"] == "ok"
      return "#{Rails.application.config.g2a[:payment_url]}?token=#{response["token"]}",nil
    else
      Order.sidekiq_delay.notify_exceptions("G2A Redirect Error", response['message'], { params: g2a_params ,reponse: response})
      return Rails.application.routes.url_helpers.new_order_path,'Something went wrong with payment gateway!'
    end
  end

  def calculate_g2a_params(return_url, cancel_url, rate, shipping_address)
    {
      api_hash: Rails.application.config.g2a[:api_hash],
      hash: g2a_checksum(rate),
      order_id: self.number,
      amount: '%.2f'%(self.cart.total_currency(rate)+self.shipping_currency(rate)),
      currency:(self.currency_code == 'Rs' ? 'INR' : self.currency_code),
      email: self.billing_email,
      url_failure: cancel_url,
      url_ok: return_url,
      items: g2a_item_list(rate,shipping_address).to_json
    }
  end

  def g2a_item_hash(line_item,rate)
    price = line_item.snapshot_price_currency(rate)
    line_item.line_item_addons.each do |li_addon|
      price += li_addon.snapshot_price_currency(rate)
    end
    {
      sku: line_item.design_id.to_s,
      name: line_item.design.title,
      amount: (price*line_item.quantity).round(2),
      type: 'Ethnic Wear',
      qty: line_item.quantity,
      price: price.round(2),
      id: line_item.design_id.to_s,
      url: MirrawMobile::Application.routes.url_for(action:'show',controller: 'designs',designer_id: line_item.design.designer.cached_slug,id:line_item.design.cached_slug,host:'www.mirraw.com')
    }
  end
  def g2a_shipping_hash(rate)
    {
      sku: 'Shipping',
      name: 'Shipping',
      amount: self.shipping_currency(rate).round(2),
      type: 'Shipping',
      qty: 1,
      price: self.shipping_currency(rate).round(2),
      id: 0,
      url: ''
    }
  end

  def g2a_discount_hash(rate)
    if (discount = cart.total_discounts_currency(rate) + cart.wallet_discounts(self.actual_country_code, rate)) > 0
      {
        sku: 'Discount',
        name:'Discount',
        amount: sprintf('%.2f', -discount),
        type: 'Discount',
        qty:1,
        price: sprintf('%.2f', -discount),
        id: 0,
        url: ''
      }
    end
  end

  def g2a_item_list(rate,shipping_address)
    line_items_list = []
    cart.line_items.each do|line_item|
      line_items_list << g2a_item_hash(line_item,rate)
    end
    line_items_list << g2a_shipping_hash(rate)
    discount_hash = g2a_discount_hash(rate)
    line_items_list << discount_hash unless discount_hash.nil?
    return line_items_list
  end


  def self.g2a_hash
    Order::G2A_HASH ||=Digest::SHA256.new.hexdigest([Rails.application.config.g2a[:api_hash],Rails.application.config.g2a[:merchant_email],Rails.application.config.g2a[:api_secret_key]].join)
  end

  def multi_line_address(line_length, split_var)
    multi_line_address_block = []
    i = 0
    split_var.split(/[ ,]/).each do |s|
      if multi_line_address_block[i].blank?
        multi_line_address_block[i] = s
      elsif (multi_line_address_block[i].length + s.length < line_length)
        multi_line_address_block[i] += ' ' + s
      else
        i += 1
        multi_line_address_block[i] = s
      end
    end
    multi_line_address_block
  end

  def self.mail_order_details_to_buyer(id)
  end

  def self.cancel_unless_confirmed_mobile(id)
  end

  def self.cancel_order_mobile(id)
  end

  def self.paypal_pending_mobile(id)
  end

  def self.send_feedback_mail(name, feedback)
  end

  def order_details
    item_details = []
    market_rate = self.currency_rate_market_value
    currency_rate = self.currency_rate
    self.designer_orders.each do |designer_order|
      designer_order.line_items.each do |line_item|
        item = {}
        item[:design_id] = line_item.design_id
        item[:title] = line_item.design.title
        item[:type] = line_item.design.categories.first.try(:title)
        item[:category_name] = line_item.design.categories.first.try(:name)
        item[:price] = line_item.design.price
        item[:sold_at_price] = market_rate.present? ? (line_item.snapshot_price_currency(currency_rate)*market_rate).round(CurrencyConvert.round_to) : 0
        item[:discount] = ((disc = item[:price] - item[:sold_at_price]) > 0) ? disc.round(2) : 0
        item[:quantity] = line_item.quantity
        item[:rating] = line_item.design.average_rating
        item[:number_of_ratings] = line_item.design.total_review
        item[:number_of_reviews] = line_item.design.reviews.where.not(review: nil).count
        item[:designer_id] = line_item.design.designer_id
        item[:designer_name] = line_item.design.designer.name
        item[:actual_design_qty] = line_item.design.quantity + item[:quantity]
        item[:variants] = line_item.variants
        item[:addons] = line_item.addons(self.currency_rate)
        item[:number_of_images] = line_item.design.images.count
        item[:collection] = line_item.design.collection_list
        item[:catalogue] = line_item.design.catalogue_list
        item[:source] = line_item.source
        item_details << item
      end
    end
    { total: self.total,
      ltv_total: self.ltv_total,
      shipping: self.ltv_shipping,
      user_email: self.user.try(:email),
      user_name: self.user.full_name,
      billing_address: {city: self.billing_city,
                        state: self.billing_state,
                        country: self.billing_country,
                        pincode: self.billing_pincode},
      shipping_adderess: {city: self.city,
                          state: self.buyer_state,
                          country: self.country,
                          pincode: self.pincode},
      coupon_used: self.cart.coupon.present?,
      coupon_code: self.used_coupon_code(self.cart.coupon),
      referral_discount: self.referral_discount,
      refund_discount: self.refund_discount,
      item_details: item_details,
      order_cashback_msg: self.order_cashback_message,
      order_number: self.number,
      platform_fee: self.platform_fee,
      subscription_fee: self.subscription_fee,
      orderId: self.id,
      return_url: "https://" + ENV["MIRRAW_MOBILE_DOMAIN"] + '/orders/' + self.number
    }
  end

  def ltv_total
    if (market_rate = self.currency_rate_market_value).present?
      ((self.total/self.currency_rate)*market_rate).round(CurrencyConvert.round_to)
    end
  end

  def ltv_shipping
    if (market_rate = self.currency_rate_market_value).present?
      ((self.shipping/currency_rate)*market_rate).round(CurrencyConvert.round_to)
    end
  end

  def cod_verification_attempt_pending?
    self.cod_verify_msg_count == 0 ? true : false
  end

  def used_coupon_code(coupon)
    coupon.present? ? coupon.code : nil
  end

  #to make route helpers work with order number
  def to_param
    number
  end

  def attempt_auto_confirm
    if check_similar_confirmed_order_week?
      self.tag_list.add('duplicate')
      self.add_notes_without_callback("Added tag duplicate", 'other')
    elsif self.previous_user_confirmed_order? || true
      # Confirm all orders that are not duplicate
      Order.sidekiq_delay.mobile_good_data(self.id)
    end
    self.save!
  end

  def check_similar_confirmed_order_week?
    w_confirmed = 'orders.confirmed_at IS NOT NULL'
    w_state_not = 'orders.state NOT IN (?)', ['cancel', 'cancel_complete']
    w_date      = 'orders.created_at BETWEEN ? and ?', 1.week.ago, Time.now
    current_order_design_ids = self.line_items.order(:design_id).pluck(:design_id)
    other_orders_similar_design_ids = LineItem.
      joins(:designer_order => :order).
      where('orders.id <> ?', self.id).
      where('orders.user_id = ?', self.user_id).
      where(:design_id => current_order_design_ids).
      where(w_confirmed).
      where(w_state_not).
      where(w_date).
      pluck(:design_id)
    other_orders_similar_design_ids.blank? ? false : true
  end

  # Checks for preivous confirmed orders for user related to current order
  #
  # Return true if confirmed_orders > 0 else false

  def previous_user_confirmed_order?
    if self.user.present?
      cod_orders_confirmed_count = self.user.orders.where('confirmed_at IS NOT NULL').count
      cod_orders_confirmed_count > 0 ? true : false
    else
      false
    end
  end

  def self.mobile_good_data(id)
  end

  def cod_delivered?
    self.designer_orders.each do |designer_order|
      return false unless (shipment = designer_order.shipment).present? && (shipment.shipment_state == 'delivered')
    end
    return true
  end

  def wallet_deductions(cart)
    if (cart_wallet = cart.wallet).present? && (self.referral_discount || self.refund_discount)
      self.user.wallet.deduct_amount(cart_wallet)
    end
  end

  def wallet_discounts(country_code, rate)
    if self.cart.present?
      self.cart.wallet_discounts(country_code, rate, nil, true)
    else
      (self.wallet_discount(rate, true)).round(2)
    end
  end

  def deduct_shipping_from_wallet(cart_return_amount)
    if international? && (credit_left = user.wallet_amounts.return_amount - cart_return_amount) > 0
      converted_shipping = shipping/currency_rate
      [credit_left, converted_shipping].min
    end
  end

  def rakhi_pre_order
    value = {}
    count, rakhi_all_schedule = 0, 0
    self.line_items.each do |item|
      rakhi_all_schedule += 1 if item.note.present? && item.note.match('Rakhi Schedule Delivery')
      count += 1
    end
    if count == rakhi_all_schedule
      value['rakhi_all_schedule'] = true
    elsif rakhi_all_schedule != 0 && count > rakhi_all_schedule
      value['rakhi_with_other_designs'] = true
    end
    value
  end

  def get_paypal_paid_rate_code
    paid_currency_code_value = PAYPAL_ALLOWED_CURRENCIES.include?(self.currency_code) ? self.currency_code : 'USD'
    @currency_rate = self.currency_rate
    paid_currency_rate_value = get_paypal_rate
    return paid_currency_rate_value, paid_currency_code_value
  end

  def order_status(all_line_items, api = false)
    if all_line_items.nil? && api
      {
        order_placed: 'Order Placed',
        order_confirmed: 'Order Confirmed',
        in_warehouse: nil,
        quality_check: nil,
        ready_to_ship: nil,
        order_dispatched: nil
      }
    else
      status_hash = {line_items_count: all_line_items.length, item_receive_count: 0, qc_pass_count: 0, qc_fail_count: 0, stitching_sent_count: 0, stitching_done_count: 0, stitch_items: 0}
      all_line_items.each do |item|
        item_design = item.design
        status_hash[:item_receive_count] += 1 if item.received == 'Y'
        status_hash[:qc_pass_count] += 1 if (item_design.skip_qc == false && item.qc_done == 'Y' && item.qc_status) || item_design.skip_qc == true
        status_hash[:qc_fail_count] += 1 if item_design.skip_qc == false && item.qc_done == 'Y' && item.qc_status == false
        status_hash[:stitch_items] += 1 if item.stitching_required == 'Y'
        status_hash[:stitching_done_count] += 1 if item.stitching_done == 'Y'
        status_hash[:stitching_sent_count] += 1 if item.stitching_sent == 'Y'
      end
      get_status_note(status_hash, api)
    end
  end

  def order_status_api
    if international? || line_items.map(&:stitching_required).include?('Y')
      if (['new','pending','fraud'].include? state)
        order_status(nil, true)
      else
        order_status(
          line_items.where('line_items.status is null OR line_items.status =?','buyer_return')
          .where('designer_orders.state not in (?)',['canceled','vendor_canceled']),
          true
        )
      end
    else
      nil
    end
  end

  def self.generate_checsum(params)
    {
      "CHECKSUMHASH" => PaytmUtilities.new_pg_checksum(params,@@paytm[:paytm_merchant_key]).tr("\n",''),
      "ORDER_ID" => params["ORDER_ID"],
      "payt_STATUS" => 1
    }
  end

  def self.verify_checksum_array(params)
    checksum_hash = params.delete("CHECKSUMHASH")
    PaytmUtilities.new_pg_verify_checksum(params, checksum_hash, @@paytm[:paytm_merchant_key]) ? params["IS_CHECKSUM_VALID"] = "Y" : params["IS_CHECKSUM_VALID"] = "N"
    return params
  end

  def returnable_line_items(app_source,app_version,mobile = false)
    items_status, last_date = {}, nil
    shipment = shipments
    all_items = mobile ? line_items : line_items.includes([design: :categories], :designer_order)
    if mobile || (app_source.downcase.include?('android') && ALLOWED_APP_VERSIONS[40..-1].include?(app_version)) || (app_source.downcase.include?('ios') && ALLOWED_IOS_APP_VERSIONS[7..-1].include?(app_version))
      all_items.each  do |item|
        designer_order = item.designer_order
        items_status[item.id] = if (self.international? && self.dispatched?) || designer_order.pickup.present?
                                  if self.domestic?
                                    if self.cod?
                                      if (shipment = designer_order.shipment).present? && shipment.delivered_on.present?
                                        last_date = shipment.delivered_on.advance(days: 7)
                                        (last_date.past? ? 'Return duration validity is expired.' : '')
                                      else
                                        'Product not yet delivered !'
                                      end
                                    else
                                      (designer_order.pickup.advance(days: 12).past? ? 'Return duration validity is expired.' : '')
                                    end
                                  elsif (shipment = item.shipment).present? && shipment.delivered_on.present?
                                    last_date = shipment.delivered_on.advance(days: 7)
                                    (last_date.past? ? 'Return duration validity is expired.' : '')
                                  else
                                    'Product not yet delivered !'
                                  end
                                else
                                  'Product not yet shipped.'
                                end
        items_status[item.id] = if item.return_id.present? || item.return_designer_order_id.present?
                                  'Product already returned'
                                elsif item.stitching_required == 'Y'
                                  'Stitching required products are not covered in return policies'
                                elsif item.status == 'cancel' || ['canceled','vendor_canceled'].include?(designer_order.state)
                                  'This Item was canceled after ordering.'
                                elsif ((category_name = item.design.designable_type.try(:downcase)) == 'consumable') || (category_name = item.design.category_parents_name & ['organic-foods','lingerie']).present?
                                  "#{category_name} product is not covered in return policy."
                                else
                                  items_status[item.id]
                                end
      end
    end
    return (mobile ? [items_status, last_date] : items_status)
  end

  def self.tag_measurements(order_id,review_id,design_id,tag_name)
  end

  def add_order_addons
    self.create_order_addon(gift_wrap_price: GIFT_WRAP_PRICE.to_f)
  end

  def do_wallet_transaction
    if user_id.present? && (refund_discount.to_f > 0 || referral_discount.to_f > 0)
      wallet_transactions.create(user_id: user_id, wallet_id: user.try(:wallet_id), referral_amount: referral_discount.to_f, return_amount: refund_discount.to_f, return_id: nil)
    end
  end

  def is_fully_paid_by_wallet?
    total <= 0 && (refund_discount.to_f > 0 || referral_discount.to_f > 0)
  end

  def invalid_order?(params)
    params[:pay_type] == 'Cash On Delivery' && cart.wallet_discount_applied?
  end

  # def new_invoice_number
  #   generate_unique_order_number(10)
  # end

  def process_braintree_order(result, device_data)
    if (transaction_info = result.try(:transaction)).present? && store_braintree_related_data(transaction_info, device_data)
      if transaction_info.try(:processor_response_text) == 'Approved'
        update_column(:notes, "#{self[:notes]} SUCCESS")
        Order.sidekiq_delay.mail_order_details_to_buyer(id)
      else
        Order.sidekiq_delay.notify_exceptions("Braintree Transaction Response Error", 'processor_response_text error', { order: number.to_s, processor_response_text:  transaction_info.try(:processor_response_text).to_s })
        Order.sidekiq_delay.paypal_pending_mobile(id)
      end
    else
      Order.sidekiq_delay.notify_exceptions("Braintree Transaction Missing", 'transaction result error', { order: number.to_s, transaction_info: {braintree_txn_id: transaction_info.id, paypal_txn_id: transaction_info.try(:paypal_details).try(:authorization_id), amount: transaction_info.amount.to_f, status: transaction_info.status , debug_id: transaction_info.try(:paypal_details).try(:debug_id)}})
      Order.sidekiq_delay.paypal_pending_mobile(id)
    end
  end

  def store_braintree_related_data(transaction_info, device_data)
    if transaction_info.present?
      self.skip_filter = true
      log = {}
      paypal_detail = transaction_info.paypal_details
      log['braintree_txn_id'] = transaction_info.id
      log['debug_id'] = paypal_detail.try(:debug_id)
      log['device_data'] = device_data
      self.payment_gateway_details = log.to_json
      self.payment_gateway = 'paypal-braintree'
      self.paypal_txn_id = paypal_detail.try(:authorization_id)
      self.paypal_payer_id = paypal_detail.try(:payer_id)
    end
    self.save if self.changed?
  end

  def get_error_info(result)
    error_log = {code: '', msg: '', attribute: '', readable_msg: result.message}
    if (errors = result.errors).present?
      errors.each do |error|
        error_log[:code] << "#{error.try(:code)},"
        error_log[:msg] << "#{error.try(:message)}"
        error_log[:attribute] << "#{error.try(:attribute)},"
      end
    end
    error_log.to_json
  end

  def assign_billing_from_user_address(address, email = nil)
    assign_attributes(
      billing_name: address.name,
      billing_phone: address.phone,
      billing_pincode: address.pincode,
      billing_street: address.street_address,
      billing_street_line_1: address.street_address_line_1,
      billing_street_line_2: address.street_address_line_2,
      billing_country: address.country,
      billing_state: address.state,
      billing_city: address.city,
      billing_email: email
    )
  end
  
  def assign_billing_from_order(old_order)  
    assign_attributes(
      billing_name: old_order.billing_name,
      billing_phone: old_order.billing_phone,
      billing_pincode: old_order.billing_pincode,
      billing_street: old_order.billing_street,
      billing_country: old_order.billing_country,
      billing_state: old_order.billing_state,
      billing_city: old_order.billing_city,
      billing_email: old_order.billing_email,
      pay_type: old_order.pay_type
    )
  end
  

  def self.generate_client_token
    gateway = Braintree::Gateway.new( :access_token => BRAINTREE_ACCESS_TOKEN)
    gateway.client_token.generate
  end

  def cashback_available?
    other_details['loyalty_rewards_credit'].to_f > 0
  end

  def cashback_rewarded?
    wallet_transactions.any?(&:cashback_transaction?)
  end

  def satisfies_cashback_conditions?
    ['cancel', 'cancel_complete'].exclude?(state)
  end

  def record_current_bmgn_offer
    current_bmgn_offer = PromotionPipeLine.current_bmgn_offer
    other_details[:bmgn_promotion] = current_bmgn_offer unless current_bmgn_offer.nil?
  end

    #Below method is to get the cashback message for an order
  def order_cashback_message
    if satisfies_cashback_conditions? && cashback_available? && !cashback_rewarded?
      key = self.international? ? 'cashback.reminder.international.html' : 'cashback.reminder.domestic.html'
      price = "#{currency_code} #{ApplicationController.helpers.get_price_in_currency(other_details['loyalty_rewards_credit'].to_f, currency_rate)}"
      I18n.t(key, price: price)
    end    
  end

  def prepaid?
    !post_paid?
  end

  def post_paid?
    bank_deposit? || cod?
  end

  def cancellable?
    cod? && %w(sane confirmed).include?(state) && order_notification['order_cancelled'].blank? && check_not_picked_up
  end

  def self.order_cancel_reasons
    I18n.t('order.cancellation.reasons')
  end

  def returnable?
    ['cancel', 'cancel_complete', 'fraud', 'reject'].exclude?(state)
  end

  def save_order_cancel_reason(reason, current_account)
    add_notes_without_callback(reason, 'order_cancel_reason', current_account)
    LineItem.where(id: line_items.select{|item| item.cancel_reason.blank? }.map(&:id)).update_all(cancel_reason: reason)
  end

  def document_upload_url
    cipher = OpenSSL::Cipher::AES.new(128, :CBC).encrypt
    cipher.key = Rails.application.config.url_secret_key
    iv = cipher.random_iv
    encrypted = cipher.update(self.number) + cipher.final
    "https://"+ ENV['MIRRAW_MOBILE_DOMAIN'] + "/kyc_documents/new?order_number=#{(Base64.urlsafe_encode64(iv + encrypted))}"
  end

  def national_id_required?
    country == 'South Africa' && user.present? && !user.kyc_documents.exists?(name: 'National ID')
  end

  def subscription_fee_applied?
    self.other_details['subscription_fee_applied'] == 'true'
  end

  private

  #checks if cart was otp verified and adds error
  def otp_verified_cod_order?
    if !COD_OTP_DISABLED && self.domestic?
      errors[:base] << 'OTP must be verified for COD orders'  if cod? && cart.try(:otp) != 'verified'
    end
  end

  #check for app and respective versions that support otp feature
  def verify_otp_for_app?
    source = app_source.split('-').map(&:downcase)
    (source[0] == 'android' && ALLOWED_APP_VERSIONS[29..-1].include?(source[-1])) && !(cart.otp_verified?(source[0]))
  end

  def verify_shipping_promo_version_for_app?
    source = self.app_source.split('-')
    source[source.length - 1] > PREPAID_PROMOTION_MINIMUM_VERSION[source[0]]
  end

  def get_status_note(status_hash = {}, api)
    status = ['Order Placed-yellowgreen','Order Confirmed-yellowgreen']
    status_api = { order_placed: 'Order Placed', order_confirmed: 'Order Confirmed' }
    if self.state == 'dispatched'
      status += get_name_of_stage('ready_to_ship','yellowgreen')
      status_api.merge!(get_name_of_stage('ready_to_ship','yellowgreen', api)) if api
      if (self.tracking_number.present? && (shipment_state = self.shipments.where(shipments: {number: self.tracking_number}).pluck(:shipment_state).first).present?)
        if shipment_state == 'delivered'
          status += ['Order Dispatched-yellowgreen','Order Delivered-yellowgreen-delivered']
          status_api.merge!({order_dispatched_complete: 'Order Dispatched', order_delivered_complete: 'Order Delivered'})
        else
          status += ['Order Dispatched-yellowgreen','Order Delivered-#AF8C39-delivered']
          status_api.merge!({order_dispatched_complete: 'Order Dispatched', order_delivered: 'Order Delivered'})
        end
      else
        status += ['Order Dispatched-yellowgreen-order_dispatched']
        status_api.merge!({order_dispatched_complete: 'Order Dispatched'})
      end
    elsif (['ready_for_dispatch','partial_dispatch'].include? self.state)
      status += get_name_of_stage('ready_to_ship','yellowgreen') + ['Order Dispatched-#D4A025-order_dispatched']
      status_api.merge!(get_name_of_stage('ready_to_ship','yellowgreen', api)).merge!({order_dispatched: 'Order Dispatched'}) if api
    elsif status_hash[:stitching_done_count] < status_hash[:stitch_items] && status_hash[:stitching_sent_count] == status_hash[:stitch_items]
      status += get_name_of_stage('out_for_stitching', 'yellowgreen') + ['Stitching Completed-#D4A025-stitching_completed'] + get_name_of_stage('ready_to_ship', 'white')
    elsif status_hash[:stitch_items] > 0 && status_hash[:stitching_done_count] == status_hash[:stitch_items]
      status += get_name_of_stage('stitching_completed','yellowgreen') + ['Ready To Ship-#D4A025-ready_to_ship','Order Dispatched-white']
      status_api.merge!(get_name_of_stage('stitching_completed','yellowgreen', api)).merge!({ready_to_ship: 'Ready To Ship', order_dispatched: nil}) if api
    elsif status_hash[:qc_pass_count] == status_hash[:line_items_count] && self.items_received_status
      status += get_name_of_stage('quality_check','yellowgreen')
      status_api.merge!(get_name_of_stage('quality_check','yellowgreen', api)) if api
      if status_hash[:stitch_items] > 0
        status += ["#{status_hash[:stitching_sent_count]} / #{status_hash[:stitch_items]} #{'item'.pluralize(status_hash[:stitch_items])} sent for stitching-#D4A025-out_for_stitching"] + get_name_of_stage('stitching_completed','white')
        status_api.merge!({stitching_completed: "#{status_hash[:stitching_sent_count]} / #{status_hash[:stitch_items]} #{'item'.pluralize(status_hash[:stitch_items])} sent for stitching"}).merge!(get_name_of_stage('ready_to_ship','white', api)) if api
      else
        status += ['Ready To Ship-#D4A025-ready_to_ship','Dispatched-white']
        status_api.merge!({ready_to_ship: 'Ready To Ship', order_dispatched: nil}) if api
      end
    elsif self.items_received_status
      status += ['All Items received in warehouse-yellowgreen',"#{status_hash[:qc_pass_count]} / #{status_hash[:line_items_count]} #{'item'.pluralize(status_hash[:line_items_count])} passed quality test-#D4A025-quality_check"] + get_name_of_stage('out_for_stitching','white')
      status_api.merge!({in_warehouse: 'In Warehouse', quality_check: "#{status_hash[:qc_pass_count]} / #{status_hash[:line_items_count]} #{'item'.pluralize(status_hash[:line_items_count])} passed quality test."}).merge!(get_name_of_stage('stitching_completed','white', api)) if api
    else
      status += ["#{status_hash[:item_receive_count]} / #{status_hash[:line_items_count]} #{'item'.pluralize(status_hash[:line_items_count])} received in warehouse-#D4A025-in_warehouse"] + get_name_of_stage('quality_check','white')
      status_api.merge!({in_warehouse: "#{status_hash[:item_receive_count]} / #{status_hash[:line_items_count]} #{'item'.pluralize(status_hash[:line_items_count])} received in warehouse"}).merge!(get_name_of_stage('quality_check','white', api)) if api
    end
    if status_hash[:stitch_items] == 0
      status -= ['Stitching Completed-white','Stitching Completed-yellowgreen','Out For Stitching-white','Out For Stitching-yellowgreen']
      status_api.except!(:stitching_completed)
    end
    api ? status_api : status
  end

  def get_name_of_stage(stage_name,color, api = false)
    order_of_stages = ['in_warehouse','quality_check','out_for_stitching','stitching_completed','ready_to_ship','order_dispatched']
    if color == 'white'
      required_stages = order_of_stages.slice(order_of_stages.index(stage_name)..order_of_stages.length)
    else
      required_stages = order_of_stages.slice(0..order_of_stages.index(stage_name))
    end
    all_names = []
    required_stages.each do |name|
      all_names << "#{name.gsub('_',' ').titleize}-#{color}"
    end
    if api
      response = {}
      counter = 0
      required_stages.each do |stage|
        response.merge!({stage.to_sym => all_names[counter].split('-')[-1] != 'white' ? all_names[counter].split('-')[0] : nil})
        counter+=1
      end
      return response
    else
      return all_names
    end
  end

  # Updating discount fields for line item
  #
  def cod_updates
    # Designer Order Discounts
    dos_discount = 0
    self.designer_orders.each do |designer_order|
      if designer_order.discount.present?
        dos_discount += designer_order.discount
        designer_order.update_line_item_discount_by(self)
      end
    end

    diff = self.discount.to_i + dos_discount -
      self.designer_orders.sum(:discount_provided)

    adjust_discount_for(diff) if diff != 0
  end

  # Adjusting discount amount in line items for difference
  #
  def adjust_discount_for(diff)
    self.designer_orders.includes(:line_items).
      where(state: ['new', 'pending']).each do |designer_order|

      discount_provided = designer_order.discount_provided

      designer_order.line_items.each do |line_item|
        if (additional_discount_piece = (diff/line_item.quantity.to_f).
          floor) != 0
          discount, snapshot_discount_price = line_item.adjust_discount(
            additional_discount_piece)

          diff -= discount
          discount_provided += discount

          LineItem.where(id: line_item.id).
            update_all(snapshot_discount_price: snapshot_discount_price)
          DesignerOrder.where(id: designer_order.id).
            update_all(discount_provided: discount_provided)
        end
        return true if diff == 0
      end
    end
  end

  # Get total weight of designer orders
  #
  # == Returns:
  # Integer
  #
  def get_weight
    value = 0
    self.designer_orders.each do |designer_order|
      value += designer_order.weight
    end
    value
  end

  # Calculates values for various totals
  #
  def build_totals
    self.total, self.shipping, self.mirraw_addon_charges, weight, self.total_tax = 0, 0, 0, 0, 0
    flag_for_add_notes = false
    flag_for_one_minute_saree = false
    self.designer_orders.each do |designer_order|
      # designer_order.send(:build_totals) unless (['new','pending','confirmed'].include?(state))
      unless ["canceled", 'cancel'].include? designer_order.state
        designer_order.send(:build_totals) unless self.new_record?
        if ['new','pending'].include?(self.state) && designer_order.paid_line_item_addons?
          self.tag_list.add('addon') if self.tag_list.exclude?('addon')
          flag_for_add_notes = true
          designer_order.line_items.each do |line_item|
            if line_item.paid_addons?
              line_item.update_column('stitching_required', 'Y')
              flag_for_one_minute_saree = true if (!flag_for_one_minute_saree && line_item.inhouse_pre_stitching)
            end
          end
          self.tag_list.add('one_minute_saree') if flag_for_one_minute_saree
        end
        self.total += designer_order.scaled_total
        self.shipping += designer_order.shipping
        self.mirraw_addon_charges += designer_order.addons_value_for('mirraw')
        weight += designer_order.weight
      end
    end
    if !(self.new_record?) && flag_for_add_notes && !(self.events.pluck(:notes).include?('Added tag addon'))
      self.add_notes_without_callback('Added tag addon','stitching')
    end
    self.discount = 0 if self.discount.blank?
    self.cod_charge = 0 if self.cod_charge.blank?
    self.total = self.total + self.shipping + self.mirraw_addon_charges -
    self.discount + self.cod_charge - self.additional_discount  unless self.cancel?
    self.shipping += self.shipping_cost(weight,self.total)
    if self.platform_fee > 0
      self.total += self.platform_fee
    end
    self.total = self.total + self.shipping
    self.total += order_addon.get_total_order_addon_charges if order_addon.present?
    self.total += express_delivery if express_delivery?
    self.total = 0 if self.total < 0
    if self.new_record?
      tax_percent,self.total_tax,self.total,_ = get_total_with_tax(country_code, self.total) 
      self.other_details['tax_rate'] = tax_percent / 100.0 
    else
      self.total += self.subscription_fee
      self.total_tax = self.total * self.other_details['tax_rate'].to_f
      self.total += self.total_tax
    end
    
    self.total -= self.wallet_discounts(self.actual_country_code, self.currency_rate) unless self.cancel?
    # if (cart = self.cart).present? && self.total == 0
    #   self.wallet_deductions(cart)
    # end
  end

  def apply_subscription_discount_on_items
    return unless self.subscription_fee > 0 
    return if subscription_fee_applied?
  
    all_items = self.designer_orders.flat_map { |d| d.line_items }
  
    total_snapshot = all_items.sum { |item| item.snapshot_price_currency(1) * item.quantity.to_i }
    return if total_snapshot <= 0
    discount_ratio = (self.subscription_fee.to_f / total_snapshot.to_f).round(6)
    total_discount_given = 0.0
  
    all_items.each_with_index do |item, index|
      original_price = item.snapshot_price_currency(1)
      item_total = original_price * item.quantity.to_i
  
      item.other_details ||= {} 
      item.other_details['original_price'] = original_price
      discount_total = (item_total * discount_ratio).round(2)
      if index == all_items.size - 1
        discount_total = (self.subscription_fee.to_f - total_discount_given).round(2)
      end
      total_discount_given += discount_total
  
      per_unit_discount = (discount_total / item.quantity).round(2)
      new_price = (original_price - per_unit_discount).round(2)
      new_price = 0 if new_price < 0  # safety check
  
      item.snapshot_price = new_price / item.scaling_factor
      item.save!(validate: false)
    end
  
    self.other_details['subscription_fee_applied'] = 'true'
  end

  # Provides unique order number
  #
  # FIXME refactor this method and implement validation using
  # validates_* utilities
  #
  # == Returns:
  # String
  #
  def generate_unique_order_number(length=9)
    record = true
    while record
      random = "M#{Array.new(9){rand(10)}.join}"
      record = self.class.unscoped.where(number: random).exists?
    end
    random
  end

  def set_order_number
    # begin
    #   if (unique_order_number = Redis.current.spop('order_number')).present?
    #     order_number = unique_order_number
    #   else
    #     raise StandardError.new "Redis store empty"
    #   end
    # rescue RuntimeError, StandardError => e
    #   Airbrake.notify(error_class: 'Redis Error',
    #   error_message: "#{e.inspect}",
    #   parameters: {})
    order_number = generate_unique_order_number
    # end
    self.number = order_number if self.number.blank?
  end

  # Adds addon related updates to order
  #
  def addon_update
    self.tag_list.add('addon')
    self.add_notes_without_callback('Added tag addon', 'stitching')
  end

  def self.find_and_delete_previous_related_orders(order_ids)
  end

  def self.designer_orders_count(cart)
    if cart.present?
      design_ids = cart.line_items.pluck(:design_id)
      Design.select('DISTINCT(designer_id)').where(id: design_ids).count
    end
  end

  def self.create_cod_order_request(charges,cart,ship_address,grandtotal,ip_addr,rate = 1)
    is_cod_available = cart.cod?(charges,ship_address.country,ship_address.pincode,rate)
    unless is_cod_available
      charges = 0
    end
    designer_orders_count = Order.designer_orders_count(cart)
    CodRequest.where(cart_id: cart.id, order_total: grandtotal, pincode: ship_address.pincode.to_s).first_or_create(is_cod_available: is_cod_available,line_items_count: cart.line_items.count, designer_order_count: designer_orders_count, ip: ip_addr, app_source: 'Mobile', total_cod: charges)
  end

  def self.process_juspay_order!(jp_order_status)
  end

  def self.send_document_to_shipper(order_id,document_name)
    self.sidekiq_delay.send_document_to_shipper(order_id,document_name)
  end

  def self.notify_exceptions(title = nil, error_message = nil, params = {})
    # ExceptionNotifier.notify_exception(
    #   Exception.new(title),
    #   data: {message: error_message ,params: params} 
    # )
    Order.sidekiq_delay.notify_exceptions(title, error_message, { params: params })
  end

  def self.domestic_cod_charge(country,pincode,cart)
    return 0 if ENABLE_COD_CHARGE == 'false' && country.try(:downcase) == 'india'
    foreign_cod_country = cart.allowed_cod_country(country) && country.try(:downcase) != 'india'
    return 0 if foreign_cod_country
    if cart.kind_of? Cart
      line_items = cart.line_items
      total_cod = 0
      designer_cod = 0
      total_cost_to_ship = 0
      total_vendor_cost = 0
      pin = Pincode.find_by_pin_code(pincode)
      if pin.present?
        total_profit = 0
        cost_to_ship =  ((!foreign_cod_country && pin.cost_to_ship.present? && pin.cost_to_ship != 0) ? pin.cost_to_ship : COD_DEFAULT_COST_TO_SHIP.to_i)
        designer_orders = {}
        designer_orders_total = {}
        line_items.preload(design: :designer).each do |item|
          designer = item.design.designer
          if !designer_orders.include? designer.name
            total_vendor_cost += COD_TOTAL_VENDOR_COST.to_i
            total_cost_to_ship += cost_to_ship
          end
         #Calculate Charge A for pincode
        base_cod_charge = ((!foreign_cod_country && pin.cod_charge.present? && pin.cod_charge != 0) ? pin.cod_charge : COD_DEFAULT_COD_CHARGE.to_i)
        rto_percent = ((!foreign_cod_country && pin.rto_percent.present? && pin.rto_percent != 0) ? pin.rto_percent : COD_DEFAULT_RTO_PERCENT.to_i)
         designer_orders[designer.name] = [] unless designer_orders[designer.name].present?
         designer_orders_total[designer.name] = [] unless designer_orders[designer.name].present?

         charge_A = ( base_cod_charge  * ( 1 / (1- rto_percent) ) )
         # puts "***********************************CHARGE-A: #{charge_A}***********************************"
         #Calculate Charge B for the designer order value
         charge_B = ((item.snapshot_price.to_f/100)*COD_COMMISSION.to_i)
         # puts "***********************************CHARGE-B: #{charge_B}***********************************"
         designer_cod = (charge_A > charge_B) ? charge_A : charge_B
         designer_orders[designer.name].push(designer_cod)
         designer_orders_total[designer.name].push(item.snapshot_price*item.quantity)
         designer_transaction_rate = designer.transaction_rate.present? ? designer.transaction_rate : TRANSACTION_RATE
         designer_orders_total[designer.name+'_transaction_rate'] = designer_transaction_rate
        end

        designer_orders.keys.each do |i|
         total_cod += designer_orders[i].max
         total = designer_orders_total[i].sum
         vendor_payout = total * (100 - designer_orders_total[i+'_transaction_rate']) / 100
         total_profit += total - vendor_payout
        end
        total_cod_required = total_cost_to_ship + COD_MINIMUM_PROFIT.to_i - total_vendor_cost - total_profit
        if total_cod_required <= total_cod
          return total_cod.ceil
        else
          return total_cod_required.ceil
        end
      end
      return false
    end
    return false
  end
  include StreetAddressLineable
  include Wisper::Publisher
end

require 'line_item_calculations'
