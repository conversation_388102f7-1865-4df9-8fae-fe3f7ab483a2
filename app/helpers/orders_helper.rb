module OrdersHelper
  def currency_params(order)
    currency = if order.paid_currency_code.present?
                  CurrencyConvert.currency_convert_memcached.find{|cc| cc.symbol == order.paid_currency_code}
                else
                  CurrencyConvert.currency_convert_cache_by_country_code(order.country_code || @country_code)
                end
    values = {}
    values[:rate] = order.paid_currency_rate || order.currency_rate || @rate
    values[:currency_symbol] = order.paid_currency_code || order.currency_code || @symbol
    values[:hex_symbol] = currency.try(:hex_symbol) || @hex_symbol
    values[:round_to] = currency.try(:round_to) || 2
    values[:market_rate] = currency.try(:market_rate) || 1
    values
  end

  def is_response_signature_valid?(params)
    signature = params[:signature]
    params.slice!('order_id', 'status', 'status_id')
    key = ENV['JUSPAY_RESPONSE_KEY']
    encoded_sorted = []
    params.keys.sort.each { |k| encoded_sorted << URI.encode(k) + "=" + URI.encode(params[k]) }
    encoded_string = CGI.escape(encoded_sorted.join("&"))
    hash_string = CGI.escape(Base64.encode64(OpenSSL::HMAC.digest(OpenSSL::Digest.new('sha256'), key, encoded_string)).strip())
    URI.decode(hash_string) == signature
  end

  def set_wallet_pay_type
    shipping_address = Address.find params["order"]["shipping_address"]
    grandtotal = @order.cart.total_currency(@rate,@country_code,shipping_address.country)
    if grandtotal <= 0 
      @order.pay_type = "Mirraw Wallet"
    else
      redirect_to_order_new('Your payment failed. Invalid Wallet Amount') and return
    end
  end



  def calculate_payu_params(order)
    # Adding Mandatory info
    params = order.payu_web_create_params
    params.merge!(key: Order::PAYU_MERCHANT_KEY)

    # Adding urls for payu
    response_url = 'https://' +
      ENV.fetch('MIRRAW_DOMAIN') +
      payu_response_orders_path
    params.merge!(curl: response_url, surl: response_url, furl: response_url)
  end

  def payu_money_info(order)
    params = calculate_payu_params(order)
    {url: "#{Order::PAYU_URL}_payment", params: params}
  end

  def payu_redirect_url(order)
    params = calculate_payu_params(order)
    response = HTTParty.post(
      "#{Order::PAYU_URL}/_payment?type=merchant_txn",
      :verify => false,
      :body => params,
      :headers => browser.opera_mini? ? {} : {'User-Agent' => request.env["HTTP_USER_AGENT"]
    })
    "#{Order::PAYU_URL}_payment_options?mihpayid=#{response.body.strip}" if response.code == 200
  end

  def order_cancelled_handler
    if cookies[:payment_failure_count].present?
      cookies[:payment_failure_count] = cookies[:payment_failure_count].to_i + 1
    else
      cookies[:payment_failure_count] = { value: 1, expires: 12.hours.from_now }
    end
    new_order_url
  end
  
  def trigger_ga4_purchase_event(order)
    return false unless ['followup', 'sane', 'new'].include?(order.state)
    return false if order.other_details['purchase_event_triggered']

    date_time = if order.sane?
                  (order.confirmed_at + 2.minutes)
                elsif order.new? && order.cod? 
                  (order.created_at + 5.minutes)
                else
                  (order.created_at + 5.minutes)
                end
    if DateTime.now <= date_time
      order.update(other_details: order.other_details.merge('purchase_event_triggered' => true))
      return true
    else
      return false
    end
  end
  
  def order_summary_data(order:, cart:, rate:, country_code:, cod_charges_inr:, billing_address:, shipping_address:, session:)
    discounts = cart.total_discounts_currency(rate, country_code)
    prepaid_discount = cart.prepaid_discount.to_i
    cod_charges = get_price_in_currency(cod_charges_inr || 0) || 0
    item_total = cart.items_total_without_addons(rate)
    addons_charges = cart.addons_total(rate)
    wallet_details = cart.wallet_details(country_code)
    wallet_referral = wallet_details[:referral_amount].round(2)
    wallet_discount = wallet_details[:total].round(2)
    total_before_shipping = item_total + addons_charges - discounts

    order_country = order.billing_country
    shipping_charges = 0
    essential_shipping_charges = essential_total = all_essential = 0
    prepaid_shipping_promo = (country_code == 'IN' && DOMESTIC_PREPAID_SHIPPING_PROMOTION && order_country == 'India')  ? 'available' : ""
    prepaid_promotion = country_code == 'IN' && order_country == 'India'
    if order_country == 'India' || country_code == 'IN'
      essential_shipping_charges, essential_total, all_essential = Order.get_essential_shipping(cart.line_items)
    end

    shipping_charges += if order_country == 'India' || country_code == 'IN'
      (all_essential ? essential_shipping_charges :
        cart.shipping_cost_currency(order_country, rate, total_before_shipping - essential_total) +
        essential_shipping_charges)
    else
      cart.shipping_cost_currency(order_country, rate)
    end

    grandtotal = total_before_shipping + shipping_charges
    grandtotal += get_price_in_currency(GIFT_WRAP_PRICE.to_f) if session[:gift_wrap] && GIFT_WRAP_PRICE.to_f >= 0

    if session[:prepaid_failed_retry] && (cart.cod?(cod_charges_inr, order_country, order.billing_pincode, rate) || billing_address != shipping_address)
      prepaid_fail_popup = true
    else
      prepaid_fail_popup = false
    end

    tax_percent, tax_amount, grandtotal, tax_enable = cart.get_total_with_tax(country_code, grandtotal)
    tax_details = { tax_percent: tax_percent.to_f, tax_amount: tax_amount.to_f, tax_enable: tax_enable,grandtotal: grandtotal.to_f }

    cod_total = grandtotal
    grandtotal -= wallet_discount
    grandtotal = grandtotal.round(2)

    {
      discounts: discounts,
      prepaid_discount: prepaid_discount,
      cod_charges: cod_charges,
      item_total: item_total,
      addons_charges: addons_charges,
      wallet_details: wallet_details,
      wallet_referral: wallet_referral,
      wallet_discount: wallet_discount,
      total_before_shipping: total_before_shipping,
      shipping_charges: shipping_charges,
      grandtotal: grandtotal,
      tax_details: tax_details,
      cod_total: cod_total,
      prepaid_fail_popup: prepaid_fail_popup,
      prepaid_shipping_promo: prepaid_shipping_promo,
      prepaid_promotion: prepaid_promotion
    }
  end

  def prepare_country_data(order, country_code)
    country = Country.find_by_name(order.billing_country) if order.present?
    phone = order.billing_phone if order.present?
    dial_code = country.dial_code if country.present?
    order.billing_phone = phone.sub(/^\+#{dial_code}/, '') if phone.present?
    disabled_value = ['----------']
    countries = Order::PRIORITY_COUNTRIES + disabled_value + Order::NON_PRIORITY_COUNTRIES
  
    billing_country = if order.billing_country.present?
                        Country.where('LOWER(name) = ?', order.billing_country.downcase).first
                      end
  
    billing_states = billing_country.present? ? billing_country.states.pluck(:name).sort : nil
    buyer_states = country.present? ? country.states.pluck(:name).sort : nil
  
    {
      order: order,
      countries: countries,
      ship_countries: countries,
      shipping_restrict: false,
      billing_country: billing_country,
      billing_states: billing_states,
      buyer_states: buyer_states,
      disabled_value: disabled_value,
      country: country 
    }
  end  

end
