class ApplicationController < ActionController::Base
  include MirrawUtil
  # Prevent CSRF attacks by raising an exception.
  # For APIs, you may want to use :null_session instead.

  # Need to allow login using DeviseTokenAuth without CSRF
  # Commented this as Devi<PERSON><PERSON>okenAuth inherits ApplicationController
  # ToDo: Fix following warnings/issues
  # >> https://shaileshjain.airbrake.io/projects/116965/groups/2021726803802872152?tab=overview
  # >> Skip all actions of devise token auth controller for api
  # protect_from_forgery with: :exception

  helper_method :guest_logged_in?

  before_filter :run_first, if: :is_web_request?
  before_filter :is_bot_request, :set_cart, if: :is_web_request?
  before_filter :user_currency
  before_filter :dynamic_value_for_pages
  before_filter :configure_permitted_parameters, if: :devise_controller?
  before_filter :store_location, :unless => :devise_controller?
  before_filter :set_unbxd_cookie
  before_filter :capture_ga_event, if: :is_web_request?
  before_filter :set_theme
  before_filter :set_experiments
  before_filter :set_menu
  before_filter :set_subscription_image, if: :is_web_request?
  before_filter :set_dynamic_cookie
  before_action :set_app_source

  def capture_ga_event
    if flash[:ga_event].present?
      @ga_login_events = flash[:ga_event]
      flash.delete(:ga_event)
    end
  end

  def ip_blocker
    if request.path.include?('/etc/passwd') || 
      request.path.include?('wp-admin') ||
      request.path.include?('wp-login') ||
      request.path.include?('acunetix_wvs_security_test') ||
      (IP_BLOCK_LIST.include?(request.ip) || IP_BLOCK_LIST.include?(request.env["HTTP_TRUE_CLIENT_IP"]))
      redirect_to("/block") && return
    end
  end

  def run_first
    ip_blocker if ENV['IP_BLOCK_ENABLE'].present? && !(params[:controller] == 'errors' && params[:action] == 'block_page')
    if (params[:controller] == 'store') || (params[:controller] == 'designs' && params[:action] == 'show') || (params[:controller] == 'pages' && ['coupons','home'].include?(params[:action])) || (params[:controller] == 'carts' && params[:action] == 'show')
      session[:previous_request_url] = session[:current_request_url]
      session[:current_request_url] = Zlib.crc32 request.original_url

      #Creating array for saving multi channel utm_source, utm_medium, utm_campaign
      session[:utm_campaign] = '' unless session[:utm_campaign].present?
      session[:utm_source] = '' unless session[:utm_source].present?
      session[:utm_medium] = '' unless session[:utm_medium].present?
      session[:utm_expid] = params[:utm_expid] if params[:utm_expid].present?

      if session[:previous_request_url] != session[:current_request_url]
        session[:utm_campaign].concat(",#{params[:utm_campaign]}") if params[:utm_campaign].present?
        session[:utm_source].concat(",#{params[:utm_source]}") if params[:utm_source].present?
        session[:utm_medium].concat(",#{params[:utm_medium]}") if params[:utm_medium].present?

        session[:utm_term] = params[:utm_term] if params[:utm_term].present?
        session[:utm_content] = params[:utm_content] if params[:utm_content].present?

        session[:referral_campaign] = params[:referral_campaign] if params[:referral_campaign].present?

        if params[:utm_source].blank? && params[:utm_campaign].blank?
          if params[:gclid].present?
            session[:utm_source].concat(",google")
            session[:utm_medium].concat(",cpc")
            session[:utm_campaign].concat(",none")
          elsif request.referer.present? && request.referer.exclude?('utm_source') && request.referer.include?('google')
            session[:utm_source].concat(",google")
            session[:utm_medium].concat(",organic")
            session[:utm_campaign].concat(",none")
            session[:utm_term] ||= request.referer.to_s[/(([\w-]+\.){1,3}\w{2,6})(?:\/|$)/]
          elsif request.referer.present? && request.referer.exclude?('utm_source') && request.referer.include?('bing')
            session[:utm_source].concat(",bing")
            session[:utm_medium].concat(",organic")
            session[:utm_campaign].concat(",none")
            session[:utm_term] ||= request.referer.to_s[/(([\w-]+\.){1,3}\w{2,6})(?:\/|$)/]
          elsif request.referer.present? && request.referer.exclude?('utm_source') && request.referer.include?('yahoo')
            session[:utm_source].concat(",yahoo")
            session[:utm_medium].concat(",organic")
            session[:utm_campaign].concat(",none")
            session[:utm_term] ||= request.referer.to_s[/(([\w-]+\.){1,3}\w{2,6})(?:\/|$)/]
          elsif request.referer.present?
            session[:utm_term] ||= request.referer.to_s[/(([\w-]+\.){1,3}\w{2,6})(?:\/|$)/]
          end
        end
      end

      #Saving only last five entries of utm_source, utm_medium, utm_campaign in session
      if session[:utm_campaign].present? && session[:utm_campaign].count(',') >= 6
        del_ca = session[:utm_campaign].index(',',1).to_i
        session[:utm_campaign] = session[:utm_campaign].slice!(del_ca..1000)
      end
      if session[:utm_source].present? && session[:utm_source].count(',') >= 6
        del_s = session[:utm_source].index(',',1).to_i
        session[:utm_source] = session[:utm_source].slice!(del_s..231)
      end
      if session[:utm_medium].present? && session[:utm_medium].count(',') >= 6
        del_m = session[:utm_medium].index(',',1).to_i
        session[:utm_medium] = session[:utm_medium].slice!(del_m..231)
      end

      if request.referer.present? &&  session[:utm_term].blank?
        session[:utm_term] = request.referer.to_s[/(([\w-]+\.){1,3}\w{2,6})(?:\/|$)/]
      end
    end
    if request.referer.present? &&  (request.referer.include?('icn_term=') || request.referer.include?('icn='))
      session[:icn_term] = request.referer.to_s[/(icn|icn_term)=[\w\d\-_]+/i].to_s + '----' + request.referer.to_s[/(ici|ici_term)=[\w\d\-_]+/i].to_s
    end
  end

  def featured_products(number_of_products = 4)
    @featured_products = Rails.cache.fetch("featured_products_mobile_#{number_of_products}", expires_in: 24.hours) do
      Design.search(include: [:images,:designer]) do
        with(:state, 'in_stock')
        with(:featured_product, true)
        with(:cluster_winner, 1)
        paginate(:page => 1, :per_page => number_of_products)
        order_by :random
      end.results || []
    end
  end

  # Initialize variable @bot_request depending user agent
  #
  # == Returns:
  # BOOLEAN
  #
  def is_bot_request
    @bot_request ||= Rails.env.development? ? true : browser.bot?
  end

  # Checks if it is web request or api request
  #
  # == Returns:
  # BOOLEAN
  #
  def is_web_request?
    (request.headers['Device-ID'].present? && request.headers['Device-ID'] == API_DEVICE_ID) || request.headers['Token'].blank?
  end

  def set_subscription_image
    if ENV['ENABLE_SUBSCRIPTION'].to_s == 'true' && cookies[:user_subscribed].blank?
      @subscription_banner_hash = Rails.cache.fetch("subscription_banner_#{@country_code}_mobile", expires_in: 24.hours) {
        NewsletterBanner.banner_hash(@country_code)
      }
    end
  end

  # Find or creates cart for current user
  #
  # == Parameters:
  #   associations::
  #     ARRAY
  #   id::
  #     NUMBER
  #
  # == Return:
  # Cart object
  # Only use when current_cart is required, do not call unnecessarily
  def current_web_cart(associations = [], id = nil)
    id = nil if id and id.to_i == 0
    @cart ||= Cart.includes(associations).where(used: false).find_by_id(id || session[:cart_id])
    if @cart.blank?
      if account_signed_in?
        @cart = Cart.includes(associations).
          where({ used: false, email: current_user.try(:email), user_id: current_user.try(:id) }).
          order('id DESC').first_or_initialize

        if @cart.new_record?
          @cart.generate_hash_value!
        end
      else
        @cart = Cart.includes(associations).create(used: false)
      end
    else
      if account_signed_in? && @cart.user_id.blank?
        @cart.update_attribute(:user_id, current_account.accountable_id)
        @cart.update_attribute(:email,current_account.try(:email))
      end
      refresh_cart_ordered_items unless @already_refreshed
    end
    session[:cart_id] = @cart.id
    session[:cart_count] = get_cart_count
    set_cart_promotion if @cart.id.present?
    current_pe_subscriber.update_cart(@cart) if current_pe_subscriber
    @cart
  end

  def refresh_cart_ordered_items
    @cart.refresh_ordered_items!
    @already_refreshed = true
  end

  # Finds current cart for user
  # If not present returns nil
  def current_web_cart_without_create
    Cart.find_by_id(session[:cart_id]) if session[:cart_id].to_i > 0
  end

  # Find or creates cart for current user and set cart_id in session
  #
  # == Return:
  # Cart object
  # session[:cart_id]
  #
  def set_cart
    session[:cart_id] = 0 unless session[:cart_id].present?
    session[:cart_count] = 0 unless session[:cart_count].present?
  end

  def reset_current_web_cart(account)
    unless account.user? and
           !session[:cart_id].in?([nil, 0]) and
           Cart.where(:id => session[:cart_id]).exists? and
           (@cart = Cart.find_by_id(session[:cart_id])).total_items > 0

      if account.user? and (cart = Cart.where({ used: false, user_id: account.user.id }).order('id DESC').first).present?
        session[:cart_id] = cart.id
        session[:cart_count] = cart.total_items
      end
    end
  end

  # Get Current User from current_account
  def current_user
    @user ||= if account = (current_account || (session[:guest_account_id].present? && Account.find_by_id(session[:guest_account_id])))
      account.try :user
    end
  end

  # configuration of permitted parameters for devise controllers
  #
  def configure_permitted_parameters
    devise_parameter_sanitizer.for(:account_update) do |u|
      u.permit(:password, :password_confirmation, :current_password, :phone)
    end
    devise_parameter_sanitizer.for(:sign_up) do |u|
      u.permit(:email, :password, :password_confirmation, :terms_of_service, :phone)
    end
  end

  # Determines whether menu details are required for request
  #
  def requires_menu_details?
    return false unless is_web_request?
    devise_controller? || !(params[:action].in? %w(create update destroy))
  end

  def get_cart_count
    @cart.line_items.sum(:quantity)
  end

  # Determines whether guest user is logged in or not
  #
  def guest_logged_in?
    !account_signed_in? && session[:guest_account_id].present?
  end

  def authenticate_current_cart!
    if current_web_cart([:line_items]).line_items.present?
      redirect_to accounts_guest_login_path if current_user.blank? && browser.device.mobile?
    else
      redirect_to root_path , error: 'Please select an item to place order'
    end
  end

  def set_menu
    @menu_list = Menu.menu_by_hide_appsource_country(@country_code,APP_SOURCE[0], nil)
  end

  def set_unbxd_cookie
    if cookies[:'unbxd.userId'].blank? || (account_signed_in? && current_account.unbxd_user_id != cookies[:'unbxd.userId'])
      if account_signed_in?
        if current_account.unbxd_user_id.blank?
          cookies[:'unbxd.userId'] ||= "uid-#{Time.now.to_i}-#{rand(100000)}"
          current_account.update_attributes(unbxd_user_id: cookies[:'unbxd.userId'])
          cookies[:'unbxd.visit'] = 'first_time'
        else
          cookies[:'unbxd.userId'] = current_account.unbxd_user_id
          cookies[:'unbxd.visit'] = 'repeat'
        end
      else
        cookies[:'unbxd.userId'] = "uid-#{Time.now.to_i}-#{rand(100000)}"
        cookies[:'unbxd.visit'] = 'first_time'
      end
    end
  end

  def update_phone_params_with_dial_code
    params[:address][:phone].try{|phone| phone.prepend(params[:address][:dial_code].to_s)} if params[:address].present? && params[:address][:country].to_s != 'India'
  end

  def cache_clear
    Rails.cache.clear
    render :nothing => true
  end

  private
  
  def set_app_source
    @app_source ||= session[:app_source] ? session[:app_source] : app_source_service.determine_and_store_app_source(session)
  end
  
  def app_source_service
    @app_source_service ||= AppSourceService.new(request.headers)
  end

  def gtm_data_layer
    @gtm_data_layer ||= []
  end

  def after_sign_in_path_for(resource)
    session[:previous_url] || root_path
  end

  def store_location
    # store last url - this is needed for post-login redirect to whatever the user last visited.
    session[:previous_url] = request.fullpath if request.get? && !request.xhr? && current_account.nil?
  end

  def set_theme
    preffered_theme = 'red_theme'
    # if browser.bot?
    #   'black_theme'
    # elsif params[:theme].present?
    #   params[:theme]
    # else
    #   cookies[:theme]
    # end
    cookies[:theme] = { value: AbTesting.decide_theme!(preffered_theme), expires: (Time.current + 1.year) }
    # if NEW_THEME_ROLLOUT.present?
    #   user_id = rand(1..100)
    #   new_hash = {}
    #   NEW_THEME_ROLLOUT.inject(0){|i, (key, val)| new_hash[key] = (i+1..val) ; val}
    # else
    #   cookies[:theme] = 'black_theme'
    # end
    # cookies[:theme] = 'black_theme' if NEW_THEME_ROLLOUT == 0
    # if cookies[:theme].blank?
    #   # ui_user_id = Rails.cache.fetch(:ui_user_id)
    #   # user_id = (ui_user_id.present? && ui_user_id < 100) ? (ui_user_id.to_i + 1) : 1
    #   # Rails.cache.write(:ui_user_id,user_id)
    #   user_id = rand(1..100)
    #   cookies.permanent[:theme] = (user_id > NEW_THEME_ROLLOUT) ? 'black_theme' : 'white_theme'
    # end
  end

  def set_experiments
    cookies[:review_exp] = { value: AbTesting.recent_review_experiment(cookies[:review_exp]), expires: (Time.current + 1.month) }
    set_review_exp
  end

  def set_dynamic_cookie
    if account_signed_in? || session[:cart_count].to_i > 0 || ['white_theme', 'red_theme'].include?(cookies[:theme])
      cookies[:dynamic_user] = 't'
    else
      cookies.delete :dynamic_user
    end
    if account_signed_in?
      cookies[:signed_in] = '1'
    else
      cookies.delete :signed_in
    end
  end

  def current_pe_subscriber=(pe_subscriber)
    @current_pe_subscriber = begin
      session[:pe_subscriber_id] = pe_subscriber.id
      cookies[:pe_subscribed] = {value: 't', expires: 1.year.from_now} # may used by gtm
      pe_subscriber
    end
  end

  def current_pe_subscriber
    @current_pe_subscriber ||= if session[:pe_subscriber_id].present?
      PushEngageSubscriber.find_by_id(session[:pe_subscriber_id])
    end
  end

  def dynamic_value_for_pages
    if controller_name == 'designs' && action_name == 'show' && @design.present?
      ecomm_prodid = @design['id']
      @ecomm_pagetype = 'product'
      ecomm_totalvalue = @design['discount_price'] * @rate
      @ecomm_category = @design['category_name']
  
    elsif controller_name == 'store' &&  action_name == 'catalog_page'
      if params[:kind].present?
        @ecomm_pagetype = 'category'
        @ecomm_category = @category.try(:name) || params[:kind]
      elsif params[:collection]
        @ecomm_pagetype = 'category'
        @ecomm_category = params[:collection]
      elsif params[:q].present?
        @ecomm_pagetype = 'searchresults'
        @ecomm_category = params[:q]
      end
      if @store_page.try(:[], 'designs').present?
        ecomm_prodid = @store_page['designs'].collect{|ds| ds['id']}
      end
    elsif controller_name == 'pages' && action_name == 'home'
      @ecomm_pagetype = 'home'
  
    elsif controller_name == 'store' && action_name == 'dynamic_landing_page' && @dynamic_landing_page.present?
      @ecomm_pagetype = 'other'
      @ecomm_category = @dynamic_landing_page.name
  
    elsif (controller_name == 'pages' || controller_name == 'store') && action_name == 'landing' && @landing.present?
      @ecomm_pagetype = 'other'
      @ecomm_category = @landing.label
  
    elsif controller_name == 'pages' && action_name == 'coupons'
      @ecomm_pagetype = 'other'
      @ecomm_category = 'coupons'
  
    elsif controller_name == 'pages' && action_name == 'eid'
      @ecomm_pagetype = 'other'
      @ecomm_category = 'eid'
  
    elsif controller_name == 'sessions' && action_name == 'guest_login'
      @ecomm_pagetype = 'guest_login'
    elsif controller_name == 'carts' && action_name == 'show' && @cart.present?
      ecomm_prodid = @cart.line_items.collect(&:design_id)
      @ecomm_pagetype = 'cart'
      ecomm_totalvalue = @cart.item_total(1) - @cart.total_discounts
      @ecomm_category = @cart.line_items.to_a.map{|line_item|  line_item.nil? ? nil : line_item.design.categories.pluck(:name).first}
  
  
    elsif controller_name == 'orders' && action_name == 'new' && @cart.present?
      ecomm_prodid = @cart.line_items.collect(&:design_id)
      @ecomm_pagetype = 'ordernew'
      ecomm_totalvalue = @cart.item_total(1) - @cart.total_discounts
      @ecomm_category = @cart.line_items.map{|line_item| line_item.design.categories.first.name}
  
    elsif controller_name == 'orders' && action_name == 'show' && @track
      ecomm_prodid = @order.line_items.collect(&:design_id)
      @ecomm_pagetype = 'purchase'
      ecomm_totalvalue = @order.total
      @ecomm_category = @order.line_items.map{|line_item| line_item.design.categories.first.name}
  
    else
      @ecomm_pagetype = 'other'
    end
  end

end
