class PagesController < ApplicationController

  include PagesHelper
  skip_before_filter :run_first, :is_bot_request, :set_cart, only: [:contact_us, :tnc, :assetlinks, :trending_searches, :apple_app_site_association]
  skip_before_filter :user_currency, only: [:tnc, :assetlinks, :trending_searches, :apple_app_site_association]
  skip_before_filter :set_menu, only: [:trending_searches, :apple_app_site_association]
  #before_action :featured_products, only: [:home]
  newrelic_ignore :only => [:home_amp, :about_amp] if Rails.env.production? || Rails.env.staging?
  before_action :get_current_user, only: [:help_center], unless: :is_web_request?

  caches_action :offers, cache_path: proc { |c|
    c.params['Currency-Code'] = @symbol
    c.params['Country-Code'] = Design.country_code }, expires_in: API_CACHE_LIFESPAN.minutes

  def home
    previous_url = request.referer
    @account = current_account
    @phone_popup = previous_url && previous_url.include?('sign_in') ? true : false
    @boards = get_response_data_for('dynamic_homepage', params) || {}
    @cart = current_web_cart([],session[:cart_id])
    @flash_page = get_response_data_for('flash_deals', params)['flash_deals']
    @seo = Rails.cache.fetch("seo_post_home_page_mobile", expires_in: 24.hours) do
      SeoList.where(label: 'home_page').first
    end
    @newly_added_products = {}
    # if @country_code == 'IN'
    #   # @newly_added_products = get_response_data_for('newly_added_products')
    #   @newly_added_products = {}
    # end
    if cookies[:sticky_coupon_banner].blank?
      @sticky_coupon_banner =  Rails.cache.fetch("sticky_banner_#{@country_code}_mobile", expires_in: 24.hours) do
        BannerSlider.graded.mirraw.where(name: ["sticky_coupon_banner"]).country(@country_code).app_source(@app_source.downcase).to_a
      end.first
    end
    if params[:more_items].present? && request.xhr?
      @boards = get_response_data_for('dynamic_homepage', params) || {}
      respond_to do |format|
        format.js { render 'home' }
      end
    end
    @ga_list = 'homepage'
    gtm_data_layer.push({pageType: 'home'})
    set_callback_flash
  end

  def home_amp
    if params[:dynamic_amp_data]
      home()
      render :json => @boards.present? ? @boards['homepage_widgets'].group_by{|board| board["type"]} : []
    else
      @seo = SeoList.where(label: 'home_page').first
      gtm_data_layer.push({pageType: 'home'})
      render 'home_amp', layout: 'application_amp'
    end
  end

  def error_404
    #@errors = Frontpage.graded.live.where(mobile_menu: true).limit(8)
    @bestseller_designs = Bestseller.get_bestsellers(@country_code)
    @ga_list = 'error / 404'
    render status: :not_found
  end

  def trending_searches
    if (trending_url = ENV['TOP_SEARCH_UNBXD_URL']).present?
      if stale? etag: 'trending_search', last_modified: Time.zone.now.beginning_of_day.utc
        response = Rails.cache.fetch(:unbxd_trending_queries_result_mobile, expires_in: 6.hours) do
          response = HTTParty.get(trending_url)
          response = response.body.present? ? JSON.parse(response.body, quirks_mode: true).prepend(code: 200) : JSON.parse('[{"empty_response": true}]').prepend(code: 404)
        end
        if response[0][:code] == 200
          render json: response
        else
          Rails.cache.delete(:unbxd_trending_queries_result_mobile)
          render json: {empty_response: true}
        end
      end
    else
      render json: {empty_response: true}
    end
  end

  def apple_app_site_association
    app_id = (Rails.env == 'production' ? 'HH76P9GQQ4.com.Mirraw.iOS' : 'HH76P9GQQ4.com.Mirraw.Staging')
    response =
     {
        'applinks': {
          'apps': [],
          'details': [
              {
                  "appID": app_id,
                  "paths": IOS_DEEP_LINK_PATTERNS
              }
          ]
        }
      }
     render json: response.to_json
   end

  def contact_us
    render file: 'public/contact_us.html.haml', layout: false
  end

  def about
    @seo = SeoList.where(label: 'about_page').first
  end

  def about_amp
    @seo = SeoList.where(label: 'about_page').first
    render 'about_amp', layout: 'application_amp'
  end

  def coupons
    @coupons = Coupon.includes(:designer).live.designer_coupons.advertise.order('end_date asc').all
  end

  def mirraw_coupons
    @coupons = Coupon.where(name: "MIRRAW_MARKETING_COUPON").where('start_date <= ? AND end_date >= ?', Time.zone.now, Time.zone.now)
  end

  def tnc
    render file: 'public/tnc.html', layout: false
  end

  def offers
    @designer_discounts = Designer.active_additional_discount.order("additional_discount_percent DESC").limit(36)
    @coupons = Coupon.includes(:designer).live.designer_coupons.advertise.order('end_date asc').first(4)
    @discounted_products = Rails.cache.fetch("discounted_products_mobile", expires_in: API_CACHE_LIFESPAN.minutes) do
      discounted_designs = Design.includes(:master_image, :designer).in_stock.where('discount_price > ?', 1000).order('discount_percent DESC').limit(16)
      @discounted_products = discounted_designs.present? ? discounted_designs.entries : []
    end
  end

  def faq
    @seo = SeoList.select('label, title, description, keyword').where(label: 'faq_page').first
    @region_wise = %w(inr rs).include?(@symbol.downcase) ? "domestic" : "international"
    @quick_links = SupportText.select('id,category_of_faq,order_for_category,priority,question,answer').
          where('category_of_faq is not null and order_for_category is not null').
          where.not(category_of_faq: 'Help Center').
          where(visible_to_designer: false).
          where(@region_wise + "= true").
          order(:order_for_category)
  end


  def mirraw_partner_program
    render template: "layouts/mirraw_partner_program",layout: false
  end

  def landing
    @landing = Landing.where(label: params[:landing],category_landing: true).first
    if @landing.present?
      @nav_tabs = @landing.nav_tabs.collapsed.graded.includes(:nav_blocks)
      @tabs = @landing.nav_tabs.normal.graded.includes(:nav_blocks)
      @widgets = @landing.widgets.graded.includes(:nav_blocks)
      side_menu_sort = request.xhr? ? params[:side_menu_sort].to_i : @landing.side_menu_sort
      if @landing.side_menu?
        if params[:landing].include?('gemstones')
          gemstone_property=Property.where(name: 'gemstones').first
          @menu_mapping = Property.get_property_value_mapping(['gemstones'])[:gemstones].map{|k,v| [v,k.to_s]}.to_h
          case side_menu_sort
          when 0
            @menu_list = gemstone_property.get_designs_by_count(params[:landing])
          when 1
            @menu_list = gemstone_property.get_designs_by_name(params[:landing])
          end
          @menu_list_name = "Gemstones"
        else
        @menu_mapping = Category.get_names_to_ids
        category = Category.find_by_namei(params[:landing])
          case side_menu_sort
          when 0
            @menu_list = category.get_menu_list_by_count(params[:landing])
          when 1
            @menu_list = category.get_menu_list_by_name(params[:landing])
          end
          @menu_list_name = "Categories"
        end
      end
      @seo = SeoList.where(label: params[:landing]+'_landing').first
    else
      redirect_to root_url,notice: 'The page you requested cannot be found.'
    end
  end

  def feedback

  end

  def post_freshdesk_ticket
    response = HTTParty.post("#{MIRRAW_DESKTOP_DOMAIN}/pages/post_freshdesk_ticket", :body=>{mobile_params: params})
    redirect_to :back, notice: response['message']
  end

  def help_center
    if @curr_account.present? && (current_user.blank? || (current_user.try(:email) != request.headers['Uid']))
      sign_in(:account, @curr_account)
    end
    if current_account
      orders = Order.unscoped.
                    preload(designs: :master_image).
                    where(user_id: current_account.user.id).
                    where('created_at >= ?', 3.month.ago.to_date).
                    order('created_at DESC')
      @order_details = {}
      country_hex = {}
      orders.each do |order|
        next if order.designs.blank? 
        unless (hex_code = country_hex[order.country_code])
          hex_code = country_hex[order.country_code] =  CurrencyConvert.currency_convert_cache_by_country_code(order.country_code).try(:hex_symbol) || order.currency_code
        end
        total = ApplicationController.helpers.get_price_with_symbol_currency(order.total,hex_code,false, order.currency_rate)
        if order.state == 'sane'
          order.state = 'Confirmed'
        else
          order.state = 'Not yet Confirmed'
        end
        @order_details[order.number] = {id: order.id,items: order.designs.collect(&:title).join(', '), item_count: order.designs.count,total: total, state: order.state.titleize, created_at: order.created_at.strftime('%d %b %Y'), country: order.country, image_link: order.designs.first.master_image.photo('thumb')}
      end
    end
    @help_center_region = %w(inr rs).include?(@symbol.downcase) ? "domestic" : "international"
    @seo = SeoList.where(label: 'help_center_page').first
    if @help_center_region == "domestic"
      @headers = HelpCenterHeader.preload(domestic_support_texts: :child_support_texts)
      @support_text_of = "domestic"
    else
      @headers = HelpCenterHeader.preload(international_support_texts: :child_support_texts)
      @support_text_of = "international"
    end
  end

  def offer_tnc
  end

  def price_match_guarantee_tnc
  end

  def privacy
  end

  def terms
  end
  
  def prohibited
  end

  def assetlinks
    render json: File.read("#{Rails.root}/public/assetlinks.json")
  end

  def bx
  end

  def bulk_order_inquiry
  end

  def show
    @page = Page.find_by(slug: params[:slug]) || Page.find_by(id: params[:id])
    unless @page
      return redirect_to request.referer || root_path
    end
    @playlist_ids = @page.playlists.where("? = ANY(country_code)", @country_code).order(:position).pluck(:id).presence || @page.playlists.order(:position).pluck(:id)
  end

  def playlist
    playlist = Playlist.find_by(id: params[:id])
    return render nothing: true unless playlist

    category = playlist.category

    service = Pages::Playlist.new(playlist, params, @country_code)
    search_params = service.build

    raw_data = service.fetch { |endpoint, query| get_response_data_for(endpoint, query) }
    search_data = raw_data['search'] || {}

    @playlist_results = { playlist.id => search_data }
    respond_to do |format|
      format.json { render json: @playlist_results }
      format.html do
        if request.xhr?
          render partial: 'playlist_section',
                 locals: {
                   search_data: search_data,
                   playlist: playlist,
                   category: category
                 },
                 layout: false
        else
          redirect_to '/404'
        end
      end
    end
  end
  
  private

  # Sets flash notices if redirected from API's email confirmation or API's reset password actions
  #
  def set_callback_flash
    if params[:account_confirmation_success] == 'true'
      flash[:notice] = 'Your e-mail ID is successfully confirmed!'
    elsif params[:reset_password] == 'true'
      flash[:notice] = 'Your password is successfully reset!'
    end
  end

end
