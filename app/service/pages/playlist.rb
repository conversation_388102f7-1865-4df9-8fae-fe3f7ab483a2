module Pages
  class Playlist
    SORT = {
      'top_rated' => '1',
      'l2h' => '2',
      'h2l' => '3',
      'new' => '4',
      'discount' => '5',
      'popularity' => '6',
      'bstslr' => '6',
      'default' => '7',
      'trending' => '8',
      'trending-designs' => '9',
      'popular' => '10',
      'recommended' => '11',
      'recent-30' => '14'
    }.freeze

    def initialize(playlist, params, country_code)
      @playlist = playlist
      @params = params.to_unsafe_h
      @country_code = country_code
    end

    def build
      raw_query = @playlist.search_query.to_s.strip
      @params =
        if raw_query.present?
          build_from_query(raw_query)
        elsif @playlist.designer_id.present?
          build_from_designer
        else
          build_default_filters
        end
    end

    def fetch
      yield('search', @params)
    end

    private

    def build_from_query(raw_query)
      search_params = {}
      if raw_query.include?('?')
        facets, query_str = raw_query.split('?', 2)
        search_params['facets'] = facets if facets.present? && facets.include?('-')
      else
        query_str = raw_query
      end

      if query_str.present?
        query_hash = Rack::Utils.parse_nested_query(query_str)

        if query_hash['grade_name'].present?
          grade_name = query_hash.delete('grade_name')

          parts = grade_name.split('_')

          app_name = parts[0]
          app_source = parts[1]
          @country_code = parts[2]
          type = parts[3]
          id = parts[4].to_i
          grading_tag = GradingTag.get_grading_tags_for_grade_name(
            id,
            type,
            @country_code,
            app_source,
            app_name
          )
          search_params['grade_name'] = grading_tag if grading_tag
        end

        search_params.merge!(query_hash)
      end
      append_common_filters(search_params)
    end

    def build_from_designer
      filters = { 'designer_id' => @playlist.designer_id.to_s }
      append_common_filters(filters)
    end

    def build_default_filters
      append_common_filters({})
    end

    def append_common_filters(filters)
      sort_by = @playlist.sort_by || @params[:sort_by] || 'default'
      filters['sort'] = Playlist::SORT[sort_by] || Playlist::SORT['default']
      filters['category_parent_id'] = @playlist.category_id if @playlist.category_id.present?
      filters['items_per_page'] = @playlist.playlist_page_limit.to_s || "10"
      filters['kind'] = @playlist.search_term if @playlist.search_term.present?
      filters['Country-Code'] = @country_code if @country_code.present?
      @params.merge(filters)
    end
  end
end
