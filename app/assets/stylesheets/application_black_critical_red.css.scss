//importing variables
@import 'variables_red';
@import 'fonts';
@import 'recommendation';
@import 'swiper-bundle.min';

meta {
  &.foundation-version {
    font-family: "/5.5.2/";
  }

  &.foundation-mq-small {
    font-family: "/only screen/";
    width: 0;
  }

  &.foundation-mq-small-only {
    font-family: "/only screen and (max-width: 29.9375em)/";
    width: 0;
  }

  &.foundation-mq-medium {
    font-family: "/only screen and (min-width:30em)/";
    width: 30em;
  }

  &.foundation-mq-medium-only {
    font-family: "/only screen and (min-width:30em) and (max-width:64em)/";
    width: 30em;
  }

  &.foundation-mq-large {
    font-family: "/only screen and (min-width:64.0625em)/";
    width: 64.0625em;
  }

  &.foundation-mq-large-only {
    font-family: "/only screen and (min-width:64.0625em) and (max-width:90em)/";
    width: 64.0625em;
  }

  &.foundation-mq-xlarge {
    font-family: "/only screen and (min-width:90.0625em)/";
    width: 90.0625em;
  }

  &.foundation-mq-xlarge-only {
    font-family: "/only screen and (min-width:90.0625em) and (max-width:120em)/";
    width: 90.0625em;
  }

  &.foundation-mq-xxlarge {
    font-family: "/only screen and (min-width:120.0625em)/";
    width: 120.0625em;
  }

  &.foundation-data-attribute-namespace {
    font-family: false;
  }
}

.desk_web {
  display: none;
}

@import 'newly_added_products';

html,
body {
  height: 100%;
  font-family: "Inter", sans-serif !important;
}

body {
  background: #ffffff;
  color: $text_black;
}

a:hover {
  cursor: pointer;
}

img {
  max-width: 100%;
  height: auto;
  -ms-interpolation-mode: bicubic;
}

#map_canvas {

  img,
  embed,
  object {
    max-width: none !important;
  }
}

.map_canvas {

  img,
  embed,
  object {
    max-width: none !important;
  }
}

.mqa-display {

  img,
  embed,
  object {
    max-width: none !important;
  }
}

.clearfix {
  &:before {
    content: " ";
    display: table;
  }

  &:after {
    content: " ";
    display: table;
    clear: both;
  }
}

.hide {
  display: none;
}

.invisible {
  visibility: hidden;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

img {
  display: inline-block;
  vertical-align: middle;
}

textarea {
  height: auto;
  min-height: 50px;
}

select {
  width: 100%;
}

.row {
  margin: 0 auto;
  max-width: 62.5rem;
  width: 100%;

  &:before {
    content: " ";
    display: table;
  }

  &:after {
    content: " ";
    display: table;
    clear: both;
  }

  &.collapse {
    > {

      .column,
      .columns {
        padding-left: 0;
        padding-right: 0;
      }
    }

    .row {
      margin-left: 0;
      margin-right: 0;
    }
  }

  .row {
    max-width: none;
    width: auto;

    &:before {
      content: " ";
      display: table;
    }

    &:after {
      content: " ";
      display: table;
      clear: both;
    }

    &.collapse {
      margin: 0;
      max-width: none;
      width: auto;

      &:before {
        content: " ";
        display: table;
      }

      &:after {
        content: " ";
        display: table;
        clear: both;
      }
    }
  }
}

@media only screen and (min-width: 768px) {
  .row {
    max-width: 100%;
  }
}

@media only screen {
  .small-1 {
    width: 8.33333%;
  }

  .small-5 {
    width: 41.66667%;
  }

  .small-6 {
    width: 50%;
  }

  .small-7 {
    width: 58.33333%;
  }

  .small-8 {
    width: 66.66667%;
  }

  .small-9 {
    width: 75%;
  }

  .small-10 {
    width: 83.33333%;
  }

  .small-11 {
    width: 91.66667%;
  }

  .small-12 {
    width: 100%;
  }

  .column.small-centered,
  .columns.small-centered {
    margin-left: auto;
    margin-right: auto;
    float: none;
  }

  .column.small-uncentered,
  .columns.small-uncentered {
    float: left;
    margin-left: 0;
    margin-right: 0;
  }

  .column.small-centered:last-child,
  .columns.small-centered:last-child {
    float: none;
  }

  .column.small-uncentered:last-child,
  .columns.small-uncentered:last-child {
    float: left;
  }

  .column.small-uncentered.opposite,
  .columns.small-uncentered.opposite {
    float: right;
  }

  .row {
    &.small-collapse {
      > {

        .column,
        .columns {
          padding-left: 0;
          padding-right: 0;
        }
      }

      .row {
        margin-left: 0;
        margin-right: 0;
      }
    }

    &.small-uncollapse> {

      .column,
      .columns {
        float: left;
      }
    }
  }
}

@media only screen and (min-width: 30em) {

  .column,
  .columns {
    position: relative;
    float: left;
  }

  .medium-4 {
    width: 33.33333%;
  }

  .medium-5 {
    width: 41.66667%;
  }

  .medium-6 {
    width: 50%;
  }

  .medium-7 {
    width: 58.33333%;
  }

  .medium-8 {
    width: 66.66667%;
  }

  .medium-9 {
    width: 75%;
  }

  .medium-10 {
    width: 83.33333%;
  }

  .medium-11 {
    width: 91.66667%;
  }

  .medium-12 {
    width: 100%;
  }

  .column.medium-centered,
  .columns.medium-centered {
    margin-left: auto;
    margin-right: auto;
    float: none;
  }

  .column.medium-uncentered,
  .columns.medium-uncentered {
    float: left;
    margin-left: 0;
    margin-right: 0;
  }

  .column.medium-centered:last-child,
  .columns.medium-centered:last-child {
    float: none;
  }

  .column.medium-uncentered:last-child,
  .columns.medium-uncentered:last-child {
    float: left;
  }

  .column.medium-uncentered.opposite,
  .columns.medium-uncentered.opposite {
    float: right;
  }

  .row {
    &.medium-collapse {
      > {

        .column,
        .columns {
          padding-left: 0;
          padding-right: 0;
        }
      }

      .row {
        margin-left: 0;
        margin-right: 0;
      }
    }

    &.medium-uncollapse> {

      .column,
      .columns {
        float: left;
      }
    }
  }
}

@media only screen and (min-width: 64.0625em) {

  .column,
  .columns {
    position: relative;
    float: left;
  }

  .large-1 {
    width: 8.33333%;
  }

  .large-2 {
    width: 16.66667%;
  }

  .large-3 {
    width: 25%;
  }

  .large-4 {
    width: 33.33333%;
  }

  .large-5 {
    width: 41.66667%;
  }

  .large-6 {
    width: 50%;
  }

  .large-7 {
    width: 58.33333%;
  }

  .large-8 {
    width: 66.66667%;
  }

  .large-9 {
    width: 75%;
  }

  .large-10 {
    width: 83.33333%;
  }

  .large-11 {
    width: 91.66667%;
  }

  .large-12 {
    width: 100%;
  }
}

.accordion {
  margin-bottom: 0;

  &:before {
    content: " ";
    display: table;
  }

  &:after {
    content: " ";
    display: table;
    clear: both;
  }

  .accordion-navigation,
  dd {
    display: block;
    margin-bottom: 0 !important;
  }

  .accordion-navigation.active>a,
  dd.active>a {
    background: $dark_red;
    color: #fff !important;
  }

  .accordion-navigation.active>a>.submenu {
    color: #fff !important;
  }

  .accordion-navigation>a,
  dd>a {
    display: block;
    font-size: $menu_font_size;
  }

  .accordion-navigation>a:hover,
  dd>a:hover {
    background: $dark_red;
  }

  .accordion-navigation>.content,
  dd>.content {
    display: none;
    padding: 0.9375rem;
  }

  .accordion-navigation>.content.active,
  dd>.content.active {
    display: block;
  }
}

.alert-box {
  border-style: solid;
  border-width: 1px;
  display: block;
  font-size: $cart_font;
  font-weight: normal;
  margin-bottom: 1.25rem;
  padding: 0.5rem 2rem 0.5rem 0.5rem;
  position: relative;
  transition: opacity 300ms ease-out;
  background-color: #008CBA;
  border-color: #0078a0;
  color: #FFFFFF;

  .close {
    right: 0.25rem;
    background: inherit;
    color: rgba(0, 0, 0, .7);
    font-size: 1.375rem;
    line-height: .9;
    margin-top: -0.6875rem;
    padding: 0 6px 4px;
    position: absolute;
    top: 50%;

    &:hover,
    &:focus {
      opacity: 0.5;
    }
  }

  a.close.deal-close {
    top: 31%;
  }

  &.radius {
    border-radius: 3px;
  }

  &.round {
    border-radius: 1000px;
  }

  &.success {
    background-color: #43AC6A;
    border-color: #3a945b;
    color: #FFFFFF;
  }

  &.alert {
    background-color: #f04124;
    border-color: #de2d0f;
    color: #FFFFFF;
  }

  &.secondary {
    background-color: #e7e7e7;
    border-color: #c7c7c7;
    color: #4f4f4f;
  }

  &.warning {
    background-color: #f08a24;
    border-color: #de770f;
    color: #FFFFFF;
  }

  &.info {
    background-color: white;
    border-color: #a5a5a5;
    border-width: 1px;
    color: rgba(0, 0, 0, .6);
    font-weight: bold;
    margin-bottom: 0.8em;
  }

  &.alert-close {
    opacity: 0;
  }
}

.global-timer.alert-box {
  z-index: 9;

}

.flash-message {
  padding: 0px 10px;
  text-align: center;

  .highlight-link {
    padding: 0 5px;
  }

  .global-timer {
    #offer_message_clock {
      .deal_timer {
        padding: 2px;
        margin: 0px 2px;
        border-radius: 2px;
      }
    }
  }
}

[class*="block-grid-"] {
  display: block;
  padding: 0;
  margin: 0 -0.625rem;

  &:before {
    content: " ";
    display: table;
  }

  &:after {
    content: " ";
    display: table;
    clear: both;
  }

  >li {
    display: block;
    float: left;
    height: auto;
    padding: 0 0.625rem 1.25rem;
  }
}

@media only screen {
  .small-block-grid-1>li {
    list-style: none;
    width: 100%;

    &:nth-of-type(1n) {
      clear: none;
    }

    &:nth-of-type(1n+1) {
      clear: both;
    }
  }

  .small-block-grid-2>li {
    list-style: none;
    width: 50%;

    &:nth-of-type(1n) {
      clear: none;
    }

    &:nth-of-type(2n+1) {
      clear: both;
    }
  }

  .small-block-grid-3>li {
    list-style: none;
    width: 33.33333%;

    &:nth-of-type(1n) {
      clear: none;
    }

    &:nth-of-type(3n+1) {
      clear: both;
    }
  }

  .small-block-grid-4>li {
    list-style: none;
    width: 25%;

    &:nth-of-type(1n) {
      clear: none;
    }

    &:nth-of-type(4n+1) {
      clear: both;
    }
  }
}

@media only screen and (min-width: 30em) {
  .medium-block-grid-2>li {
    list-style: none;
    width: 50%;

    &:nth-of-type(1n) {
      clear: none;
    }

    &:nth-of-type(2n+1) {
      clear: both;
    }
  }

  .medium-block-grid-3>li {
    list-style: none;
    width: 33.33333%;

    &:nth-of-type(1n) {
      clear: none;
    }

    &:nth-of-type(3n+1) {
      clear: both;
    }
  }

  .medium-block-grid-4>li {
    list-style: none;
    width: 25%;

    &:nth-of-type(1n) {
      clear: none;
    }

    &:nth-of-type(4n+1) {
      clear: both;
    }
  }

  .medium-block-grid-5>li {
    list-style: none;
    width: 20%;

    &:nth-of-type(1n) {
      clear: none;
    }

    &:nth-of-type(5n+1) {
      clear: both;
    }
  }

  .medium-block-grid-6>li {
    list-style: none;
    width: 16.66667%;

    &:nth-of-type(1n) {
      clear: none;
    }

    &:nth-of-type(6n+1) {
      clear: both;
    }
  }
}

@media only screen and (min-width: 64.0625em) {
  .large-block-grid-3>li {
    list-style: none;
    width: 33.33333%;

    &:nth-of-type(1n) {
      clear: none;
    }

    &:nth-of-type(3n+1) {
      clear: both;
    }
  }

  .large-block-grid-4>li {
    list-style: none;
    width: 25%;

    &:nth-of-type(1n) {
      clear: none;
      padding: 10px 20px 0px 0px;
    }

    &:nth-of-type(4n+1) {
      clear: both;
    }
  }

  .large-block-grid-5>li {
    list-style: none;
    width: 20%;

    &:nth-of-type(1n) {
      clear: none;
    }

    &:nth-of-type(5n+1) {
      clear: both;
    }
  }

  .large-block-grid-6>li {
    list-style: none;
    width: 16.66667%;

    &:nth-of-type(1n) {
      clear: none;
    }

    &:nth-of-type(6n+1) {
      clear: both;
    }
  }
}

button {

  &:hover,
  &:focus {
    background-color: $red_background;
    color: #FFFFFF;
  }
}


button.secondary {

  &:hover,
  &:focus {
    background-color: #b9b9b9;
    color: #333333;
  }
}


button.success,
.button.success {
  background-color: white;
  border-color: $dark_red;
  color: #FFFFFF;
}

button.success {

  &:hover,
  &:focus {
    background-color: $dark_red;
  }
}

.button.success {

  &:hover,
  &:focus {
    background-color: $dark_red;
  }
}

button.success {

  &:hover,
  &:focus {
    color: #FFFFFF;
  }
}

.button.success {

  &:hover,
  &:focus {
    color: #FFFFFF;
  }
}

button.alert,
.button.alert {
  background-color: #f04124;
  border-color: #cf2a0e;
  color: #FFFFFF;
}

button.alert {

  &:hover,
  &:focus {
    background-color: #cf2a0e;
  }
}

.button.alert {

  &:hover,
  &:focus {
    background-color: #cf2a0e;
  }
}

button.alert {

  &:hover,
  &:focus {
    color: #FFFFFF;
  }
}

.button.alert {

  &:hover,
  &:focus {
    color: #FFFFFF;
  }
}

button.small,
.button.small {
  padding: 0.875rem 1.75rem 0.9375rem 1.75rem;
  font-size: $cart_font;
}

button.radius,
.button.radius {
  border-radius: 3px;
}


button {

  &.disabled,
  &[disabled] {
    background-color: #008CBA;
    border-color: #007095;
    color: #FFFFFF;
    box-shadow: none;
    cursor: default;
    opacity: 0.7;
  }
}

.button {

  &.disabled,
  &[disabled] {
    background-color: #008CBA;
    border-color: #007095;
    color: #FFFFFF;
    box-shadow: none;
    cursor: default;
    opacity: 0.7;
  }
}

@media only screen and (min-width: 30em) {

  button,
  .button {
    display: inline-block;
  }
}

form {
  margin: 0 0 1rem;

  .row .row {
    margin: 0 -0.5rem;

    .column,
    .columns {
      padding: 0 0.5rem;
    }
  }
}

label {
  color: #4d4d4d;
  cursor: pointer;
  display: block;
  font-size: 0.875rem;
  font-weight: normal;
  line-height: 1.5;
  margin-bottom: 0;
}

.prefix,
.postfix {
  border-style: solid;
  border-width: 1px;
  display: block;
  font-size: 0.875rem;
  height: 2.3125rem;
  line-height: 2.3125rem;
  overflow: visible;
  padding-bottom: 0;
  padding-top: 0;
  position: relative;
  text-align: center;
  width: 100%;
  z-index: 2;
}

input {

  &[type="text"],
  &[type="password"],
  &[type="date"],
  &[type="datetime"],
  &[type="datetime-local"],
  &[type="month"],
  &[type="week"],
  &[type="email"],
  &[type="number"],
  &[type="search"],
  &[type="tel"],
  &[type="time"],
  &[type="url"],
  &[type="color"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    border-radius: 0;
    background-color: #FFFFFF;
    border-style: solid;
    border-width: 1px;
    border-color: #cccccc;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    color: rgba(0, 0, 0, 0.75);
    display: block;
    font-size: 0.875rem;
    height: 2.3125rem;
    margin: 0 0 1rem 0;
    padding: 0.5rem;
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-transition: border-color 0.15s linear, background 0.15s linear;
    -moz-transition: border-color 0.15s linear, background 0.15s linear;
    -ms-transition: border-color 0.15s linear, background 0.15s linear;
    -o-transition: border-color 0.15s linear, background 0.15s linear;
    transition: border-color 0.15s linear, background 0.15s linear;
  }
}

textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 0;
  background-color: #FFFFFF;
  border-style: solid;
  border-width: 1px;
  border-color: #cccccc;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.75);
  display: block;
  font-size: 0.875rem;
  height: 2.3125rem;
  margin: 0 0 1rem 0;
  padding: 0.5rem;
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: border-color 0.15s linear, background 0.15s linear;
  -moz-transition: border-color 0.15s linear, background 0.15s linear;
  -ms-transition: border-color 0.15s linear, background 0.15s linear;
  -o-transition: border-color 0.15s linear, background 0.15s linear;
  transition: border-color 0.15s linear, background 0.15s linear;
}

input {

  &[type="text"]:focus,
  &[type="password"]:focus,
  &[type="date"]:focus,
  &[type="datetime"]:focus,
  &[type="datetime-local"]:focus,
  &[type="month"]:focus,
  &[type="week"]:focus,
  &[type="email"]:focus,
  &[type="number"]:focus,
  &[type="search"]:focus,
  &[type="tel"]:focus,
  &[type="time"]:focus,
  &[type="url"]:focus,
  &[type="color"]:focus {
    background: #fafafa;
    border-color: #999999;
    outline: none;
  }
}

textarea:focus {
  background: #fafafa;
  border-color: #999999;
  outline: none;
}

::-webkit-input-placeholder,
:-moz-placeholder,
::-moz-placeholder,
:-ms-input-placeholder {
  color: $placeholder_color;
}

select {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  background-color: #FAFAFA;
  border-radius: 0;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMTJweCIgeT0iMHB4IiB3aWR0aD0iMjRweCIgaGVpZ2h0PSIzcHgiIHZpZXdCb3g9IjAgMCA2IDMiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDYgMyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PHBvbHlnb24gcG9pbnRzPSI1Ljk5MiwwIDIuOTkyLDMgLTAuMDA4LDAgIi8+PC9zdmc+);
  background-position: 100% center;
  background-repeat: no-repeat;
  border-style: solid;
  border-width: 1px;
  border-color: #cccccc;
  color: rgba(0, 0, 0, 0.75);
  font-size: 0.875rem;
  line-height: normal;
  padding: 0.5rem;
  border-radius: 0;
  height: 2.3125rem;

  &::-ms-expand {
    display: none;
  }

  &:hover {
    background-color: #f3f3f3;
    border-color: #999999;
  }

  &:disabled {
    background-color: #DDDDDD;
    cursor: default;
  }

  &[multiple] {
    height: auto;
  }
}


select {
  margin: 0 0 1rem 0;
}

input {

  &[type="checkbox"]+label,
  &[type="radio"]+label {
    display: inline-block;
    margin-left: 0.5rem;
    margin-right: 0.2rem;
    margin-bottom: 0;
    vertical-align: baseline;
  }
}

.clear-all-btn {
  color: #303030 !important;
  background-color: #eeeeee;
  font-weight: normal;
  text-decoration: none;
  line-height: 15px;
  border: none;
  font-size: 12px !important;
  margin-top: 6px;
  padding: 6px;
}

.clear-all-btn:hover,
.clear-all-btn:focus,
.clear-all-btn:active {
  color: #303030 !important;
  /* Ensures color remains unchanged */
  background-color: #eeeeee !important;
  /* Prevents background change */
  text-decoration: none;
}

.label {
  display: inline-block;
  font-weight: normal;
  line-height: 1;
  margin-bottom: auto;
  position: relative;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  padding: 0.25rem 0.5rem 0.25rem;
  font-size: 0.6875rem;
  background-color: #008CBA;
  color: #FFFFFF;

  &.radius {
    border-radius: 3px;
  }

  &.round {
    border-radius: 0px;
  }

  &.alert {
    background-color: #f04124;
    color: #FFFFFF;
  }

  &.warning {
    background-color: #9d9596;
    color: #FFFFFF;
  }

  &.success {
    background-color: #670e19;
    color: #FFFFFF;
  }

  &.secondary {
    background-color: #e7e7e7;
    color: #333333;
  }

  &.info {
    background-color: #a0d3e8;
    color: #333333;
  }
}

.orbit-container {
  background: none;
  overflow: hidden;
  position: relative;
  width: 100%;

  .orbit-slides-container {
    list-style: none;
    margin: 0;
    padding: 0;
    position: relative;
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);

    img {
      display: block;
      max-width: 100%;
    }

    >* {
      position: absolute;
      top: 0;
      width: 100%;
      margin-left: 100%;

      &:first-child {
        margin-left: 0;
      }
    }
  }

  .orbit-timer {
    position: absolute;
    top: 12px;
    right: 10px;
    height: 6px;
    width: 100px;
    z-index: 10;

    .orbit-progress {
      height: 3px;
      background-color: rgba(255, 255, 255, 0.3);
      display: block;
      width: 0;
      position: relative;
      right: 20px;
      top: 5px;
    }

    >span {
      border: solid 4px #FFFFFF;
      border-bottom: none;
      border-top: none;
      display: none;
      height: 14px;
      position: absolute;
      top: 0;
      width: 11px;
      right: 0;
    }
  }
}

@media only screen and (max-width: 29.9375em) {

  .orbit-timer,
  .orbit-next,
  .orbit-prev,
  .orbit-bullets {
    display: none;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.panel {
  // border-style: solid;
  // border-width: 1px;
  // border-color: #d8d8d8;
  // margin-bottom: 1.25rem;
  // padding: 1.25rem;
  background: #ffffff;

  > {
    :first-child {
      margin-top: 0;
    }

    :last-child {
      margin-bottom: 0;
    }
  }
}

.reveal-modal-bg {
  background: #000000;
  background: rgba(0, 0, 0, 0.45);
  bottom: 0;
  display: none;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 999;
  left: 0;
}

.reveal-modal {
  border-radius: 3px;
  display: none;
  position: absolute;
  top: 0;
  visibility: hidden;
  width: 100%;
  z-index: 1005;
  left: 0;
  background-color: #FFFFFF;
  padding: 1.875rem;
  border: solid 1px #666666;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);

  .column,
  .columns {
    min-width: 0;
  }

  > {
    :first-child {
      margin-top: 0;
    }

    :last-child {
      margin-bottom: 0;
    }
  }

  .close-reveal-modal {
    color: #AAAAAA;
    cursor: pointer;
    font-size: 2.5rem;
    font-weight: bold;
    line-height: 1;
    position: absolute;
    top: 0;
    right: 0;
    padding: 0 9px;
  }
}

@media only screen and (max-width: 29.9375em) {
  .reveal-modal {
    min-height: 100vh;
  }
}

@media only screen and (min-width: 30em) {
  .reveal-modal {
    left: 0;
    margin: 0 auto;
    max-width: 62.5rem;
    right: 0;
    width: 80%;
  }
}

@media only screen and (min-width: 30em) {
  .reveal-modal {
    top: 7.25rem;
  }
}

@media only screen and (min-width: 64em) {

  #sizeChartModal,
  #dynamicSizeChartModal,
  #shapewearChartModal {
    padding: 0px;
    top: 58% !important;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100vh !important;
    overflow-y: auto !important;

    .btn_close_top.button {
      position: absolute;
      right: 10px;
    }

    .size-box-modal {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .row.table-responsive.table-inches {
        table {
          width: 100%;
        }

        .size_chart_modal_image {
          width: 50%;
          max-width: 340px;

          img {
            width: 100%;
          }
        }
      }
    }
  }
}

@media screen and (min-width: 29.9375em) and (max-width: 64em) {

  #sizeChartModal,
  #dynamicSizeChartModal,
  #shapewearChartModal {
    top: 50% !important;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 29em;
    padding: 0px;
    height: 60% !important;
  }
}

.switch {
  border: none;
  margin-bottom: 1.5rem;
  outline: 0;
  padding: 0;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  label {
    background: #DDDDDD;
    color: transparent;
    cursor: pointer;
    display: block;
    margin-bottom: 1rem;
    position: relative;
    text-indent: 100%;
    width: 4rem;
    height: 2rem;
    transition: left 0.15s ease-out;
  }

  input {
    left: 10px;
    opacity: 0;
    padding: 0;
    position: absolute;
    top: 9px;

    +label {
      margin-left: 0;
      margin-right: 0;
    }
  }

  label:after {
    background: #FFFFFF;
    content: "";
    display: block;
    height: 1.5rem;
    left: .25rem;
    position: absolute;
    top: .25rem;
    width: 1.5rem;
    -webkit-transition: left 0.15s ease-out;
    -moz-transition: left 0.15s ease-out;
    -o-transition: translate3d(0, 0, 0);
    transition: left 0.15s ease-out;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  input:checked+label {
    background: #008CBA;

    &:after {
      left: 2.25rem;
    }
  }

  label {
    height: 2rem;
    width: 4rem;

    &:after {
      height: 1.5rem;
      width: 1.5rem;
    }
  }

  input:checked+label:after {
    left: 2.25rem;
  }

  label {
    color: transparent;
    background: #DDDDDD;

    &:after {
      background: #FFFFFF;
    }
  }

  input:checked+label {
    background: #008CBA;
  }

  &.tiny {
    label {
      height: 1.5rem;
      width: 3rem;

      &:after {
        height: 1rem;
        width: 1rem;
      }
    }

    input:checked+label:after {
      left: 1.75rem;
    }
  }
}

table {
  background: #FFFFFF;
  border: solid 1px #f1f1f1;
  margin-bottom: 1.25rem;
  table-layout: auto;

  thead {
    background: #F5F5F5;

    tr {

      th,
      td {
        font-size: $cart_font;
        font-weight: bold;
        padding: 0.5rem 0.625rem 0.625rem;
      }
    }
  }

  tr {

    th,
    td {
      font-size: $cart_font;
      padding: 0.5625rem 0.625rem;
      text-align: left;
    }
  }

  thead tr th {
    display: table-cell;
    line-height: 1.125rem;
  }

  tfoot tr {

    th,
    td {
      display: table-cell;
      line-height: 1.125rem;
    }
  }

  tbody tr {

    th,
    td {
      display: table-cell;
      line-height: 1.125rem;
    }
  }

  tr td {
    display: table-cell;
    line-height: 1.125rem;
  }
}

.tabs-content {
  margin-bottom: 1.5rem;
  width: 100%;

  &:before {
    content: " ";
    display: table;
  }

  &:after {
    content: " ";
    display: table;
    clear: both;
  }

  >.content {
    display: none;
    float: left;
    padding: 0.9375rem 0;
    width: 100%;

    &.active {
      display: block;
      float: none;
    }
  }
}

meta.foundation-mq-topbar {
  font-family: "/only screen and (min-width:30em)/";
  width: 30em;
}

.tooltip {
  background: #333333;
  color: #FFFFFF;
  display: none;
  font-size: 0.875rem;
  font-weight: normal;
  line-height: 1.3;
  max-width: 300px;
  padding: 0.75rem;
  position: absolute;
  width: 100%;
  z-index: 1006;
  left: 50%;

  >.nub {
    border-color: transparent transparent #333333 transparent;
    border: solid 5px;
    display: block;
    height: 0;
    pointer-events: none;
    position: absolute;
    top: -10px;
    width: 0;
    left: 5px;

    &.rtl {
      left: auto;
      right: 5px;
    }
  }

  &.radius {
    border-radius: 3px;
  }

  &.round {
    border-radius: 1000px;

    >.nub {
      left: 2rem;
    }
  }

  &.opened {
    border-bottom: dotted 1px #003f54 !important;
    color: #008CBA !important;
  }
}

.tap-to-close {
  color: #777777;
  display: block;
  font-size: 0.625rem;
  font-weight: normal;
}

@media only screen and (min-width: 30em) {
  .tooltip {
    >.nub {
      border-color: transparent transparent #333333 transparent;
      top: -10px;
    }

    &.tip-top>.nub {
      border-color: #333333 transparent transparent transparent;
      bottom: -10px;
      top: auto;
    }

    &.tip-left,
    &.tip-right {
      float: none !important;
    }

    &.tip-left>.nub {
      border-color: transparent transparent transparent #333333;
      left: auto;
      margin-top: -5px;
      right: -10px;
      top: 50%;
    }

    &.tip-right>.nub {
      border-color: transparent #333333 transparent transparent;
      left: -10px;
      margin-top: -5px;
      right: auto;
      top: 50%;
    }
  }
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

.text-justify {
  text-align: justify !important;
}

div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
form,
p,
blockquote,
th,
td {
  margin: 0;
  padding: 0;
}

p {
  font-weight: normal;
  line-height: 1.6;
  text-rendering: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: white;
  font-style: normal;
  font-weight: normal;
  line-height: 1.4;
  margin-bottom: 0.5rem;
  margin-top: 0.2rem;
  text-rendering: none;
}

h1 {
  font-size: 1.1875rem;
}

h2 {
  font-size: 1.6875rem;
}

h3 {
  font-size: 1.375rem;
}

h4 {
  font-size: 1.125rem;
}

hr {
  border: solid #DDDDDD;
  border-width: 1px 0 0;
  clear: both;
  height: 0;
  margin: 1.25rem 0 1.1875rem;
}

em,
i {
  font-style: italic;
  line-height: inherit;
}

strong,
b {
  font-weight: bold;
  line-height: inherit;
}

small {
  font-size: 60%;
  line-height: inherit;
}

ul,
ol,
dl {
  font-size: 1rem;
  line-height: 1.6;
  list-style-position: outside;
  margin-bottom: 1.25rem;
}

ul {
  margin-left: 1.1rem;

  &.no-bullet {
    margin-left: 0;
  }

  li {

    ul,
    ol {
      margin-left: 1.25rem;
      margin-bottom: 0;
    }
  }

  &.no-bullet {
    list-style: none;
  }
}

@media only screen and (min-width: 30em) {

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    line-height: 1.4;
  }

  h1 {
    font-size: 1.5rem;
  }

  h2 {
    font-size: 2.3125rem;
  }

  h3 {
    font-size: 1.6875rem;
  }

  h4 {
    font-size: 1.4375rem;
  }
}

.move-right,
.offcanvas-overlap {
  >.inner-wrap {
    // -webkit-transform: translate3d(15.625rem, 0, 0);
    // -moz-transform: translate3d(15.625rem, 0, 0);
    // -ms-transform: translate(15.625rem, 0);
    // -ms-transform: translate3d(15.625rem, 0, 0);
    // -o-transform: translate3d(15.625rem, 0, 0);
    // transform: translate3d(15.625rem, 0, 0);
  }

  .exit-off-canvas {
    -webkit-backface-visibility: hidden;
    box-shadow: -4px 0 4px rgba(0, 0, 0, 0.5), 4px 0 4px rgba(0, 0, 0, 0.5);
    cursor: pointer;
    transition: background 300ms ease;
    -webkit-tap-highlight-color: transparent;
    background: rgba(255, 255, 255, 0.2);
    bottom: 0;
    display: block;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1002;
  }
}

@media only screen {

  .show-for-small-only,
  .show-for-small-up,
  .show-for-small,
  .show-for-small-down,
  .hide-for-medium-only,
  .hide-for-medium-up,
  .hide-for-medium,
  .show-for-medium-down,
  .hide-for-large-only,
  .hide-for-large-up,
  .hide-for-large,
  .show-for-large-down,
  .hide-for-xlarge-only,
  .hide-for-xlarge-up,
  .hide-for-xlarge,
  .show-for-xlarge-down,
  .hide-for-xxlarge-only,
  .hide-for-xxlarge-up,
  .hide-for-xxlarge,
  .show-for-xxlarge-down {
    display: inherit !important;
  }

  .hide-for-small-only,
  .hide-for-small-up,
  .hide-for-small,
  .hide-for-small-down,
  .show-for-medium-only,
  .show-for-medium-up,
  .show-for-medium,
  .hide-for-medium-down,
  .show-for-large-only,
  .show-for-large-up,
  .show-for-large,
  .hide-for-large-down,
  .show-for-xlarge-only,
  .show-for-xlarge-up,
  .show-for-xlarge,
  .hide-for-xlarge-down,
  .show-for-xxlarge-only,
  .show-for-xxlarge-up,
  .show-for-xxlarge,
  .hide-for-xxlarge-down {
    display: none !important;
  }
}

@media only screen and (min-width: 30em) {

  .hide-for-small-only,
  .show-for-small-up,
  .hide-for-small,
  .hide-for-small-down,
  .show-for-medium-only,
  .show-for-medium-up,
  .show-for-medium,
  .show-for-medium-down,
  .hide-for-large-only,
  .hide-for-large-up,
  .hide-for-large,
  .show-for-large-down,
  .hide-for-xlarge-only,
  .hide-for-xlarge-up,
  .hide-for-xlarge,
  .show-for-xlarge-down,
  .hide-for-xxlarge-only,
  .hide-for-xxlarge-up,
  .hide-for-xxlarge,
  .show-for-xxlarge-down {
    display: inherit !important;
  }

  .show-for-small-only,
  .hide-for-small-up,
  .show-for-small,
  .show-for-small-down,
  .hide-for-medium-only,
  .hide-for-medium-up,
  .hide-for-medium,
  .hide-for-medium-down,
  .show-for-large-only,
  .show-for-large-up,
  .show-for-large,
  .hide-for-large-down,
  .show-for-xlarge-only,
  .show-for-xlarge-up,
  .show-for-xlarge,
  .hide-for-xlarge-down,
  .show-for-xxlarge-only,
  .show-for-xxlarge-up,
  .show-for-xxlarge,
  .hide-for-xxlarge-down {
    display: none !important;
  }
}

@media only screen and (min-width: 64.0625em) {

  .hide-for-small-only,
  .show-for-small-up,
  .hide-for-small,
  .hide-for-small-down,
  .hide-for-medium-only,
  .show-for-medium-up,
  .hide-for-medium,
  .hide-for-medium-down,
  .show-for-large-only,
  .show-for-large-up,
  .show-for-large,
  .show-for-large-down,
  .hide-for-xlarge-only,
  .hide-for-xlarge-up,
  .hide-for-xlarge,
  .show-for-xlarge-down,
  .hide-for-xxlarge-only,
  .hide-for-xxlarge-up,
  .hide-for-xxlarge,
  .show-for-xxlarge-down {
    display: inherit !important;
  }

  .show-for-small-only,
  .hide-for-small-up,
  .show-for-small,
  .show-for-small-down,
  .show-for-medium-only,
  .hide-for-medium-up,
  .show-for-medium,
  .show-for-medium-down,
  .hide-for-large-only,
  .hide-for-large-up,
  .hide-for-large,
  .hide-for-large-down,
  .show-for-xlarge-only,
  .show-for-xlarge-up,
  .show-for-xlarge,
  .hide-for-xlarge-down,
  .show-for-xxlarge-only,
  .show-for-xxlarge-up,
  .show-for-xxlarge,
  .hide-for-xxlarge-down {
    display: none !important;
  }
}

section.main-section {
  display: block;
}

sup.menu-tag {
  color: #ffffff;
  padding: 3px 4px;
  font-size: 10px;
  background: #bf445e;
  background-size: 100% 100%;
  border-radius: 2px;
}

#menu-side-nav {
  height: 100%;
  width: 0px;
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  background-color: #fff9fa;
  overflow-x: hidden;
  overflow-y: scroll;
  display: block;
  color: #3C4345;
  max-width: 300px;

  .menu_text_box {
    img {
      width: 100%;
      max-width: 120px;
      padding: 0 15px;
      margin: 10px 0;
    }
  }

  .close-menu {
    float: right;
    width: 30px;
    height: 30px;
    position: absolute;
    right: 0;
    top: 10px;

    &:before,
    &:after {
      position: absolute;
      content: ' ';
      height: 20px;
      width: 2px;
      background-color: #3C4345;
      right: 20px;
      top: 5px;
    }

    &:before {
      transform: rotate(45deg);
    }

    &:after {
      transform: rotate(-45deg);
    }
  }

  .menu-accordion {
    margin-left: 0px;
    display: none;
  }

  .has-submenu {
    padding: 0.6rem 1rem;
    border-bottom: 1px solid #f1f1f1;
  }

  a {
    color: #3C4345;
    letter-spacing: 1px;
    font-size: 16px;

    &:hover {
      background-color: $dark_red !important;
      color: #fff !important;
    }
  }

  .menu-content {
    padding: 0px;
  }

  .all-menu {
    display: block;
    padding: 0.6rem 1rem;
    font-size: 16px;
    font-weight: bold;
  }

  .menus_static {
    list-style-type: none;
    font-size: 15px;
    padding: 10px 0;

    li {
      border-bottom: 1px solid #f1f1f1;
      padding: 4px 0px;
      width: 85%;
      font-size: $menu_font_size;
    }
  }

  .submenu {
    font-size: 16px;
    margin-left: 5px;
    position: absolute;
    right: 14px;
    font-weight: bold;
    color: #670b19;

    &:hover {
      color: white !important;
    }
  }
}

.off-canvas-wrap.move-right:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  background: #000;
  z-index: 99;
  opacity: 0.7;
}

#branch-banner-iframe {
  top: 145px !important;
  z-index: 1 !important;

  .reviews {
    background-color: red !important;
  }
}

body.branch-banner-is-active {
  margin-top: 0 !important;
}

.turbolinks-progress-bar {
  background-color: #ccc5c5;
  z-index: 99999;
}

[class*="block-grid-"] li {
  padding: 0 0.15rem 0.35rem;
}

body {
  overflow: scroll;

  a {
    color: $dark_red;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: $text_black;
  }

  #action_buttons .action_button_btn {
    .add_to_buy_bow {
      background-color: #670b19;
      font-weight: 700;
      font-size: $big_font;
      width: 100%;
      margin-bottom: 0;
      box-shadow: 0 -1px 15px 0 rgba(0, 0, 0, 0.2);
    }
  }

  .panel_block {

    // background-color: #4c4b4b;
    // box-shadow: 0 0 0.5em #2d2d2d;
    // margin-top: 1em;
    .panel_content {
      background-color: #4c4b4b;
    }

    .panel_heading {
      padding: 0em 0.2em;
    }
  }

  .truncate {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  a.b1g1,
  .b1g1_tnc,
  .design_offer_message,
  .qpm_tnc,
  .fd_message,
  .fd_tnc {
    color: #b84224 !important;
    font-size: 14px;
    font-family: "Inter", sans-serif;
  }

  .loyalty_cashback,
  .loyalty_tnc {
    color: #b84224 !important;
    font-size: 14px;
    font-family: "Inter", sans-serif;
  }

  #pushengage_confirm {
    left: 0px !important;
    width: auto !important;

    #pushengage_close_btn {
      color: #333232;
    }
  }

  .b1g1_text,
  .bag-info {
    border-radius: 50%;
    background-color: #d44f67;
    padding: 0px 6px;
    font-size: 14px;
    font-style: italic;
    font-weight: 700;
    cursor: pointer;
    color: #fff;
  }

  .b1g1_info {
    border-radius: 2px;
    background-color: #d44f67;
    padding: 0px 6px;
    margin: 0 5px;
    width: auto;
    font-size: 14px;
    font-style: italic;
    font-weight: 700;
    cursor: pointer;

    a {
      color: #fff;
    }
  }

  .b1g1_colored {
    background-color: #d44f67;
    color: #fff;
    float: left;
    font-size: 14px;
    padding: 5px;
  }

  .discounts-text {
    border: 1px solid #c73567 !important;
    color: #f1f1f1 !important;
  }

  .discount-percent {
    padding: 3px;
  }

  .sold-out-btn {
    width: 100% !important;
    font-weight: 600;
    display: block;
    text-transform: uppercase;
    padding: 0.6rem 2rem 0.6rem 2rem;
  }
}

:-webkit-autofill {
  transition: background-color 0s ease-in-out 5000s;
  -webkit-box-shadow: 0 0 0px 1000px #4c4b4b inset !important;
  -webkit-text-fill-color: #f1f1f1 !important;

  &:hover,
  &:focus,
  &:active {
    transition: background-color 0s ease-in-out 5000s;
    -webkit-box-shadow: 0 0 0px 1000px #4c4b4b inset !important;
    -webkit-text-fill-color: #f1f1f1 !important;
  }
}

input {

  &[type="text"],
  &[type="password"],
  &[type="email"],
  &[type="number"],
  &[type="tel"],
  &[type="time"] {
    margin: 0 0 0.5em 0;
    border: 0;
    border-bottom: 1px solid #9e9e9e;
    color: #3c4345;
    box-shadow: none;
    outline: none;
  }
}

textarea,
select {
  margin: 0 0 0.5em 0;
  border-bottom: 1px solid #9e9e9e;
  // background-color: #4c4b4b;
  box-shadow: none;
  outline: none;
}

input {
  &[type="text"] {

    &:focus,
    &:hover {
      // box-shadow: 0 1px 0 0 #5d8da5;
      // border-bottom: 1px solid #5d8da5;
      // background-color: #4c4b4b;
    }
  }

  &[type="password"] {

    &:focus,
    &:hover {
      box-shadow: 0 1px 0 0 #5d8da5;
      border-bottom: 1px solid #5d8da5;
      background-color: #4c4b4b;
    }
  }

  &[type="email"] {

    &:focus,
    &:hover {
      box-shadow: 0 1px 0 0 #5d8da5;
      border-bottom: 1px solid #5d8da5;
      background-color: #4c4b4b;
    }
  }

  &[type="number"] {

    &:focus,
    &:hover {
      box-shadow: 0 1px 0 0 #5d8da5;
      border-bottom: 1px solid #5d8da5;
      background-color: #4c4b4b;
    }
  }

  &[type="tel"] {

    &:focus,
    &:hover {
      box-shadow: 0 1px 0 0 #5d8da5;
      border-bottom: 1px solid #5d8da5;
      background-color: #4c4b4b;
    }
  }

  &[type="time"] {

    &:focus,
    &:hover {
      box-shadow: 0 1px 0 0 #5d8da5;
      border-bottom: 1px solid #5d8da5;
      background-color: #4c4b4b;
    }
  }
}

textarea {

  &:focus,
  &:hover {
    // box-shadow: 0 1px 0 0 #5d8da5;
    border-bottom: 1px solid $dark_red;
    // background-color: #4c4b4b;
  }
}

select {

  &:focus,
  &:hover {
    // box-shadow: 0 1px 0 0 #5d8da5;
    border-bottom: 1px solid $dark_red;
    // background-color: #4c4b4b;
  }
}

input {

  &[type="text"]::-webkit-input-placeholder,
  &[type="password"]::-webkit-input-placeholder,
  &[type="email"]::-webkit-input-placeholder,
  &[type="number"]::-webkit-input-placeholder,
  &[type="tel"]::-webkit-input-placeholder,
  &[type="time"]::-webkit-input-placeholder {
    color: $placeholder_color;
  }
}

textarea::-webkit-input-placeholder,
select::-webkit-input-placeholder {
  color: $placeholder_color;
}

input {

  &[type="text"]::-moz-placeholder,
  &[type="password"]::-moz-placeholder,
  &[type="email"]::-moz-placeholder,
  &[type="number"]::-moz-placeholder,
  &[type="tel"]::-moz-placeholder,
  &[type="time"]::-moz-placeholder {
    color: $placeholder_color;
  }
}

textarea::-moz-placeholder,
select::-moz-placeholder {
  color: $placeholder_color;
}

input {

  &[type="text"]:-ms-input-placeholder,
  &[type="password"]:-ms-input-placeholder,
  &[type="email"]:-ms-input-placeholder,
  &[type="number"]:-ms-input-placeholder,
  &[type="tel"]:-ms-input-placeholder,
  &[type="time"]:-ms-input-placeholder {
    color: $placeholder_color;
  }
}

textarea:-ms-input-placeholder,
select:-ms-input-placeholder {
  color: $placeholder_color;
}

input {

  &[type="text"]:-moz-placeholder,
  &[type="password"]:-moz-placeholder,
  &[type="email"]:-moz-placeholder,
  &[type="number"]:-moz-placeholder,
  &[type="tel"]:-moz-placeholder,
  &[type="time"]:-moz-placeholder {
    color: $placeholder_color;
  }
}

textarea:-moz-placeholder,
select:-moz-placeholder {
  color: $placeholder_color;
}

img {
  &[data-original] {
    opacity: 1;
    transition: opacity 0.5s;
  }
}


.fi-arrow-down:before {
  content: "\f109";
}

.fi-telephone:before {
  content: "\f109";
}

.fi-arrow-up:before {
  content: "\f10c";
}

.fi-filter:before {
  content: "\f14b";
}

.fi-list:before {
  content: "\f169";
}

.fi-magnifying-glass:before {
  content: "\f16c";
  transform: scaleX(-1);
}

.fi-thumbnails:before {
  content: "\f1fa";
}

.fi-download:before {
  content: "\f143";
}

.fi-heart:before {
  content: "\f159";
}

.opera_top_margin {
  margin-top: 22px;
}

meta.foundation-data-attribute-namespace {
  font-family: false;
}

#design_title {
  margin-top: 13px;
  display: inline;
  font-size: 1.1em;
}

#design_images_opera {
  div {
    width: 100%;
    display: inline-block;
    display: none;
    text-align: center;
  }

  span {
    position: absolute;
  }
}

.opera_next {
  right: 5px;
  margin-top: 100px
}

.opera_prev {
  left: 5px;
  margin-top: 100px;
}

.opera_arrows {
  position: absolute;
}

.cc-window,
.cc-banner {
  padding: 0 !important;
}

.cc-revoke,
.cc-window {
  line-height: 1.3em !important;
}

.orbit-bullets-container {
  text-align: center;

  .orbit-bullets {
    float: none;
    margin: 0 auto 6px auto;
    overflow: hidden;
    position: relative;
    text-align: center;
    top: 5px;

    li {
      background: #CCCCCC;
      cursor: pointer;
      display: inline-block;
      float: none;
      height: 0.4rem;
      margin-right: 6px;
      width: 0.4rem;
      border-radius: 1000px;
    }

    .active {
      background: $dark_red;
      color: #fff !important;
    }
  }
}

.wchat {
  position: fixed;
  bottom: 25%;
  right: 0%;
  z-index: 9999;
}

figure.chat {
  margin: 0;
  padding: 8px 3px 0 6px;
  border-bottom: 1px solid;
  border-left: 1px solid;
  border-top: 1px solid;
  border-radius: 10px 0px 0px 10px;
  background: #fff;

  .fa-whatsapp {
    margin-bottom: 0px;
  }
}

figcaption.chat {
  white-space: nowrap;
  font-size: 13px;
  font-weight: bold;
  text-align: left;
}

.page_not_found_suggestion {
  width: 100%;
  max-width: 1300px;
  margin: 0 auto
}

.page_not_found_suggestion img {
  border-radius: 10px
}

.page_not_found_suggestion a {
  font-size: 1rem;
  color: #777 !important
}

.page_not_found_suggestion .design-col1 {
  padding: 3px 0 5px
}

.no-design-found {
  padding: 70px 0 30px;
  color: maroon;
  font-size: 2.4rem;
  font-weight: 500
}

.label-custom-color-desktop {
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: inline-block;
  position: relative;
  top: 2px;
  opacity: 0.8;
  border: darkgrey 1px solid;
}

.multicolor-value {
  background: -webkit-linear-gradient(45deg, #edf1ee 0%, #8BC34A 25%, #FFEB3B 25%, #FFC107 40%, #FF5722 40%, #E91E63 60%, #673AB7 60%, #3F51B5 75%, #03A9F4 75%, #040404 100%);
  background: linear-gradient(45deg, #edf1ee 0%, #8BC34A 25%, #FFEB3B 25%, #FFC107 40%, #FF5722 40%, #E91E63 60%, #673AB7 60%, #3F51B5 75%, #03A9F4 75%, #040404 100%);
}

@media only screen and (max-width: 767px) {
  .no-design-found {
    padding: 30px 0 0;
    font-size: 2rem
  }

  .footer-info #popular_search tr {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    width: 100%;
  }

  .footer-info #popular_search tr td {
    width: 33%;
    font-size: 12px !important;
  }

  #menu-side-nav a.side-logo:hover {
    background-color: transparent !important;
  }

  .pdp-whatsapp-logo .wchat {
    bottom: 32% !important;
  }
}

.no-design-subtext {
  font-size: 1.2rem;
  padding-bottom: 10px
}

.tnc-in-detail {
  display: none;
  background-color: transparent;
  height: 80%;
  overflow: hidden;
  width: 90%;
  margin: 10% 5%;
  outline: none;

  @media only screen and (min-width: 767px) {
    width: 65%;
    margin: 10% 18%;
    height: 65%;
  }

  .modal-dialog {
    .modal-content {
      height: 100%;
      width: 100%;
      position: absolute;

      .modal-header {
        float: right;
        position: inherit;
        right: 5px;

        @media only screen and (min-width: 767px) {
          right: 15px;
        }
      }

      .modal-body {
        padding: 16px;
        font-size: 12px;
        overflow: auto;
        height: 100%;
        background: white;
        padding-bottom: 40px;

        @media only screen and (min-width: 767px) {
          margin-top: 0rem;
        }

        .modal-text {
          .qsn {
            font-weight: bold;
          }

          .ans {
            margin-bottom: 20px;
          }

          ul {
            font-size: 12px;
          }
        }
      }
    }

    .modal-footer {
      text-align: center;
      background: $red_background;
      padding: 15px 0px;
      position: inherit;
      width: 100%;
      bottom: 0;
      box-shadow: 2px -4px 20px 0px grey;
      color: $text_white !important;
      font-weight: bold;

      @media only screen and (min-width: 1088px) {
        display: none;
        margin-left: 160px;
      }
    }
  }
}

.off-canvas-wrap.move-right:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  background: #000;
  z-index: 99;
  opacity: 0.7;
}

#more_like_category {
  max-width: 60rem;
  margin: auto;

  .title-block {
    display: flex;
    justify-content: space-between;

    .view_all {
      color: #b11f2b !important;
      font-weight: bolder;
      font-size: small;
      min-width: 50px;
    }
  }

  .item-container {
    display: grid;

    .list-item {
      display: grid;
      padding-bottom: 1.2rem;
      grid-template-rows: min-content;
      text-align: center;
      grid-auto-flow: column;
      grid-auto-columns: 45%;
      column-gap: 1.6rem;
      overflow-x: auto;

      @media only screen and (min-width: 368px) {
        grid-auto-columns: 52%;
      }

      @media only screen and (min-width: 468px) {
        grid-auto-columns: 38%;
      }

      @media only screen and (min-width: 568px) {
        grid-auto-columns: 20%;
      }
    }

    .custom-discount,
    .percent_discount {
      display: inline-flex;
      color: #F44336;
    }

    .details_block.custom-home-page,
    .percent_discount,
    .p-detail-box {
      font-weight: 600;
      color: #423f3f;
      width: 100%;
      text-wrap: wrap;
      flex-wrap: wrap;

      .actual_price {
        margin: 0 4px;
      }
    }
  }

}

#unbxd_rec {
  max-width: 60rem;
  margin: auto;

  .title-block {
    display: flex;
    justify-content: space-between;

    .view_all {
      color: #b11f2b !important;
      font-weight: bolder;
      font-size: small;
      min-width: 50px;
    }
  }

  .item-container {
    display: grid;

    .list-item {
      display: grid;
      padding-bottom: 1.2rem;
      grid-template-rows: min-content;
      text-align: center;
      grid-auto-flow: column;
      grid-auto-columns: 45%;
      column-gap: 1.6rem;
      overflow-x: auto;

      @media only screen and (min-width: 368px) {
        grid-auto-columns: 52%;
      }

      @media only screen and (min-width: 468px) {
        grid-auto-columns: 38%;
      }

      @media only screen and (min-width: 568px) {
        grid-auto-columns: 20%;
      }
    }

    .custom-discount,
    .percent_discount {
      display: inline-flex;
      color: #F44336;
    }

    .details_block.custom-home-page,
    .percent_discount,
    .p-detail-box {
      font-weight: 600;
      color: #423f3f;
      width: 100%;
      text-wrap: wrap;
      flex-wrap: wrap;

      .actual_price {
        margin: 0 10px;
      }
    }

    img {
      background: pink;
      height: auto;
      max-width: 100%;
    }
  }

}

@import 'red_theme';
@import 'footer_red';
@import 'common_desk';
@import 'catalog_red';
@import 'blaze';
