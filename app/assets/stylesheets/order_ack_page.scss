.order-items {
    background: linear-gradient(to bottom, #ffffff 0.5, #fcfcfc 0.95); 
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); 
    border-radius: 6px; 
    margin: 10px auto;
    padding:5px;
    width: 100%; 

    .item:last-child {
      border-bottom: none;
    }
  
    .order-ack-header {
      font-size: 1.3125rem; 
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
  
      svg {
        color: black;
        width: 20px;
        height: 21px;
        margin-right: 8px;
      }
    }
  
    .item {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin: 5px 0;
      padding: 5px 0;
      width: 100%;
      
      .item-status{
        margin: auto;
        text-align: center;
        width: 100%;
      }
      
      p {
        margin: 0;
      }
      
      .order-right {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin: 1px 0;
        width: 100%;
        background: linear-gradient(to bottom, #ffffff 0.5, #fafafa 0.95); 
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08); 
        border-radius: 6px; 
        padding: 15px; 
        
        .order-right-d {
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 4px 0;
          width: 100%;
  
          .image-cover {
            width: 70%;
            height: 70%;
            object-fit: cover;
          }
          .image-details{
            display: flex;
            flex-direction: column;
          } 
          .status{
            display: flex;
            flex-direction: column;
          }
          .details {
            margin-left: -20px;
            display: inline-block; 
            width: 90%;
  
            p, h6 {
              margin: 0;
            }

            h6 {
              font-size: 1rem; 
            }
            .line_item_details {
              padding: 0.4em;
              color: $text_black;
              border: none;
              background-color: white;
            }
          }
        }
      }
    }

    @media screen and (max-width: 425px) {
        .order-right-d {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin: 2px 0;
            width: 100%;
        }
        .details {
            margin-top : 15px;
        } 
        .image-details{
          display: flex;
          flex-direction: column;
          text-align: center;
        } 
    }
}

.total-section {
  width: 100%;
  margin: 0 auto;
  background: linear-gradient(to bottom, #ffffff 0.1, #fafafa 0.6); 
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); 
  border-radius: 8px; 
  padding: 20px; 
  margin-bottom: 1rem;

  p {
    display: flex;
    justify-content: space-between;
    margin: 5px 0;
  }

  .order-ack-header {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.24rem; 
    font-weight: bold;
    margin-bottom: 15px;
    color: black;

    svg {
      width: 19px;
      height: 19px;
      margin-right: 8px;
      color: black;
    }
  }
}

.shipping-details {
  width: 100%;
  margin: 0 auto;
  background: linear-gradient(to bottom, #ffffff 0.1, #fafafa 0.9); 
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); 
  border-radius: 8px; 
  padding: 20px; 

  .order-ack-header {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.24rem; 
    font-weight: bold;
    margin-bottom: 15px;
    color: black;

    svg {
      width: 22px;
      height: 22px;
      margin-right: 8px;
      vertical-align: middle;
      color: black;
    }
  }

  .ship {
    margin-top: 10px;

    p {
      margin: 5px 0;
    }
  }
}
#order_show_block{
  width: 100%;
    .order-acknowledgement-font-size{
        font-size: 1.75rem;
        margin-top: 1rem;
        display: flex;
        justify-content: center;
        align-items: center;
        .custom-svg {
          width: 32px;
          height: 32px;
          margin-right: 10px;
          color: black;
        
        .st0 {
          fill: #01A601; 
        }
      }    
  }
}

#order-ack-header {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem; 
    font-weight: bold;
    color: black;
    margin-bottom: 15px;
  
    svg {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      color: black;
    }
  }
  

#order-info {
    width: 100%;
    margin: 0 auto;
    background: linear-gradient(to bottom, #ffffff 0.1, #fafafa 0.9); 
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); 
    border-radius: 8px; 
    padding: 20px; 
    margin-bottom: 1rem;
    .order-info-details{
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.25rem; 
      font-weight: bold;
      color: black;
      margin-bottom: 15px;
    
      svg {
        width: 20px;
        height: 20px;
        margin-right: 8px;
        color: black;
      }
    }
    .order-details {
      display: flex;
      flex-direction: column;
      font-size: 1rem;
      margin-bottom: 1rem;
      width: 100%;
      margin-top:2rem;
    }
 
  @media screen and (max-width: 768px) {
      .order-details {
          flex-direction: column; 
          align-items: flex-start; 
      }
  }
}

#order-status-ack{
  background: linear-gradient(to bottom, #ffffff 0.5, #f9f9f9 0.95); 
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.08); 
  border-radius: 8px; 
  padding: 20px;
  margin: 0 auto; 
  width: 100%; 
  margin-bottom: 1.5rem;

  #order-ack-header{
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem; 
    font-weight: bold;
    color: black;
    margin-bottom: 18px;
  
    svg {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      color: black;
    }  
  }
  .progress-bar-container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative; 

    .steps-wrapper {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
    
      .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
          
        .number-container {
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          .circle {
            border: 1px solid black;
            padding: 10px;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            position: relative;
          
            &.yellowgreen {
              background-color: yellowgreen;
              color: white;
            }
          
            &.white {
              background-color: white;
              color: black;
            }
          
            &:not(.yellowgreen):not(.processing):not(.white) {
              background-color: orange; 
              color: black;
            }
          
            .step-number {
              font-size: 14px;
            }
          }
        }
        
        .step-label {
          display: block;
          white-space: normal;
          word-wrap: break-word;
          overflow-wrap: break-word; 
          text-align: center;
          padding-top: 5px;
          padding-bottom: 5px;
          max-width: 100px; 
          margin: 0 auto;
        }
        
        .status-note span {
          font-size: 12px;
          font-weight: 400;
          text-align: center;
          display: block;
          
          .status-note-done{
            color:green
          }
          .status-note-pending{
            color:gray
          }
          .status-note-processing{
            color:orange
          }
        }
        
      }
      .vertical-line {
        background-color: #898484;
        height: 3px;
        width: 5rem;
        margin: 24px 0px;
        position: relative;
        top: 2px; 
      }
    }
  }
}



