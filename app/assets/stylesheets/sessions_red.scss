@import 'variables_red';
@import 'red_theme';
body {
  font-family: "Inter", sans-serif !important;
}
.sign-in-links {
  text-align: center;

  .sign-in-link {
    display: inline-block;
    width: 45%;
    height: auto;
    margin: 0 4px 10px;
  
    text-align: center;
  }
}

.bordered_block {
  label {
    color:#3C4345;
  }
}
.login_btn, .sign-up-btn, .reset-password {
  background: $red_background !important;
  border-radius: 0 !important;
  text-transform: uppercase;
  font-size: 14px;
}

hr {
  border: #bdb3b3 solid;
  border-width: 0.1em 0 0;
  margin-top: 0.4em;
}

#new_account{
  label{
    color: $text_black;
  }
}
.registration-block{
  display: inline-flex;
  padding: 0.75em;
  width: 100%;
  text-align: center;
  li:first-child {
    border-right: 1px solid #8f1b1d;
  }
}

.chanepassword_page {
  height: 45vh;
  display: flex;
  align-items: center;
}
.change-password {
  padding: 20px 0;
}
.change-password h4{
  font-family: "Inter", sans-serif !important;
}

#account_login,
#signin-password-input {
  margin: 0 0 0.5em 0;
}

@media screen and (max-width:991px) {
  .chanepassword_page {
    height: 65vh;
}
}
@media only screen and (max-width: 768px){
  .chanepassword_page {
    align-items: unset;
  }
}
@media only screen and (min-width: 768px){
  #container{
    margin-top: 4.5rem !important;
  }
}

.password-field-icon{
  .password-field-div{
    position: relative;
  }
  .password-field-div .password-toggle-icon-btn{
    position: absolute;
    top: 50%;
    right: 8px; /* Adjust as needed */
    transform: translateY(-50%);
    cursor: pointer;
    font-weight: 100;
  }
  .eye-icon.password-toggle-icon-btn {
    font-weight: lighter;
    width: 20px; /* Adjust width as needed */
    height: 20px;
    fill: #3d3b3b;
  }
  }
@media screen and (max-width:991px) {
  .off-canvas-wrap .with-menu {
    margin-top: 11.3rem;
}
}
small.error.error {
  display: none;
}
// confirm password mask-unmask css
.confirm_password_field {
  position: relative;
.eye-icon {
  svg {
    width: 20px;
    position: absolute;
    top: 34px;
    right: 16px;
  }
}
}
