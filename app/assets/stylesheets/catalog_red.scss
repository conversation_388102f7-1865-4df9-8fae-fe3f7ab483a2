/*Catalog Specific CSS */
//importing variables

@import 'variables_red';
@import 'red_theme';

body {
  padding: 0;
  margin: 0;
  font-family: "Inter", sans-serif !important;
  overflow-x: hidden !important;
}

$background-colors: "rgba(170, 222, 233, 0.62)", "rgba(194, 150, 220, 0.6)", "rgba(243, 150, 189, 0.52)", "rgba(224, 164, 137, 0.66)", "rgba(171, 188, 241, 0.49)", "rgba(201, 241, 210, 0.58)", "rgba(204, 169, 218, 0.51)", "rgba(206, 182, 172, 0.63)";

@each $background-color in $background-colors {
  $i: index($background-colors, $background-color);

  .design_#{$i} {
    background-color: #{$background-color};
  }
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: end;
}

.desifn_0 {
  background-color: rgba(157, 137, 220, 0.2784313725490196);
}

body.modal-open {
  overflow: hidden;
  position: fixed;
}

body {
  .heading_underline {
    text-decoration: underline;
  }

  .line_through_text {
    text-decoration: line-through;
  }

  .truncate {
    width: 100%;

    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .truncate-new {
    width: 100%;
    // max-width: 150px;
    // white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .title-container {
    height: 27px;
  }

  .store-breadcrumb {
    display: flex;

    @media only screen and (min-width: 500px) and (max-width: 1040px) {
      margin-top: 15px;
    }

    ul {
      margin-bottom: 0px;
      margin-left: 0px;
    }

    li:not(:first-child):before {
      content: '/';
      margin-left: 0px;
      margin-right: 0px;
    }

    a {
      font-size: 12px !important;
      color: $text_black;
    }

    li {
      font-size: 12px !important;
      display: inline-block;
    }

    .final {
      font-weight: bold;
    }
  }

  .store_page_design {
    margin: 0;

    >li {
      -webkit-transition: all 0.5s ease;
      -moz-transition: all 0.5s ease;
      -o-transition: all 0.5s ease;
      transition: all 0.5s ease;
      margin-bottom: 0;

      img {
        width: 100%;
        object-fit: cover;
        height: 100% !important;
        max-height: 100%;
      }

      .label-ribbon:before {
        font-family: "foundation-icons";
        content: "\f12b";
        color: $text_white !important;
        font-size: 14px;
        padding: 0px 3px;
      }

      .label-ribbon {
        border-color: $dark_red !important;
        position: absolute;
        font-weight: 500;
        font-size: 12px;
        padding: 0px 0.2em 3px 0em;
        ;
        background-color: $light_red !important;
        color: $text_white !important;
        display: inline-block;
        line-height: 1;
        margin: -6px 0px 0px -7px;
        border: 0 solid transparent;
        z-index: 1;
        display: none;
      }

      .label-ribbon:after {
        position: absolute;
        content: '';
        top: 100%;
        left: 0;
        background-color: transparent !important;
        border-style: solid;
        border-width: 0px 0.78em 1em 0px;
        border-color: transparent;
        border-right-color: inherit;
        width: 0;
        height: 0;
      }

      .b1g1 {
        &.label-ribbon:before {
          content: '';
        }
      }

      .fr_page {
        .plp-image-box {
          height: 100%;
          border-radius: 10px;
        }

        .panel {
          position: relative;
          padding: 0em 0.3em 0.3em 0.3em;

          &.design_desc {
            .row.price-box {
              padding: 5px 0;

              &:nth-child(3) {
                padding: 0px 0;
              }
            }

            margin-bottom: 0em;

            a {
              display: inline-block;
              color: #010101;
              font-size: 14px;
              font-weight: 400;
              line-height: 17px;
              padding: 8px 0px;
              width: 89%;
            }

            .offer-message-frame {
              span {
                padding: 4px 6px;
                color: #142536;
                border: none;
                background: #DCEBFE;
                font-size: 10px;
                font-weight: 500;
                line-height: 12px;
                letter-spacing: 0;
                text-align: left;
                border-radius: 3px;
                text-transform: uppercase;
                margin-right: 4px;
              }
            }

            .title-column,
            .wishlist-column {
              padding: 0;

            }

            .discount-block {
              color: #670b19;
              margin-top: 3px
            }

            .price-padding {
              font-size: 11px !important;
              padding-right: 5px;
            }

            .discount-price {
              font-size: 14px !important;
              font-weight: 600;

              @media screen and (max-width: 767x) {
                font-size: 11px !important;
              }
            }

            .wishlist-column {
              .wishlist-forms {
                .wishlist-heart-button {
                  font-size: 1.6rem;
                  margin: 0;
                  padding: 0;
                  float: right;
                  background: transparent;
                  color: #8f1b1d;
                  position: absolute;
                  right: 0;
                  top: 1px;

                  &:focus {
                    outline: none;
                  }
                }

                .wishlist-heart-button.empty-heart {
                  color: gray;
                }
              }
            }
          }

          .add_to_cart_link {
            bottom: 0em;
            color: $text_white;
            background: $add_to_cart_red;
            position: absolute;
            padding: 10px 2px;
            font-weight: 700;
          }

          .sold_out_link {
            margin-bottom: 0.7em;
            width: 89%;
            bottom: 0em;
            color: $text_white;
            font-weight: 700;
          }
        }
      }
    }

    li {
      &.original_price {
        font-size: 0.8em;
        color: white;
      }

      &.discount_price {
        font-weight: bold;
        color: white;
      }

      &.percent_off {
        color: red;
        margin-top: -1.7em;
        font-size: 0.9em;
      }
    }

    .design_desc {
      padding: 0.5em;

      li {
        padding-bottom: 0em;
      }
    }
  }

  .postfix {
    border-style: solid;
    border-width: 1px;
    display: block;
    font-size: 0.875rem;
    height: 2.3125rem;
    line-height: 2.3125rem;
    overflow: visible;
    padding-bottom: 0;
    padding-top: 0;
    position: relative;
    text-align: center;
    width: 100%;
    z-index: 2;
  }

  .postfix.button {
    border-color: true;
  }

  .postfix.button.radius {
    border-radius: 0;
    -webkit-border-bottom-right-radius: 3px;
    -webkit-border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
  }

  .postfix.button.round {
    border-radius: 0;
    -webkit-border-bottom-right-radius: 1000px;
    -webkit-border-top-right-radius: 1000px;
    border-bottom-right-radius: 1000px;
    border-top-right-radius: 1000px;
  }

  span.postfix,
  label.postfix {
    background: #f2f2f2;
    color: #333333;
    border-color: #cccccc;
  }

  .store_page_block,
  .wishlist-block {
    margin-bottom: 2em;

    #top_content {
      &.read-more {
        max-height: max-content;
      }

      font-size:0.9rem;
      text-align: justify;
      word-wrap: break-word;

      #hidden_content {
        >* {
          font-size: 0.9rem;
        }
      }

      >* {
        font-size: 0.9rem;
      }
    }

    .product-title-mobile.columns {
      display: flex;
      justify-content: space-between;
    }

    .product-name-mobile {
      width: 70% !important;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      display: block;
    }

    .product_count_mobile {
      margin-top: 7px;
      font-size: 13px;
      color: rgba(113, 111, 113, 0.96);
    }

    .add-read-more {
      font-size: 14px;
      word-break: break-word;
    }

    a#view-more-top-content,
    a#view-more-seo-post {
      color: #670b19;
      font-size: 0.9rem;
      text-align: center;
    }

    .heading_title {
      padding: 0.2rem 0rem;
      max-width: 100%;

      .columns {
        padding: 0px;
      }

      .product-title {
        font-size: 1.2rem;

        h1 {
          margin: 0px;
        }

        .product_count {
          font-size: 0.8rem;
          margin-top: 0.5rem;
        }
      }

      #toggle-design-box {
        float: right;
        text-align: right;

        span {
          font-size: 0.9rem;
          vertical-align: super;
        }

        i {
          font-size: 2rem;
          vertical-align: top;
        }
      }
    }

    .category-sort-container {
      display: flex;
      align-items: center;

      #category_links {
        overflow-x: visible;
        overflow-y: hidden;
        white-space: nowrap;
        padding: 0.3rem 0rem;
        max-width: 100%;
        margin-left: 0rem;
        display: flex;
        justify-content: space-between;
        gap: 1rem;

        .link-container {
          display: flex;
          gap: 2px 4px;
        }

        .custom {
          background-color: #fff;
          border: 1px solid #3C4345;
          color: #3C4345;
          font-size: 0.765rem;
          padding: 6px 6px;
          margin-bottom: 0.3rem;
          border-radius: 100px;
          font-weight: 500;

          &:hover {
            background: #670b19;
            color: #fff;
            border: 1px solid #670b19;
          }
        }
      }
    }

    ::-webkit-scrollbar {
      width: 3px;
      height: 5px;
    }

    ::-webkit-scrollbar-track {
      background: transparent;
    }

    ::-webkit-scrollbar-thumb {
      background-color: rgba(155, 155, 155, 0.5);
      border-radius: 20px;
      border: transparent;
    }

    #action_buttons {

      /*padding: 0 0.625rem;*/
      #short_btn {
        padding: 2px;
      }

      #filter_btn {
        padding: 2px;
      }

      .select_box {
        width: 100%;
        overflow: hidden;
        position: relative;
        height: 45px;

        #custom_sort {
          width: 100%;
          height: 100%;
          position: absolute;
          left: 10px;
          top: 0px;
          text-transform: uppercase;
          font-size: 12px;
          color: $text-black;
          padding-top: 18px;
          border-right-color: white;
          border-right-width: 2px;
          background-color: #fff;
        }

        .form_input_m_select {
          width: 100%;
          font-size: 14px;
          border: 0;
          overflow: hidden;
          text-transform: none;
          background: 0 0;
          -webkit-appearance: none;
          opacity: 0;
          font-family: "Inter", sans-serif !important;
          filter: alpha(opacity=0);
          margin-bottom: 12px;
        }
      }

      #filter-button {
        width: 100%;
        height: 40px;
        background: #fff;
        color: #303030;
        font-size: 12px;
        padding: 10px;
      }
    }

    #more-designs-loader {
      display: none;
      border: 4px dotted $dark_red;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      margin: 0 auto;
      animation: spin 2s ease-in-out infinite;
    }

    #load-more-designs-btn {
      display: none;
      font-size: 14px;
      border-radius: 2px;
      cursor: pointer;
      background-color: $red_background;
    }

    .float-Btn {
      position: fixed;
      bottom: 42px;
      right: 5px;
      z-index: 99;

      i:before {
        background-color: $red_background;
        width: 2.3rem;
        height: 2.3rem;
        line-height: 2.3rem;
        border-radius: 50%;
        color: #fff;
        text-align: center;
        font-size: 1.5rem;
        opacity: 0.95;
      }
    }

    #back-top {
      display: none;
    }

    #toggle-design-box {
      #toggle-design-icon {
        -webkit-transition: all 0.5s ease;
        -moz-transition: all 0.5s ease;
        -o-transition: all 0.5s ease;
        transition: all 0.5s ease;
      }
    }

    .navigate {
      text-align: center;
      margin-top: 5px;
      border-top: 1px solid #efefef;
      padding-top: 5px;

      .pagination {
        padding: 0 60px;
        display: flex;
        justify-content: space-between;
      }
    }

    .wishlist-forms {
      .wishlist-heart-button {
        font-size: 30px;
        margin: 2px 0;
        padding: 0 4px;
        float: right;
        background: transparent;
        color: #8f1b1d;
        position: absolute;
        right: 0;
        top: 0px;

        &:focus {
          outline: none;
        }
      }

      .wishlist-heart-button.empty-heart {
        color: gray;
      }
    }
  }

  .reveal-modal {
    overflow: scroll;
    height: 100%;
    top: 0px;
    bottom: 0px;
    margin-top: 0px;
    position: fixed;
    -webkit-overflow-scrolling: touch;

    #loader {
      display: none;
      border: 10px dotted $dark_red;
      border-radius: 50%;
      width: 60px;
      height: 60px;
      position: absolute;
      left: 43%;
      z-index: 9999;
      top: 44%;
      animation: spin 2s ease-in-out infinite;
    }
  }

  #designable_details {
    border-radius: 0px;
    height: 100%;

    .chips_maker {
      background: #000000;
      padding: 0px 4px;
      display: none;
      border-radius: 2px;

      .chip {
        display: inline-block;
        padding: 2px 10px;
        height: 25px;
        margin: 3px 2px;
        font-size: 15px;
        line-height: 20px;
        border-radius: 16px;
        background-color: #607D8B;

        .closebtn {
          padding-left: 10px;
          color: #FF9800;
          font-weight: bold;
          float: right;
          font-size: 20px;
          cursor: pointer;
        }
      }
    }

    .panel_heading {
      padding: 0px;
    }

    .panel_content.small-4 {
      padding: 0px 0px 59px 0px;
      height: 100%;
      overflow-y: scroll;
      border-radius: 0px;
      background-color: $dark_red;
    }

    .tabs-content {
      padding-bottom: 59px;
      width: 66%;
      background-color: #fff;
      height: 100%;
      margin-bottom: 0px;
      overflow-y: scroll;
      border-radius: 0px;

      .active {
        padding-bottom: 0px;
      }

      .on-off-radiobox {
        display: inline-block;
        margin-bottom: 1rem;

        label {
          color: white;
        }
      }

      .on-off-checkbox {
        display: inline-block;
        margin-bottom: 1rem;

        label {
          color: white;
        }
      }

      .on-off-checkbox.color-switch {
        margin-bottom: 0.5rem !important;
        width: 25%;

        .color-input {
          visibility: hidden;
          width: 0;
        }

        .label {
          position: relative;
          left: 20px;
          bottom: 20px;
        }

        .label-custom-color {
          width: 20px;
          height: 20px;
          box-sizing: border-box;
          margin: 0px;
          position: relative;
          border: $border_black;
        }

        .label-custom-color:after {
          content: '\2713';
          display: block;
          position: absolute;
          bottom: -3px;
          left: 2px;
          opacity: 0;
          color: whitesmoke;
          font-size: 16px;
        }

        input:checked+.label-custom-color:after {
          opacity: 1;
        }

        .multicolor-value {
          background: -webkit-linear-gradient(45deg, #edf1ee 0%, #8BC34A 25%, #FFEB3B 25%, #FFC107 40%, #FF5722 40%, #E91E63 60%, #673AB7 60%, #3F51B5 75%, #03A9F4 75%, #040404 100%);
          background: linear-gradient(45deg, #edf1ee 0%, #8BC34A 25%, #FFEB3B 25%, #FFC107 40%, #FF5722 40%, #E91E63 60%, #673AB7 60%, #3F51B5 75%, #03A9F4 75%, #040404 100%);
        }
      }

      .switch.tiny {
        label {
          height: 1rem;
          width: 2rem;
          background: #9e9696;
        }

        label:after {
          height: 0.5rem;
          width: 0.7rem;
          background: #f4f4f4;
        }

        input {
          left: 8px;
          top: 5px;
        }

        input:checked+label {
          background: $dark_red;
        }

        input:checked+label:after {
          left: 0.9rem;
        }
      }

      .label-custom {
        vertical-align: top;
        display: inline-block;
        width: 75%;
        float: right;
        font-size: $menu_font_size;

        .label {
          float: right;
        }
      }

      .label-desktop-fix {
        width: 90%;
      }
    }

    header {
      .tab-filter-fix {
        font-size: 12px;
        width: 100%;
        padding: 10px 0px 10px 5px;
        text-align: left;
        margin: 0px;
        line-height: 16px;
        text-transform: capitalize;
        background: $dark_red;

        .tiny-green {
          border-radius: 50%;
          background: white;
          color: black;
          padding: 2px 5px;
          margin-left: 3px;
          font-size: 10px;
          font-weight: 700;
        }
      }

      .active-tab {
        background-color: #841523;
      }
    }

    .short_filter_btn {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      box-shadow: 0 2px 34px -4px rgba(0, 0, 0, .7);
      background: #c1bbae;

      div {
        background-color: $text_white !important;
        color: $dark_red;
        margin: 0px;
        font-size: $big_font;
        border-right: 1px solid grey;
        padding: 18px 0px;
        font-weight: bold;
      }
    }

    .filter-desktop-fix {
      bottom: 10px;
      left: 172px;
      width: 74%;
    }

  }

  .navigate_page {
    vertical-align: middle;
    margin-bottom: 1em;
  }

  .nav-button {
    margin: 1em 1em 0 0;
  }

  #design_details {
    width: 100%;
  }

  @media only screen and (min-width: 320px) and (max-width: 1040px) {
    .details_block {
      font-size: $big_font !important;

      .actual_price {
        font-size: $menu_font_size;
      }
    }
  }

  @media only screen and (max-width: 320px) {
    .details_block {
      font-size: $small_font !important;

      .actual_price {
        font-size: 0.65rem;
      }
    }
  }

  .details_block {
    color: $text_black;
    float: left;
    text-align: left;
    font-size: $font_size !important;

    .actual_price {
      display: inline-block;
      color: rgba(113, 111, 113, 0.96);
      text-decoration: line-through;
      margin-left: 3px;
      font-weight: 400 !important;
      margin-top: 3px;
    }
  }

  .rts_plp_logo {
    background: image-url('RTS_PLP_logo.png') no-repeat;
    width: 38px;
    height: 38px;
    background-size: 40px;
    -webkit-border-radius: 99em;
    -moz-border-radius: 99em;
    border-radius: 99em;
    border: 2px solid #eee;
    box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, 0.2);
    position: relative;
    float: right;
    background-position: -3px -3px;
    margin-top: -38px;
    top: 40px;
    padding: 9px 9px;
    display: none;
  }

  body .store_page_design>li .fr_page .panel.design_desc a:empty {
    display: none !important;
  }

  .discount_new_block {
    font-size: 12px !important;
    background: image-url('sprite.png') no-repeat;
    padding: 9px 9px;
    line-height: 12px;
    position: relative;
    color: $text_white;
    width: 54px;
    height: 40px;
    float: right;
    top: 40px;
    margin-top: -40px;
    background-position: 78% 45%;
  }

  .discount_new_wrap {
    padding: 1px;
  }

  .add_new_pos {
    position: relative !important;
    width: 100% !important;
  }

  .discount_font {
    font-size: 0.7em;
    font-weight: normal;
    float: left;
    color: $text_white;
  }

  .margin-down-5 {
    margin-bottom: 5px;
  }

  #search_desktop {
    display: none;
  }

  .search_margin {}
}

.home_page_designs {
  margin: 0;

  >li {
    img {
      width: 100%;
    }

    .fr_page {

      // border: 0.75px solid #d8d8d8;
      .wishlist-remove {
        position: absolute;
        right: 4px;
        top: 4px;
        border-radius: 50px;
        height: 24px;
        width: 24px;
        border: none;
        background-color: #716f6f36;
        font-size: 16px;
        font-weight: bold;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
      }

      position: relative;

      .panel {
        position: relative;
        padding: 0em 0.3em 0.3em 0.3em;

        &.design_desc {
          margin-bottom: 0em;
          margin-top: 0.2em;

          a {
            color: $text_red;
            font-size: 0.725rem;
            font-weight: bold;
          }

          .wishlist-column {
            .wishlist-forms {
              .wishlist-heart-button {
                font-size: 1.6rem;
                margin: 0;
                padding: 0;
                float: right;
                background: transparent;
                color: #8f1b1d;
                position: absolute;
                right: 0;
                top: 4px;

                &:focus {
                  outline: none;
                }
              }

              .wishlist-heart-button.empty-heart {
                color: gray;
              }
            }
          }
        }
      }
    }
  }

  li {
    &.original_price {
      font-size: $menu_font_size;
      color: $text_black;
    }

    &.discount_price {
      font-weight: bold;
      color: $text_black;
    }

    &.percent_off {
      color: red;
      margin-top: -1.7em;
      font-size: 0.9em;
    }
  }

  .design_desc {
    li {
      padding-bottom: 0em;
    }
  }

  #design_details {
    width: 100%;
  }

  .details_block {
    color: $text_black;
  }

  .design-col1 {
    float: left;
    width: 100%;
    font-size: $font_size !important;
    font-weight: bold;
  }

  .design-col2 {
    font-size: 12px !important;
    background: image-url('sprite.png') no-repeat;
    padding: 9px 9px;
    line-height: 12px;
    position: relative;
    color: $text_white;
    background-position: 78% 45%;
    width: 54px;
    height: 40px;
    float: right;
    top: 40px;
    margin-top: -40px;
  }

  .add-to-cart-bst {
    padding: 12px 2px;
    width: 100%;
    color: $text_white;
    background: $add_to_cart_red;
    font-weight: 700;
  }

  .discount_new_wrap {
    text-align: center;
    word-wrap: break-word;
    padding-left: 0.5px;
  }

  .add_new_pos {
    position: relative !important;
    width: 100% !important;
  }

  .discount_font {
    font-size: 0.8em;
  }

  .design_price {
    font-weight: bold;
  }
}

.footer-info {
  /*border:1px dotted grey;*/
  padding: 8px;
  margin-bottom: 20px;
  margin-top: 20px;
  box-sizing: border-box;

  #seo_post {
    .seo-list-anchor {
      font-size: 14px;
      padding: 5px;
      line-height: 17px;
    }

    .seo-list-table {
      width: 70%;
      border: 1px solid white;
    }

    .seo-list-line-height {
      line-height: 30px;
    }

    .seo-list-font {
      font-size: 14px;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      color: $text_black;
      font-weight: bold;
    }

    h1 {
      font-size: 1.1875rem;
    }

    h2 {
      font-size: 1.125rem;
    }

    h3 {
      font-size: 1rem;
    }

    h4 {
      font-size: 0.9375rem;
    }

    h5 {
      font-size: 0.8125rem;
    }

    h6 {
      font-size: 0.6875rem;
    }

    p {
      font-size: $seo_p_font_size;
      color: $text_black;
      text-align: justify;
      margin-bottom: 0.9rem;

      a {
        font-weight: bold;
      }
    }

    ul,
    ol {
      font-size: $seo_p_font_size;
      color: $text_black;
      text-align: justify;
      margin-bottom: 0.6rem;
      margin-left: 1.1rem;
    }

    &.read-more {
      height: 7.8em;
      overflow: hidden;
    }
  }
}

.catalog-cert {
  position: absolute;
  width: 40px !important;
  height: 40px;
}

.catalog-rating {
  position: relative;
  float: left;
  width: 40px !important;
  height: 30px;
  margin-top: -25px;
}

.navigate_page.text-center.li_append span.first,
.navigate_page.text-center.li_append span.last {
  display: none;
}

.sorting-button {
  background-color: #fff;
}

.sorting-button li {
  position: relative;
}

.sorting-button li:after {
  position: absolute;
  content: "";
  width: 2px;
  height: 41px;
  background: #f2f2f2;
  left: 0;
  top: 0;
}

.sorting-button #custom_sort {
  top: -8px !important;
}

@media only screen and (min-width: 64.063em) {
  .search_margin {
    display: none;
  }

  #search_desktop {
    display: block;
  }
}

.flickr_pagination_store {
  text-align: center;
  margin-top: 1rem;

  .pagination.prev,
  .pagination.next {
    display: inline-flex;
    border-color: #585858;
    cursor: pointer;
  }

  .prev {
    margin-right: 8px;
    border: 0px !important;
    float: left;

    &:hover {
      background: none !important;
    }

    &:before {
      content: "";
      height: 10px;
      width: 10px;
      -webkit-transform: rotate(-45deg);
      -moz-transform: rotate(-45deg);
      -ms-transform: rotate(-45deg);
      -o-transform: rotate(-45deg);
      transform: rotate(-45deg);
      margin-top: 4px;
      margin-right: 5px;
    }
  }

  .next {
    margin-left: 8px;
    border: 0px !important;
    float: right;

    &:hover {
      background: none !important;
    }

    &:after {
      content: "";
      height: 10px;
      width: 10px;
      -webkit-transform: rotate(45deg);
      -moz-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
      -o-transform: rotate(45deg);
      transform: rotate(45deg);
      margin-top: 4px;
      margin-left: 5px;
    }
  }

  .current {
    background-color: #670b19;
    color: #fff !important;
    border: 1px solid;
    border-color: #670b19;
    font-style: normal;
    font-weight: 700;
    margin: 0px 5px;
    padding: 7px 10px;
    border-radius: 100px;
    margin: 0.5rem;
    padding-left: 13px;
  }

  span.gap {
    margin: 0px 5px;
  }

  a {
    cursor: pointer;
    padding: 7px 10px;
    border-radius: 100px;
    margin: 0;
    padding-left: 13px;
    border: 1px solid transparent;
  }

  a:hover {
    background-color: transparent;
    color: #670b19 !important;
    border: 1px solid #670b19;
    border-radius: 100px;
  }
}

@media screen and (min-width: 40em) {
  #toggle-design-box {
    display: none !important;
  }
}

.rating_div {
  .small_rating {
    font-size: 12px;
    background-color: #16be48;
    color: white;
    font-weight: bold;
    border-radius: 3px;
    padding: 2px 8px;
    position: absolute;
    left: 10px;
    bottom: 10px;
  }

  .green-rating {
    background-color: #16be48;
  }

  .red-rating {
    background-color: #FF5722;
  }

  .orange-rating {
    background-color: #FFA000;
  }
}

.store_flash_deals {
  #container {
    padding: 0;
    width: 100%;
    margin: 0;
  }

  .flash-deal-header {
    margin: 0 0 0.5rem 0;
    border-bottom: 1px solid #eee;

    .fd_timer,
    .fd-header {
      font-size: $big_font;
    }

    .fd_tab {
      font-size: $font_size;
    }

    .tab-fix {
      background-color: $text_white;
      position: relative;
      text-align: center;
      display: inline-block;
      padding: 0.5rem 0.1rem;
      color: $text_red;
      margin: 0 -2px;
      border: 1px solid #eee;
    }

    .fd-active-tab {
      background-color: $red_background;
      color: $text_white;
    }

    .ongoing-fd-timer {
      margin: auto;
      display: flex;
      text-align: center;
      padding: 5px 0px;
      background: #e0cfcf;

      .ends-text {
        width: 80%;
        text-align: right;
      }

      .deal_ends {
        padding: 0;

        .countdown {
          width: 0;

          .deal_text {
            display: none;
          }

          .clock {
            padding: 0;

            .deal_timer {
              margin: 4px;
              padding: 4px;
              background: $dark_red;
              color: #fff;
              border-radius: 2px;
              font-size: $big_font;
            }
          }
        }
      }
    }
  }

  .truncate {
    a {
      color: $text_black;
    }
  }

  .fd-details-block {
    color: $text_red;
    text-align: left;

    .actual_price {

      display: inline-block;
      color: #595959;
      text-decoration: line-through;
      margin-left: 3px;
      font-weight: 100 !important;
    }
  }

  .percent-discount {
    .details_block {
      color: $text_black;
      float: left;
      font-weight: 400;
      text-align: left;
      padding: 0.1em 0.3em 0em 0.3em;
      font-size: 0.85em !important;
      border: 1px dashed $text_black;
    }
  }

  .add_to_cart_link,
  .sold_out_link,
  .wishlist-heart-button {
    position: absolute;
    bottom: 0;
    padding: 0.6em 2em 0.5em 2em;
    ;
    margin: 0 .5em;
    border: 1px solid $text_red;
    color: $text_red;
    left: 0;
    background: white;

    &:hover,
    &:focus {
      background: none;
    }
  }

  .fd_page {
    display: flex;
    border-bottom: 2px solid #eee;
    padding-bottom: 5px;

    img {
      border: 1px solid #eee;
    }

    .wishlist-column {
      .new_wishlist {
        .wishlist-heart-button {
          &:after {
            content: 'ADD TO WISHLIST';
            font-size: $big_font;
          }

          .fi-heart {
            display: none;
          }
        }
      }

      .delete_wishlist {
        .wishlist-heart-button {
          &:after {
            content: 'REMOVE FROM WISHLIST';
            font-size: $big_font;
          }

          .fi-heart {
            display: none;
          }
        }
      }
    }

    .only-few-left-label {
      position: absolute;
      margin-top: 1.8em;
      color: $dark_red;
    }
  }
}

/*.star-yellow:before {
      content: "\2605";
      display: inline-block;
      background: -webkit-linear-gradient(left, #FFC315 100%, #e2e0e0 0%);
      background: -o-linear-gradient(left, #FFC315 100%, #e2e0e0 0%); 
      background: -moz-linear-gradient(left, #FFC315 100%, #e2e0e0 0%); 
      background: linear-gradient(left, #FFC315 100%, #e2e0e0 0%); 
      -webkit-text-fill-color: transparent;
      color: transparent;
      -webkit-background-clip: text;
      background-clip: text;
      font-size: 115%;
    }
.star-gray:before {
      content: "\2605";
      display: inline-block;
      background: -webkit-linear-gradient(left, #e2e0e0 100%, #FFC315 0%);
      background: -o-linear-gradient(left, #e2e0e0 100%, #FFC315 0%); 
      background: -moz-linear-gradient(left, #e2e0e0 100%, #FFC315 0%); 
      background: linear-gradient(left, #e2e0e0 100%, #FFC315 0%); 
      -webkit-text-fill-color: transparent;
      color: transparent;
      -webkit-background-clip: text;
      background-clip: text;
      
      font-size: 115%;
    }
.star-half:before{
    content: "\2605";
    display: inline-block;
    background: -webkit-linear-gradient(left, #FFC315 49%, #e2e0e0 50%);
    background: -o-linear-gradient(left, #FFC315 49%, #e2e0e0 50%); 
    background: -moz-linear-gradient(left, #FFC315 49%, #e2e0e0 50%); 
    background: linear-gradient(left, #FFC315 49%, #e2e0e0 50%); 
    -webkit-text-fill-color: transparent;
    color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    font-size: 115%;
}

.star_align{
  padding: 0px;
  text-align: center;
}

*/

#FAQs {
  padding: 0.75em;
  margin-bottom: 1em;
  margin-top: 1em;
  box-sizing: border-box;
  background: #fff9fa;
  font-size: 14px;
  color: #303030;
  line-height: 25px;

  dd.accordion-navigation a {
    position: relative;
  }

  dd.accordion-navigation a:after {
    position: absolute;
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg id='icon_Right_arrow_small' data-name='icon/Right arrow/small' transform='translate(24) rotate(90)'%3E%3Cpath id='Path_102951' data-name='Path 102951' d='M1063.648,1252.947a.651.651,0,0,1,.918,0l3.459,3.471h0a1.3,1.3,0,0,1,0,1.836h0l-3.489,3.478a.651.651,0,0,1-.918-.918l3.2-3.192a.4.4,0,0,0,0-.571l-3.174-3.193A.65.65,0,0,1,1063.648,1252.947Z' transform='translate(-1053.462 -1245.758)' fill='rgba(0,0,0,0.8)'/%3E%3Crect id='Rectangle_21663' data-name='Rectangle 21663' width='24' height='24' fill='none'/%3E%3C/g%3E%3C/svg%3E%0A");
    right: 0;
    top: 0;
  }

  ol,
  ul {
    font-size: 14px;
    margin: 5px 0px 5px 0px;
    padding-left: 20px;
  }

  dt {
    font-size: 16px;
    font-weight: bold;
    padding-bottom: 1em;
  }

  .accordion-navigation {
    a {
      font-size: 1em;
      color: $dark_red;

      h4 {
        font-size: 15px;
        font-weight: bold;
      }

      p {
        font-size: 14px;
        font-weight: bold;
      }
    }
  }

  dd:not(:last-child) {
    border-bottom: 1px solid #eee;
  }

  dd>a:hover {
    background: none;
  }

  dd.active>a {
    background: none;
  }

  .accordion-navigation>.content,
  dd>.content {
    padding: 0px;
  }

}

.sort_filter_options .sort_filter_button {
  position: fixed;
  background: white;
  top: auto;
  bottom: -10px;
  left: 0;
  right: 0;
  z-index: 10;

  .select_box {
    background: #fff;
  }
}

.row.truncate.designer_name {
  font-size: 14px;
  margin-bottom: -5px;
}

.banner-container {
  border: 1px solid white;
  text-align: center;
  margin-bottom: 0.5rem;
  width: 99%;
}

.pagination-container {
  position: relative;
}

.tripple-dots.swiper-pagination.swiper-pagination-clickable.swiper-pagination-bullets.swiper-pagination-horizontal {
  position: absolute;
  bottom: 0;
  margin-bottom: -6px;
}

.swiper-pagination-bullet {
  background-color: #670b19;
}

.swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: #670b19;
}

@media only screen and (min-width: 1024px) {
  #container {
    margin-top: 8.5rem !important;
  }

  @mixin flc() {
    &::first-letter {
      text-transform: uppercase !important;
    }
  }

  @mixin btnReset() {
    border: none;
    outline: none;
    box-shadow: none;
  }

  .store_catalog_page {
    #container {
      .flex--item-1 {
        width: 20%;
        background: #ffffff;

        h5 {
          font-size: 18px;
          font-weight: 600;
        }

        .filter-accordion-wrapper {
          border-right: 2px solid #f0f2f9;
          border-left: 2px solid #f0f2f9;
          margin-bottom: 50px;

          ul {
            margin-left: 0;
          }
        }
      }

      .flex--item-2 {
        flex: 1;
      }

      #category_links {
        .link-container {
          flex-wrap: wrap;
        }
      }
    }

    .sort-container {
      .new_box.sort-by-wrap.small-4.columns.desk_web {
        display: flex;
        align-items: center;
        width: 223px;
        height: 31px;
        border: 1px solid #3C4345;
        padding-left: 4px;
        border-radius: 4px;
      }

      #sortByDropdownMenuBox {
        padding-top: 5px;
      }

      .select_sort_value {
        border: none;
        background-color: transparent;
        padding-right: 20px;
        font-family: "Inter", sans-serif !important;
      }

      .sort-by-txt {
        font-size: 0.765rem;
        color: #3C4345;
        font-weight: 500;
        text-wrap: nowrap;
      }
    }

    .ready-ship-wrapper {
      padding: 15px 0;
      border-bottom: 1px solid #0000000f;
    }

    .breadcrumb-wrapper {
      .b-item {
        display: inline;

        &:last-child {
          .b-link {
            color: #000000cf;
          }
        }
      }

      .b-item+.b-item:before {
        padding: 8px;
        color: #00000066;
        content: '/\00a0';
      }

      .b-link {
        color: #00000066;
        font-size: 14px;
        text-decoration: none;
        text-transform: capitalize;
      }

      .b-link:hover {
        color: #000;
      }
    }

    .filter-accordion-wrapper {
      .accordion {
        margin-bottom: 0;

        &:before {
          content: " ";
          display: table;
        }

        &:after {
          content: " ";
          display: table;
          clear: both;
        }

        .accordion-navigation,
        dd {
          display: block;
          margin-bottom: 0 !important;
        }

        .accordion-navigation.active>a,
        dd.active>a {
          background: white;
        }

        .accordion-navigation>a,
        dd>a {
          display: block;
          font-size: $menu_font_size;
          margin-left: 18px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .accordion-navigation>a .arrow {
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg id='icon_Right_arrow_small' data-name='icon/Right arrow/small' transform='translate(24) rotate(90)'%3E%3Cpath id='Path_102951' data-name='Path 102951' d='M1063.648,1252.947a.651.651,0,0,1,.918,0l3.459,3.471h0a1.3,1.3,0,0,1,0,1.836h0l-3.489,3.478a.651.651,0,0,1-.918-.918l3.2-3.192a.4.4,0,0,0,0-.571l-3.174-3.193A.65.65,0,0,1,1063.648,1252.947Z' transform='translate(-1053.462 -1245.758)' fill='rgba(0,0,0,0.8)'/%3E%3Crect id='Rectangle_21663' data-name='Rectangle 21663' width='24' height='24' fill='none'/%3E%3C/g%3E%3C/svg%3E%0A")
        }

        .accordion-navigation>a::after {
          width: auto;
          background-image: none !important;
          float: unset;
          padding-top: 17px;
          content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg id='icon_Right_arrow_small' data-name='icon/Right arrow/small' transform='translate(24) rotate(90)'%3E%3Cpath id='Path_102951' data-name='Path 102951' d='M1063.648,1252.947a.651.651,0,0,1,.918,0l3.459,3.471h0a1.3,1.3,0,0,1,0,1.836h0l-3.489,3.478a.651.651,0,0,1-.918-.918l3.2-3.192a.4.4,0,0,0,0-.571l-3.174-3.193A.65.65,0,0,1,1063.648,1252.947Z' transform='translate(-1053.462 -1245.758)' fill='rgba(0,0,0,0.8)'/%3E%3Crect id='Rectangle_21663' data-name='Rectangle 21663' width='24' height='24' fill='none'/%3E%3C/g%3E%3C/svg%3E%0A")
        }

        .accordion-navigation.active>a .arrow {
          transform: rotate(-180deg);
        }

        .accordion-navigation>a:hover,
        dd>a:hover {
          background: white;
        }

        .accordion-navigation>.content,
        dd>.content {
          display: none;
          padding: 0.9375rem;
        }

        .accordion-navigation>.content.active,
        dd>.content.active {
          display: block;
        }
      }

      .accordion-item {
        border: none;
        border-top: 1px solid #0000000f;
        padding: 0 16px 0 0;

        &:last-child {
          border-bottom: 1px solid #0000000f !important;
        }
      }

      .accordion-button::after {
        zoom: 1.3;
      }

      .accordion-body {
        padding: 0;
        overflow-y: auto;
        overflow-x: hidden;
        max-height: 15rem;
      }

      .filter-header-title {
        background: $white;
        border: none;
        box-shadow: none;
        border-radius: 0;
        padding: 25px 0 15px 0;
        color: #000000cc;
        text-transform: uppercase;
        font-size: 14px;
      }

      .active-dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #1daf3a;
        margin-top: -5px;
        margin-left: 5px;
      }

      .clear-btn {
        background: #fff3f3;
        border-radius: 7px;
        color: #f56a6a;
        font-size: 10px;
        text-transform: uppercase;
        border: 1px solid #f56a6a;
        padding: 5px 7px;
        position: absolute;
        right: 60px;
      }

      .common-search-box {
        width: 100%;
        border: none;
        color: #000;
        outline: none;
        font-size: 12px;
        border-radius: 8px;
        padding: 4px 10px;
        background: #f0f2f9;
        transition: all 0.2s linear;
        border: 1.5px solid #f0f2f9;

        &::-webkit-input-placeholder {
          opacity: 0.24;
          color: #000000cc;
        }

        &::-moz-placeholder {
          opacity: 0.24;
          color: #000000cc;
        }

        &:-ms-input-placeholder {
          opacity: 0.24;
          color: #000000cc;
        }

        &:-moz-placeholder {
          opacity: 0.24;
          color: #000000cc;
        }

        &:focus {
          background-color: #fff;
          border: 1.5px solid #f0f2f9;
        }
      }

      .checkbox-list {
        margin: 24px 0;
        max-height: 385px;
        overflow-y: auto;
      }

      .checkbox-item {
        margin: 16px 0;

        &:first-child {
          margin: 0;
        }
      }

      .price-range-wrap {
        margin-bottom: 20px;

        .max-box {
          margin-left: 15px;
        }
      }
    }

    .heading-sort-wrapper {
      .category-heading {
        font-size: 28px;
        color: #000;
        @include flc();
      }

      .item-count {
        font-size: 20px;
        color: #00000099;
      }

      .heading-wrap {
        width: 70%;
      }

      .sort-by-wrap {
        width: 30%;

        .sort-by-txt {
          color: #767779;
          font-size: 0.765rem;
          text-wrap: nowrap;
        }

        .sort-by-dropdown-btn {
          background-color: transparent;
          color: #1f1f1f;
          font-size: 14px;
          @include btnReset();
          min-width: 160px;
          padding-right: 25px;
          text-transform: uppercase;
        }

        .dropdown-item {
          cursor: pointer;
          color: #1f1f1f;
          font-size: 14px;
        }

        .dropdown-item.active,
        .dropdown-item:active {
          background-color: #e9ecef;
        }
      }
    }

    .product-list-wrapper {
      padding-top: 30px;
    }

    .sort-filter-drawer-wrapper {
      bottom: 0;
      width: 100%;
      display: none;
      position: fixed;
      max-width: 1366px;
      background-color: #fff;
      border-top: 1px solid #eaeaec;

      .flex-container {
        text-align: center;

        .flex-item {
          width: 50%;
        }
      }

      .divider {
        position: relative;

        &::after {
          content: '';
          right: 0;
          top: 15px;
          width: 2px;
          height: 35px;
          position: absolute;
          background-color: #00000014;
        }
      }

      .action-btn {
        width: 100%;
        padding: 10px;
        display: block;
        @include btnReset();
        background-color: $white;
      }

      .svg--icon {
        margin-top: -5px;
      }

      .txt-1 {
        font-size: 16px;
      }

      .txt-2 {
        display: block;
        font-size: 10px;
        color: #7b7b7b;
      }
    }

    .facet-link-desktop {
      display: flex;
      gap: 5px;
      align-items: start;

      .on-off-checkbox.tiny {
        display: grid;
        place-content: center;
      }

      .on-off-radiobox.tiny {
        display: grid;
        place-content: center;

        .filter-select {
          margin: 4px;
        }
      }

      .on-off-checkbox.color-switch {
        display: inline;
      }

      label.label-custom.label-desktop-fix {
        display: inline;
      }
    }

    div#action_buttons {
      display: none;
    }
  }
}

.img_placeholder {
  height: 25vw;
}

.image_placeholder {
  height: 24vw;
}

@media only screen and (min-device-width: 1024px) and (max-device-width: 1366px) and (-webkit-min-device-pixel-ratio: 2) {
  .image_placeholder {
    height: 100%;
  }

  .img_placeholder {
    height: 100%;
  }
}

@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (-webkit-min-device-pixel-ratio: 2) {
  .image_placeholder {
    height: 100%;
  }

  .img_placeholder {
    height: 100%;
  }
}

.plp-image-boxs {
  height: 100%;
}

#filter-chip-box {
  #filter-chips {
    background: transparent;
    display: inline-block;
    border-radius: 2px;
    padding: 10px 0px;

    .chip {
      display: inline-block;
      font-size: 12px;
      line-height: 14px;
      background-color: #eeeeee;
      color: #303030;
      padding: 6px 10px;
      margin-right: 5px;
      margin-bottom: 5px;
      border-radius: 5px;

      .closebtn {
        color: #303030;
        cursor: pointer;
        padding-right: 3px;
        margin-left: 4px;
      }
    }
  }
}

.single_line_div .on-off-checkbox.switch-desk.tiny,
.single_line_div .on-off-radiobox.switch-desk.tiny {
  label {
    display: none;
  }
}

.store_catalog_page .filter-accordion-wrapper .accordion-body {
  margin-left: 10px;
  margin-right: 1rem;

  a.facet-link-desktop {
    display: flex;
    align-items: start;
    gap: 5px;
    margin-bottom: 10px;
  }
}


#clear-all-btn {
  display: none;
  font-size: 12px;
  line-height: 14px;
  background-color: #eeeeee;
  color: #303030;
  padding: 6px;
  margin-right: 5px;
}


.lightning {
  display: flex;
  color: #B10D28;
  font-size: 0.9rem;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: -0.32px;
}

.deal-text {
  margin-right: 0.4rem;
}

.deal_timer_text {
  padding-right: 0.3rem
}

.discount-wishlist {
  display: inline-block;
  color: #670b19;
  font-size: 12px !important;
  font-weight: 500;
}

.wishlist-price {
  margin-right: 10px;
}

.store_page_block {
  padding-left: 20px;
  width: 80%;
}

.wishlist-block {
  .heading_title {
    padding: 1rem 0;
    border-bottom: 1px solid #f1f1f1;
    margin-bottom: 1rem;
  }

  .navigate .pagination {
    justify-content: center;

    span {
      padding: 0 10px;
    }

    .last,
    .first {
      display: none;
    }
  }

  .truncate a {
    color: #777;
    font-size: .9rem;
  }

  .design-col1.details_block {
    padding: 0px 0 5px;

    .design-col1 {
      float: left;
      width: 100%;
      font-size: 0.75rem !important;
      font-weight: bold;
    }
  }

}

body .wishlist-block .navigate .pagination {
  padding: 0 60px;
  display: flex;
  justify-content: flex-end;
}

.store_catalog_page .seo-text-box a {
  font-weight: bold;
}

@media only screen and (max-width: 991px) {

  .store_catalog_page .seo-text-box h2,
  .store_catalog_page .price-list-wrapper h2 {
    font-size: 1.125rem;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .store_page_block {
    padding-left: 0px;
  }

  .store_catalog_page .filter-accordion-wrapper .accordion-body {
    margin-left: 32px;
  }

  .store_catalog_page input[type="file"],
  input[type="checkbox"],
  input[type="radio"] {
    margin: 0px 5px 1rem 0;
  }

  .sorting-button {
    background-color: #fff;
    box-shadow: 0 0 0.4em #b7b2b2;
  }

  .sorting-button li {
    position: relative;
  }

  .sorting-button li:after {
    position: absolute;
    content: "";
    width: 2px;
    height: 41px;
    background: #f2f2f2;
    left: 0;
    top: 0;
  }

  .sorting-button #custom_sort {
    top: -8px !important;
  }
}

@media screen and (max-width:1199px) {

  body .store_page_design>li .fr_page .plp-image-box {
    height: 269px;
  }

}

@media (min-width: 991px) and (max-width: 1023px) {
  .store_page_block {
    width: 100%;
    flex: inline-start;
  }
}

@media screen and (max-width:991px) {
  .store_page_design>li .fr_page .plp-image-box {
    height: 280px;
  }

  .large-10.small-10.columns.store_page_block {
    width: 100%;
  }

  body .store_page_design>li .fr_page .plp-image-box {
    height: 277px;
  }

  .truncate-new {
    max-width: 145px;
  }
}

::-webkit-scrollbar {
  width: 3px;
  height: 5px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 20px;
  border: transparent;
}

@media screen and (max-width:767px) {
  body .store_page_design>li .fr_page .plp-image-box {
    height: 270px;
  }

  .wishlist-image {
    height: 297px;
  }

  .img_placeholder {
    height: 100%;
  }

  .image_placeholder {
    height: 100% !important;

    .img {
      height: 100% !important;
    }
  }
}

@media screen and (max-width:540px) {
  .plp-image-boxs {
    height: 255px;
  }
}

@media screen and (max-width:450px) {
  body .store_page_design>li .fr_page .plp-image-box {
    height: 300px;
  }

  .plp-image-box {
    height: 290px;
  }

  .plp-image-boxs {
    height: 295px;
  }
}

@media screen and (max-width: 430px) {
  body .store_page_design>li .fr_page .plp-image-box {
    height: 312px;
  }

  .plp-image-boxs {
    height: 306px;
  }
}

@media screen and (max-width: 414px) {
  body .store_page_design>li .fr_page .plp-image-box {
    height: 300px;
  }

  .plp-image-boxs {
    height: 293px;
  }
}

@media screen and (max-width: 400px) {
  body .store_page_design>li .fr_page .plp-image-box {
    height: 280px;
  }

  .plp-image-boxs {
    height: 277px;
  }
}

@media screen and (max-width:375px) {
  .plp-image-boxs {
    height: 267px;
  }
}

@media screen and (max-width: 360px) {
  body .store_page_design>li .fr_page .plp-image-box {
    height: 260px;
  }

  .plp-image-boxs {
    height: 255px;
  }
}

@media screen and (max-width:320px) {
  .plp-image-boxs {
    height: 100%;
  }

  .img_placeholder {
    height: 100%;
  }
}

.filter-box {
  position: sticky;
  top: 20%;
  right: 0;
  overflow-y: scroll;
  height: 80vh;

  &::-webkit-scrollbar {
    width: 3px;
    height: 5px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(155, 155, 155, 0.5);
    border-radius: 20px;
    border: transparent;
  }
}

@media screen and (max-width:991px) {
  .large-9.small-9.columns.store_page_block {
    width: 100%;
  }
}

.store_catalog_page .hidden-xs.footer-info {
  display: flex;
  justify-content: space-between;
  width: 100%;
  flex-direction: row;

  .seo-text-box {
    width: 80%;
  }

  .price-list-wrapper {
    width: 50%;
    margin-left: 30px;
  }
}

.store_catalog_page .seo-text-box h2,
.store_catalog_page .price-list-wrapper h2 {
  font-size: 1.125rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: #303030;
  margin-top: 0;
}

.footer-info th:nth-child(1) {
  width: 10%;
}

.footer-info th.title-header {
  width: 70%;
}

.footer-info th:nth-child(3) {
  width: 20%;
}

.footer-info th,
.footer-info table tbody tr td {
  font-size: 0.8rem;
}

.store_catalog_page .updated-date,
.store_catalog_page .seo-text-box p,
.store_catalog_page .seo-text-box li,
.store_catalog_page .seo-text-box h4 {
  font-size: 0.875rem;
  margin-bottom: 10px;
  margin-top: 0;
}

.store_catalog_page .seo-text-box li a {
  font-weight: bold;
}

.store_catalog_page .seo-text-box h3 {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 10px;
  margin-top: 0;
}

.store_catalog_page .seo-text-box h4 {
  display: inline-block;
  margin: 0;
  font-weight: bold;
}

body .store_page_block #top_content>*,
body .wishlist-block #top_content>* {
  font-size: 0.875rem;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

@media only screen and (max-width: 991px) {
  .hidden-xs.footer-info {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column-reverse;
  }

  .store_catalog_page .hidden-xs.footer-info {
    .price-list-wrapper {
      width: 100%;
      margin-left: 0px;
    }

    .seo-text-box {
      width: 100%;
    }
  }

  .store_catalog_page .seo-text-box h2,
  .store_catalog_page .price-list-wrapper h2 {
    font-size: 1.125rem;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .store_catalog_page .seo-text-box li,
  .store_catalog_page .seo-text-box p {
    font-size: 0.875rem;
    margin-bottom: 0px;
  }

  .price-list-wrapper {
    order: 1;
  }
}

.wishlist-block {
  padding: 40px 0 280px;
}

.wishlist-image {
  height: 360px;
}

@media screen and (max-width:1399px) {
  .wishlist-block {
    height: 100vh;
    overflow-y: auto;
    padding: 0;
  }
}

@media screen and (max-width:767px) {
  .small-12.columns.title-column.offer-message-frame {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    align-items: center;

    span {
      margin-bottom: 5px;
    }
  }

  #FAQs .accordion-navigation a h4 {
    padding-right: 30px;
  }

  .home_page_designs>li img {
    width: 100%;
    object-fit: contain;
  }

  .wishlist-image img {
    width: 100%;
    height: 307px !important;
  }
}

.filter-search-input-field {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #ced4da;
  padding-left: 10px;
  height: 36px;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 10px;
  align-items: center;

  .fi-magnifying-glass {
    font-size: 19px;
  }

  .offer-message-frame {
    display: flex;
    flex-wrap: wrap;

    span {
      margin-bottom: 4px;
    }
  }
}

.filter-search-input-field .fi-magnifying-glass {
  font-size: 19px;
  color: #6c757d;
  margin-right: 8px;
}

.filter-search-input-field .search-field {
  border: none;
  outline: none;
  height: 100%;
  padding: 0;
  margin: 0;
  width: 100%;
  font-size: 14px;
  box-sizing: border-box;
}

.filter-search-input-field .search-field::placeholder {
  color: #6c757d;
}



@media screen and (max-width: 380px) {
  .truncate-new {
    max-width: 134px;
  }
}

@media screen and (max-width:390px) {
  .wishlist-image {
    height: 270px;

    img {
      height: 267px !important;
    }
  }
}

@media screen and (max-width: 361px) {
  .wishlist-image {
    height: 258px;
  }
}

@media screen and (max-width: 321px) {
  .wishlist-image {
    height: 229px;
  }
}

.facet_follow {
  list-style-type: none;
  display: flex;
  justify-content: space-around;
}

.published_count_block,
.followers_count_block {
  text-align: center;
}

.bignumber {
  font-size: x-large;
}
