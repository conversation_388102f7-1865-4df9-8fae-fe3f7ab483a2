#playlists{
  .title{
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 20px;
  }
    max-width: 1600px;
    margin: auto;
    width :100%;

  .categories {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;


    .category-pill {
      border: 1px solid #ccc;
      padding: 8px 12px;
      color:black;
      background-color: #fff;
      font-size: 12px;
      font-weight: bold;
      text-transform: uppercase;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .category-pill:hover {
      background-color: #f5f5f5;
      border-color: #999;
    }
  }

  .playlist-card {
    width: 100%;
    max-width: 1600px;
    margin: auto;
    overflow: hidden;
    
    .spinner {
      text-align: center;
      padding: 10px;
      font-size: 14px;
      color: #666;
    }

    .product-card {
      border: 1px solid #eee;
      padding: 15px;
      background: #fff;
    }
  }

  .playlist_view {
    margin-top:10px;
    position: relative;
    margin-bottom: 48px;

    .title-block {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .category-title {
        font-size: 22px;
        font-weight: bold;
      }

      .cat-name {
        text-decoration: none;
        font-size: 14px;
        color: #333;

        &.view_all {
          font-weight: 500;
          color: #999;
        }
      }
    }

    .swiper {
      position: relative;
      overflow: visible;
      padding: 10px 0;
    }

    .swiper-wrapper {
      display: flex;
    }

    .swiper-slide {
      width: 220px !important;
      padding: 0 8px;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      overflow: visible;
    }

    .product-container {
      width: 205px;
      max-width: 260px;
      background: #fff;
      overflow: hidden;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      position: relative;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    
      &:hover {
        transform: scale(1.2);
        box-shadow: 0 12px 20px rgba(0, 0, 0, 0.2);
        z-index: 10;
      }
    
      .product-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
        transition: transform 0.3s ease;
      }
    
      &:hover .product-image {
        transform: scale(1.05);
      }
    
      .overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100%;
        background: rgba(0, 0, 0, 0.3);
        color: #fff;
        opacity: 0;
        transition: opacity 0.3s ease;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        padding: 12px;
      }
    
      &:hover .overlay {
        opacity: 1;
      }
    
      h5.truncate {
        font-size: 16px;
        margin: 0 0 6px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color:#fff;
      }
    
      .price-tag.d-flex {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        flex-wrap: wrap;
        font-size: 14px;
    
        .discount_price {
          color: white;
          font-weight: bold;
        }
    
        .actual_price.product_price_wo_discount {
          text-decoration: line-through;
          color: #d5c4c4;;
          font-size: 13px;
        }
    
        .percent_disc {
          color: white;
          font-size: 12px;
          font-weight: 500;
        }
      }
    }
    
  }

  .swiper-button-prev,
  .swiper-button-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    color: #333;
    font-size: 20px;
    font-weight: bold;
    border: none;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: background 0.3s ease;
    text-align: center;
    line-height: 36px;
  }

  .swiper-button-prev {
    left: 10px;
  }

  .swiper-button-next {
    right: 10px;
  }

  .swiper-button-prev:hover,
  .swiper-button-next:hover {
    background-color: #fff;
  }

  @media (max-width: 768px) {
    .swiper-slide {
      width: 160px !important;
    }

    .product-container {
      max-width: 160px;
    }

    .product-image {
      height: 240px;
    }

    .swiper-button-prev,
    .swiper-button-next {
      font-size: 16px;
      width: 28px;
      height: 28px;
      line-height: 28px;
    }
  }
}