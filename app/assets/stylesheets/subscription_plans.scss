* {
  transition: all 0.3s ease-in-out;
}

body {
  margin: 0;
  padding: 0;
  line-height: 1.6;
  color: #333;

  .main-section.no-search-bar {
    margin-top: 7rem;

    #container {
      margin: 0;
    }
  }
}

.hero {
  text-align: center;
  background: #fafafa;
  padding: 4rem 2rem;
}

.badge {
  display: inline-block;
  color: #D0AE58;
  padding: 0.5rem 0.8rem;
  border-radius: 20px;
  font-size: 0.875rem;
  background: #fffcfc;
  border: 1px solid #dddddd;
}

.badge.coming-soon {
  font-size: 1rem;
  background: #F9F4EC;
  border: 1px solid #F1E6CE;

  .sparkle-icon {
    margin-right: 0.3rem;
  }
}

.hero h1 {
  font-size: 3.75rem;
  margin: 1rem 0;
  color: #670b19;
}

.hero .subtitle {
  max-width: 700px;
  margin: 0 auto 1.5rem;
  color: #846262;
  font-size: 1.25rem;
}

.hero {
  .cta {
    border: none;
    padding: 1.2rem 2.5rem;
    border-radius: 0.75rem;
    color: black;
    cursor: default;
    font-weight: bold;
    background: linear-gradient(45deg, #D0AD57, #ED6F2D);
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease;
    display: none;

    &:hover {
      transform: scale(1.05);
      cursor: default;
    }

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: -100%; // keep it hidden off-screen initially
      width: 200%;
      height: 100%;
      background: linear-gradient(120deg,
          rgba(255, 255, 255, 0) 30%,
          rgba(255, 255, 255, 0.6) 50%,
          rgba(255, 255, 255, 0) 70%);
      pointer-events: none;
      opacity: 0; // invisible by default
    }

    &:hover::after {
      opacity: 1; // only visible on hover
      animation: hero-shimmer 1s linear forwards;
    }
  }
}

@keyframes hero-shimmer {
  100% {
    left: 100%;
  }
}


/* Plans Section */
.plans {
  padding: 4rem 2rem;
  background: #fff;
  text-align: center;

  h2 {
    font-size: 3rem;
    color: #670b19;
  }

  .subtitle {
    font-size: 1.25rem;
    color: #846262;
    max-width: 750px;
    margin: 0 auto;
  }

  .badge {
    padding: 0.7rem 1.5rem;
    font-size: 1rem;
    border-radius: 5rem;
    background: linear-gradient(60deg, #fffbf2, #FBEEE7);
    border-color: #F1E6CE;
    margin-top: 1rem;

    .sparkle-icon {
      margin-right: 0.5rem;
    }
  }
}

.plans .plans-container {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem;
  flex-wrap: wrap;
  max-width: 1300px;
  margin: 2rem auto;
}

.plan {
  border: 2px solid #E5DCDC;
  border-radius: 0.5rem;
  padding: 5rem 1.5rem 2rem;
  min-width: 300px;
  flex: 1;
  max-width: 430px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: linear-gradient(0deg, #F7F5F0, #FFFFFF);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0px 10px 20px 0px #afafaf;
    border-color: #D3B4B9;
  }

  .plan-tab-container {
    position: absolute;
    top: 4%;
    margin: 0 auto;
    left: 50%;
    transform: translate(-50%, 0);
    display: flex;
    white-space: nowrap;
    border: 1px solid #670b19;
    border-radius: 2rem;
  }

  .plan-tab {
    font-weight: normal;
    color: #670B19;
    padding: 0.5rem 1.5rem;
    border-radius: 4rem;
    flex: 1;
    cursor: pointer;
  }

  .active {
    background: #670B19;
    color: white;
  }

  .plan-header {
    font-size: 1.5rem;
    font-weight: bold;
    color: #670b19;

    img {
      padding: 1rem;
      background: #F4EEE9;
      border-radius: 1.4rem;
      display: block;
      margin: 2rem auto 1rem;
    }
  }

  .plan-price {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    gap: 0.3rem;

    h3 {
      font-size: 2.25rem;
      color: #670B19;
    }

    span {
      margin-bottom: 1rem;
      color: #846262;
      display: none;
    }
  }

  .plan-cycle {
    font-size: 0.875rem;
    color: #846262;
    margin-bottom: 1rem;
    display: none;
  }

  .features {
    margin: 1rem 0;

    li {
      display: flex;
      align-items: start;
      gap: 0.2rem;
    }
  }

  .tick-icon {
    margin-right: 0.5rem;
  }

  .exclusive {
    color: #d0ae58;
    border-top: 1px solid #EFE9E8;
    display: none;

    .title {
      text-align: start;
      font-weight: bold;
      margin: 0.6rem 0;

      .sparkle-icon {
        margin-right: 0.65rem;
      }
    }

    li {
      display: flex;
      align-items: center;
      gap: 0.9rem;
    }
  }

  ul {
    list-style: none;
    text-align: start;
    margin: 0;

    li+li {
      margin: 0.5rem 0;
    }
  }

}

.plan {
  button {
    margin-top: 1.5rem;
    margin-bottom: 0;
    background: #670B19;
    border: none;
    color: white;
    padding: 1rem 1.2rem;
    border-radius: 0.75rem;
    cursor: pointer;
    width: 100%;
    font-weight: bold;
    font-size: 18px;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -75%; // start off-screen
      width: 50%;
      height: 100%;
      background: linear-gradient(80deg,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.4) 50%,
          rgba(255, 255, 255, 0) 100%);
      transform: skewX(-25deg);
    }

    &:hover::before {
      animation: shimmer 0.8s forwards;
    }

    .lightning-icon {
      margin-right: 0.7rem;
    }
  }
}

.d-none {
  display: none;
}

.plan.featured {
  border-color: #D0AE58;
  position: relative;

  .popular-badge {
    align-items: center;
    gap: 0.4rem;
    padding: 0.15rem 0.8rem;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
    background: linear-gradient(90deg, #765d02, #ff6800);
    border-radius: 5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
  }

  button {
    background: linear-gradient(to right, #e2b241, #ff5700);
  }
}

// Shimmer animation
@keyframes shimmer {
  100% {
    left: 125%; // slide across
  }
}

.comparison {
  padding: 4rem 2rem;
  background: #fdfdfd;
  text-align: center;

  h2 {
    font-size: 3rem;
    color: #670b19;
  }

  .subtitle {
    font-size: 1.25rem;
    color: #846262;
  }

  .pricing-matrix {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
    max-width: 1300px;
    margin: 0 auto;

    .matrix-header {
      background: linear-gradient(to right, #670B19, #a3222e, #670B19);
      color: white;
      text-align: center;
      padding: 2rem 1rem;

      h2 {
        color: white;
        margin: 0;
        font-size: 1.6rem;
        font-weight: bold;
      }

      p {
        margin: 0.5rem 0 0;
        font-size: 0.95rem;
        opacity: 0.9;
      }
    }

    .matrix-grid {
      display: grid;
      grid-template-columns: 2fr repeat(10, 1fr);
      border: 1px solid #eee;
      overflow: auto;

      &::-webkit-scrollbar {
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #f0f0f0;
        border-radius: 10px;
      }

      &::-webkit-scrollbar-thumb {
        background: #670b19;
        border-radius: 5rem;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #b4152d;
      }

      .matrix-row {
        display: contents;

        &:hover {
          .matrix-cell:not(:nth-last-child(5)):not(:last-child) {
            background: rgba(219, 219, 219, 0.075);
          }
        }
      }

      .matrix-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
        border-bottom: 1px solid #eee;
        border-right: 1px solid #eee;
        font-size: 1rem;
        text-align: center;

        &:first-child {
          justify-content: flex-start;
          text-align: left;
          font-weight: 500;
          color: #333;
          gap: 1rem;
          white-space: nowrap;
          min-width: 390px;
        }

        &:nth-last-child(1),
        &:nth-last-child(5) {
          background: #fff8e9;
        }

        &:last-child {
          border-right: none;
        }

        span {
          color: #D0AE58;
          background: #d0ae5833;
          padding: 0.2rem 0.5rem;
          font-size: 12px;
          border-radius: 4rem;
          font-weight: bold;
          white-space: nowrap;
        }
      }

      .header .matrix-cell {
        font-weight: bold;
        background: #fafafa;
        flex-direction: column;
        justify-content: center;

        &:nth-last-child(5),
        &:nth-last-child(1) {
          background: #fff8e9;
        }

        .plan-name {
          white-space: nowrap;
        }

        .plan-price {
          font-size: 0.75rem;
          color: #846262;
          white-space: nowrap;
          display: none;
        }
      }

      .group-title .matrix-cell {
        grid-column: 1 / -1; // span full width
        background: #fdf2f2;
        font-weight: bold;
        color: #670B19;
        gap: 0.7rem;
      }

      .highlight {
        background: #fff8e1;
        font-weight: bold;
        position: relative;

        .badge {
          display: inline-block;
          background: linear-gradient(to right, #D6B870, #EE7C41);
          color: #FFFFFF;
          font-size: 0.75rem;
          padding: 0.1rem 0.5rem;
          border-radius: 1rem;
          margin-left: 0.5rem;
          border: none;

          .sparkle-icon {
            margin-right: 0.3rem;
          }
        }
      }
    }
  }
}


.why-subscribe {
  padding: 4rem 2rem;
  background: #FAF8F4;
  text-align: center;

  h2 {
    color: #670B19;
  }
}

.why-subscribe .features {
  display: flex;
  justify-content: center;
  gap: 4rem;
  margin-top: 2rem;
}

.feature {
  max-width: 300px;

  img {
    background: #670B19;
    padding: 1rem;
    border-radius: 1rem;
  }

  .sparkle-icon {
    background: linear-gradient(120deg, #D3A753, #E87934)
  }

  h3 {
    font-size: 1.25rem;
    margin: 1rem 0;
  }

  p {
    font-size: 1rem;
    color: #846262;
  }
}

/* ============================= */
/* 📱 Responsive Media Queries   */
/* ============================= */

/* Tablets (≤1024px) */
@media (max-width: 1024px) {
  .hero {
    padding: 3rem 1.5rem;
  }

  .hero h1 {
    font-size: 2.75rem;
  }

  .hero .subtitle {
    font-size: 1.1rem;
    max-width: 600px;
  }

  .plans h2,
  .comparison h2,
  .why-subscribe h2 {
    font-size: 2.25rem;
  }

  .plans .plans-container {
    gap: 1.5rem;
  }

  .plan {
    padding: 4rem 1.2rem 2rem;
  }

  .plan .plan-price span {
    margin-bottom: 0.8rem;
  }

  .plan .plan-header img {
    margin: 1.5rem auto 1rem;
    padding: 0.8rem;
  }

  .plan .plan-price h3 {
    font-size: 1.9rem;
  }

  .plan button {
    font-size: 16px;
    padding: 0.9rem 1rem;
  }

  .comparison .pricing-matrix .matrix-grid .matrix-cell {
    font-size: 0.9rem;
    padding: 0.8rem;
  }

  .why-subscribe .features {
    gap: 2rem;
  }
}

/* Mobiles (≤768px) */
@media (max-width: 768px) {
  .hero {
    padding: 2.5rem 1rem;
  }

  .hero h1 {
    font-size: 2.25rem;
  }

  .hero .subtitle {
    font-size: 1rem;
    max-width: 90%;
  }

  .plans h2,
  .comparison h2,
  .why-subscribe h2 {
    font-size: 2rem;
  }

  .plans .plans-container {
    flex-direction: column;
    margin: 1rem auto;
  }

  .plan {
    min-width: auto;
    max-width: 100%;
    padding: 5rem 1rem 1.5rem;
  }

  .plan .plan-price span {
    margin-bottom: 0.7rem;
  }

  .plan .plan-price h3 {
    font-size: 1.75rem;
  }

  .plan .plan-cycle {
    font-size: 0.8rem;
  }

  .plan button {
    font-size: 15px;
    padding: 0.8rem 1rem;
  }

  .comparison .pricing-matrix {
    border-radius: 0.25rem;
    box-shadow: none;
  }

  .comparison .pricing-matrix .matrix-header h2 {
    font-size: 1rem;
  }

  .comparison .pricing-matrix .matrix-header p {
    font-size: 0.7rem;
  }

  .comparison .pricing-matrix .matrix-grid .matrix-cell {
    font-size: 0.85rem;
    padding: 0.7rem;
  }

  .why-subscribe .features {
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 1.5rem;
  }

  .feature {
    max-width: 100%;
  }

  .feature h3 {
    font-size: 1.1rem;
  }

  .feature p {
    font-size: 0.9rem;
  }
}

/* Small Mobiles (≤480px) */
@media (max-width: 480px) {
  .hero h1 {
    font-size: 1.9rem;
  }

  .hero .cta {
    padding: 1rem 0.7rem;
    font-size: 0.7rem;
  }

  .hero .subtitle {
    font-size: 0.9rem;
  }

  .badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.7rem;
  }

  .plans {
    padding: 2rem 1rem;
  }

  .plans .subtitle,
  .comparison .subtitle {
    font-size: 0.9rem;
  }

  .plans h2,
  .comparison h2,
  .why-subscribe h2 {
    font-size: 1.6rem;
  }

  .plan {
    padding: 4rem 0.8rem 1.2rem;
  }

  .plan .plan-price span {
    margin-bottom: 0.6rem;
  }

  .plan .plan-tab {
    padding: 0.5rem 1rem;
    top: 3%;
    font-size: 0.875rem;
  }

  .plan .plan-header {
    font-size: 1.25rem;
  }

  .plan .plan-price h3 {
    font-size: 1.5rem;
  }

  .plan button {
    font-size: 14px;
    padding: 0.7rem 0.9rem;
  }

  .comparison {
    padding: 2rem 1rem;
  }

  .comparison .pricing-matrix .matrix-grid .matrix-cell {
    font-size: 0.75rem;
    padding: 0.6rem;
  }

  .badge.coming-soon {
    font-size: 0.7rem;
  }

  .comparison .pricing-matrix .matrix-grid .matrix-cell:first-child {
    min-width: 285px;
  }

  .feature h3 {
    font-size: 1rem;
  }

  .feature p {
    font-size: 0.85rem;
  }
}
