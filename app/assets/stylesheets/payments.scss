@font-face {
  font-family: "PaymentFont";
  src: font-url("paymentfont-webfont.ttf") format("truetype"), font-url("paymentfont-webfont.woff") format("woff");
}

body {
  font-family: "Inter", sans-serif !important;
}

.card-block.row {
  padding: 0 1em;
  margin-bottom: 2em;

  // background-color: #f4f4f4;
  .form-control {
    background-color: transparent;
  }

  .card-info {
    .row:first-child {
      .columns {
        position: relative;

        .card-number {
          padding-right: 3em;
        }

        .card-icon {
          position: absolute;
          right: 0;
          top: 0;
        }
      }
    }
  }
}

.pf {
  font-family: "PaymentFont";
  font-size: 1.8em;
}

.pf {
  &-unknown::after {
    content: "\f012";
  }

  &-amex::after {
    content: "\f001";
  }

  &-diners::after {
    content: "\f013";
  }

  &-discover::after {
    content: "\f014";
  }

  &-mastercard::after {
    content: "\f02d";
  }

  &-visa::after {
    content: "\f045";
  }

  &-jcb::after {
    content: "\f028";
  }

  &-maestro::after {
    content: "\f02c";
  }

  &-dankort::after {
    content: "\f04f";
  }

  &-unionPay::after {
    content: "\f041";
  }
}

.payment-option {
  padding: 0.65rem;
  background-color: #f4f4f4;
  color: #303030;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 0.825rem;
  display: inherit !important;
  margin-left: 0 !important;
  width: 100%;
}

.saved-cards {
  padding: 0.65rem;
  color: #303030;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 0.825rem;
  display: inherit !important;
  margin-left: 0 !important;
  width: 100%;
}

.saved_card_message {
  padding-left: 0.65rem;
  margin-top: -0.5rem;
  display: none;
  color: #076915;
}

%span {
  display: inline-block;
  padding: 9px 28px;
}

.cvv-input {
  width: 70% !important;
  display: inline-block !important;
  margin: 0px !important;
  float: right;
  background-color: transparent !important;
}

.payment-upi-id {
  [type=text] {
    background-color: transparent;
  }
}

.offer-payment {
  font-weight: 600;
  font-size: 12px;
  padding-left: 10.4px;
  color: #76ba94;
  line-height: 1.0;
  background-color: #f4f4f4;
  padding-bottom: 5px;
  padding-right: 40px;
}


.offer-payment-background-change {
  background: #e3e3e3;
}


.payment-radio-options {
  -webkit-appearance: none;
  width: 1.15rem;
  height: 1.15rem;
  border: 1px solid #545151;
  border-radius: 50%;
  outline: none;
  display: inline-block;
  margin: 10.4px 10.4px 10.4px 0 !important;

  &:after {
    content: "✔";
    color: #5f5c5c;
    padding: 0.19rem;
  }

  &:checked {
    background-color: #8f1b1d;
    border: 1px solid #8f1b1d;

    &:after {
      color: #fff;
    }
  }
}

.row.inline-list {
  list-style-type: none;
  display: inline-flex;
  text-align: center;
  vertical-align: top;

  .columns {
    margin-left: -1rem;
    left: 1rem;
    padding: 0 0.5rem;
    display: inline-block;

    .title {
      text-transform: uppercase;
      color: #968d8d;
      margin: 0.15em 0.8em;
      font-size: 0.7rem;
    }
  }

  [type=radio] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
  }

  [type=radio]+img {
    border: 3px solid #eee;
    border-radius: 8px;
    cursor: pointer;
    width: 4em;
    height: 4em;
    padding: 0.125em;
  }

  [type=radio]:checked+img {
    border-color: #8f1b1d;
  }
}

[type=radio]:checked+label {
  background-color: #fff;
}

#WALLET,
#UPI {
  .title {
    font-size: 0.6rem;
    margin: 0px;
    padding-top: 3px;
    padding-bottom: 2px;
  }

  .columns {
    min-width: 7%;
    padding-left: unset;
  }

  .inline-list {
    li {
      margin-left: 0;
    }

    label {
      margin-right: 0.2rem;
    }
  }

  @media screen and (min-width: 320px) and (max-width: 340px) {
    .inline-list {
      li {
        margin-left: 0.6rem !important;
      }

      [type=radio]+img {
        width: 3.5em;
        height: 3.5em;
      }
    }
  }
}

.dom-payments {

  .accordion .accordion-navigation.active>a,
  .accordion dd.active>a {
    background: #e8e8e8;
  }

  .accordion {
    dd {
      margin-bottom: 0.25rem;
      border: 1px solid #eee;

      .content {
        padding: 0.25rem 0.9375rem;
      }

      &.active {
        a {
          // background: transparent;
          color: #670e19 !important;

          &:after {
            transform: rotate(-45deg);
          }
        }
      }

      a {
        background: #f4f4f4;
        color: #303030;
        display: block;
        font-family: "Inter", sans-serif;
        font-size: 0.825rem;
        padding: 0.65rem;
        text-transform: uppercase;
        font-weight: bold;

        &:after {
          content: "";
          float: right;
          width: 8px;
          height: 8px;
          border-left: 2px solid;
          border-bottom: 2px solid;
          transform: rotate(230deg);
          margin: 4px 0px;
          margin-right: 8px;
          margin-top: 6px;
          color: #670e19;
        }
      }
    }
  }
}

.payment-error {
  display: none;
  padding: 0.25rem 0.9375rem;
  color: red;
}

.dom-payment-error {
  display: none;
  color: red;
}

/* shake effect */
.shake-effect {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-fill-mode: none;
  animation-fill-mode: none;
  -webkit-animation-name: shake;
  animation-name: shake;
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
    color: #ed8f03;
  }

  20%,
  60% {
    transform: translateX(-5px);
    color: #ed8f03;
  }

  40%,
  80% {
    transform: translateX(5px);
    color: #ed8f03;
  }
}

@-webkit-keyframes shake {

  0%,
  100% {
    -webkit-transform: translateX(0);
    color: #ed8f03;
  }

  20%,
  60% {
    -webkit-transform: translateX(-5px);
    color: #ed8f03;
  }

  40%,
  80% {
    -webkit-transform: translateX(5px);
    color: #ed8f03;
  }
}
