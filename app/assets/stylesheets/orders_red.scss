// Place all the styles related to the Orders controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
@import 'variables_red';
@import 'red_theme';
@import 'selectize';
@import 'payments';
@import 'newly_added_products';

@import 'order_ack_page';
body {
  font-family: "Inter", sans-serif !important;
  color: #303030;
}

.d-flex {
  display: flex;
}

.modal-open {
  overflow: hidden !important;
}

.clearfix {
  display: none;
}

h4 {
  font-size: 20px;
  font-weight: normal;
}

form .row .row .columns {
  padding: 0.3rem 1rem
}

.row.flex-row {
  display: flex;
}

.left-column {
  flex: 1;
  margin-right: 10px;
}

.main-section #main-section #container {
  margin-top: 8.5rem;
}

.payment-container {
  display: flex;
  flex-direction: column;
}

.modal_wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: none;
  z-index: 999;
}

.modal_box {
  background: #fff;
  margin: 100px auto;
  padding: 1rem 2rem 2rem 2rem;
  position: relative;
  border-radius: 8px;
  max-width: 800px;
  width: 80%;
  max-height: 75vh;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  overflow: auto;

  .modal_header {
    font-size: 18px;
    font-weight: 600;
  }

  .modal_body {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-auto-rows: auto;
    gap: 2rem;
    margin-top: 1rem;

    .saved_address {
      cursor: pointer;
      border: 1px dotted transparent;
      padding: 1rem;
      transition: border-color 0.3s ease;
      border-color: #999;
      font-size: 12px;

      &:hover {
        border-style: solid;
      }
    }
  }
}

.close_btn {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 18px;
  text-decoration: none;
  color: #000;
}

.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #888 transparent;

  &::-webkit-scrollbar {
    width: 2px;
    height: 2px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-button {
    display: none;
    height: 0;
    width: 0;
  }
}

.orders_new {
  .off-canvas-wrap {
    .inner-wrap {
      .main-section {
        margin-top: 1rem;
      }

      .header-with-searchbar-wrapper {
        .main-flex-container {
          padding: 10px 0;

          .search-box {
            display: none;
          }
        }

      }

      .header-mega-menu-wrapper {
        display: none;
      }

      .desk_web {
        display: none;

        .desktop-header-component-container {
          position: relative;
          height: auto;
        }
      }
    }

  }
}

#main-section {
  margin: 0 auto;

  #container {
    margin-top: 0.5em !important;

    .new_order {
      width: 100%;
    }

    .shipping-payment-text {
      color: #670b19;
      font-size: 16px;
      text-transform: uppercase;
      max-width: 1170px;
      margin: 0 auto;
    }

    .checkout-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      max-width: 1170px;
      margin: 2rem auto;
    }

    .checkout-container {
      display: flex;
      justify-content: space-between;
      margin: 2rem 5rem;
      max-width: 1170px;
      margin: 0 auto;

      .left-column {
        max-width: 575px;
        width: 50%;

        #error_explanation {
          width: 100%;

          .notice {
            background: #fff6bf;
            padding: 9px;
            border: 2px solid #ffd324;
            margin: 1rem 0;
            word-break: break-word;
          }
        }

        .payment_box_style {
          background-color: #f4f4f4;
          border: 1px solid #ebebeb;
          padding: 10px 18px;
          margin: 1rem 0;

          .checkout_head {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .clr {
              display: none;
            }

            .change_address a {
              font-size: 14px;
            }
          }

          .billing-form {
            list-style-type: none;
            margin-left: 0;
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 0;

            .billing-fields {
              margin-right: 2rem;
              display: flex;
              flex-direction: column;
              gap: 1rem;

              .form-field {
                display: flex;
                justify-content: end;
                align-items: start;
                gap: 5px 1rem;
                width: 100%;
                flex-wrap: wrap;

                label {
                  color: #303030;
                  text-align: right;
                }

                input,
                select,
                textarea {
                  max-width: 320px;
                  height: 40px;
                  background-color: #ebebeb;
                  border: 1px solid #ebebeb;
                  border-radius: 4px;
                  color: #000;

                  &:focus {
                    border: 2px solid #000;
                    border-bottom: 2px solid #000;
                    box-shadow: none;
                  }

                  &:hover {
                    box-shadow: none;
                  }
                }

                label.error {
                  width: 100%;
                  color: #ff0000;
                }

                #showpincodefields,
                #showpincodefieldsshipping {
                  width: 100%;

                  .pin-code-field {
                    display: flex;
                    gap: 1rem;
                    justify-content: end;

                    .pin-code-field-helper {
                      display: flex;
                      flex-direction: column;
                      align-items: end;
                      max-width: 320px;
                      width: 100%;
                    }
                  }

                  #pincode_format_notice_shipping,
                  #pincode_format_notice {
                    margin-top: 5px;
                    text-align: center;
                    font-size: 12px;
                    background-color: #dff0d8;
                    border-color: #d6e9c6;
                    color: #3c763d;
                    padding: 0.5rem 1rem;
                    border: 1px solid transparent;
                    border-radius: 4px;
                    display: none;
                  }

                  .pin-code-helper {
                    font-size: 12px;
                    margin: 5px 0px;
                    text-align: center;
                    color: #3c763d;
                    padding: 5px;
                    border: 1px solid transparent;
                    border-radius: 4px;
                    background-color: #dff0d8;
                  }
                }
              }

              .order-address-line-fields {
                .street-address {
                  min-width: 200px;
                  max-width: 400px;
                  max-height: 300px;
                }
              }

              .order-address-line-field {
                .form-field {
                  &:nth-child(2) {
                    margin-top: 1rem;
                  }
                }
              }
            }

            .mobile-field {
              display: flex;
              gap: 1rem;
              justify-content: start;
              align-items: center;

              .number-field {
                display: flex;
                width: 100%;
                max-width: 320px;
                flex-wrap: wrap;

                .input-phone-addon {
                  background-color: #ccc;
                  width: 20%;
                }

                #order_billing_phone,
                #order_phone {
                  width: 80%;
                }
              }

              label {
                white-space: nowrap;
              }

              .input-phone-addon {
                max-width: 70px;
              }

              input[type="number"].phone,
              .phone[type="number"] {
                -moz-appearance: textfield;
                -webkit-appearance: none;
                appearance: textfield;

                &::-webkit-inner-spin-button,
                &::-webkit-outer-spin-button {
                  -webkit-appearance: none;
                  appearance: none;
                  margin: 0;
                }
              }
            }

            .address {
              display: flex;
              justify-content: start;
              align-items: center;

              .same-address-container {
                width: 100%;
                max-width: 320px;
              }

              #ship_to_same_address {
                margin: 0;
                height: unset;
              }
            }
          }
        }
      }

      .right-column {
        width: 50%;

        .payment-container {
          .payment-and-items {
            margin-top: 1rem;
            border: 1px solid #ebebeb;
            border-bottom: none;
            background: #f4f4f4;
          }

          .row {
            width: 100%;
          }

          .bordered_block {
            margin: 1rem 0 0 0;
            background-color: #f4f4f4;
            border: 1px solid #ebebeb;
          }

          .last-section {
            margin: 0 0 1rem;

            .discount-container {
              background: #f4f4f4;
              border-left: 1px solid #ebebeb;
              border-right: 1px solid #ebebeb;
            }

            .payment-text {
              padding: 1rem;
              background: #ebebeb;
              margin: 0 1rem;
              font-weight: 600;
              font-size: 14px;
            }
          }

          .order_total {
            width: 100%;
            background: #f4f4f4;
            padding: 1rem 1rem 0;

            .total-payable-amount {
              display: flex;
              gap: 5px;
              font-weight: 600;
              font-size: 14px;
              margin-left: 24px;
            }
          }

          .dom-payments {
            .pay_online {
              margin-bottom: 0;
              border: none;

              .content {
                background-color: #f4f4f4;

                .paypal-smart-payment-buttons {
                  padding: 0 1rem;
                }
              }
            }
          }

          .cart_mini_info {
            font-size: 15px;
            margin-top: 0;
            border-top: none;

            .product-details-container {
              display: grid;
              grid-template-columns: 1fr 1fr 2fr 2fr;
              gap: 10px;
              font-size: 14px;
              padding: 5px;
              border: 1px solid #ebebeb;

              .title,
              .item-image,
              .value-details,
              .quantity {
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }

            .product-details-container.heading {
              background-color: #ebebeb;
            }

            .wide {
              grid-column: span 2;
            }

            .estd_days {
              font-size: 16px;
            }

            .notice_rts {
              font-weight: 600;
              font-size: 12px;
              background: #ffffb6;
              padding: 5px;
            }

            .item_block {
              margin: 5px 0;

              .line-item {
                grid-column: span 4;

                .addons-notes {
                  list-style: none;
                  max-width: 277px;

                  .addon-breakdown {
                    display: flex;
                    justify-content: space-between;
                  }
                }
              }

              .item-image {
                width: 100%;
                overflow: hidden;

                a {
                  max-width: 52px;
                  max-height: 52px;
                  border: 1px solid;
                  overflow: hidden;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                }
              }

              .item-details {
                width: 100%;

                .addons-notes {
                  margin-left: 0;

                  .accordion-navigation {
                    a {
                      padding: 0;
                      display: flex;
                      justify-content: space-between;
                      font-size: 15px;
                    }
                  }
                }
              }
            }

            #totals_block {
              padding-top: 4px;
              display: flex;
              flex-direction: column;
              align-items: end;

              .total-pricing-section {
                width: 50%;
                display: flex;
                flex-direction: column;
                align-items: end;
                margin-left: auto;

                .shipping-charges-container {
                  width: 100%;
                }
              }

              .total {
                width: 100%;

                .items-grand-total {
                  display: grid;
                  grid-template-columns: 0.5fr 0.5fr 2fr 1fr;
                  border: 1px solid #ebebeb;
                  padding: 15px 5px 15px 25px;
                  margin: 2px 0;
                  background: #ebebeb;
                  font-weight: 600;

                  span:nth-of-type(1) {
                    grid-column: 3;
                  }
                }
              }

              .items-price {
                width: 100%;
                display: grid;
                grid-template-columns: 1fr 1fr;
                border: 1px solid #ebebeb;
                padding: 5px;
                margin: 2px 0;
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}



#paypal_button {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  display: table;
}

.btn {
  background: #670b19;
  border: 0px;
  width: 100%;
  color: #fff;
  display: inline-block;
  font-size: 14px;
  font-weight: bold;
  padding: 8px 15px;
  text-decoration: none;
  text-align: center;
  position: relative;
  transition: color .1s ease;
}

.btn.btn-big {
  font-size: 18px;
  padding: 15px 20px;
  min-width: 100px;
}

.btn-close {
  color: #303030;
  font-size: 30px;
  text-decoration: none;
  position: absolute;
  right: 5px;
  top: 0;
}

.btn2 {
  background-color: #fff;
  color: #8f1b1d;
  border: 2px solid #8f1b1d;
}

.modal:target:before {
  display: none;
}

.modal:before {
  content: "";
  display: block;
  background: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
}

.modal .modal-dialog {
  background: #fefefe;
  border: #333333 solid 1px;
  border-radius: 5px;
  position: fixed;
  bottom: 0;
  left: -0.5px;
  z-index: 11;
  width: 100%;
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  transform: translate(0, 0);
  -webkit-transition: -webkit-transform 0.3s ease-out;
  -moz-transition: -moz-transform 0.3s ease-out;
  -o-transition: -o-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
}

.modal:target .modal-dialog {
  top: -100%;
  -webkit-transform: translate(0, -500%);
  -ms-transform: translate(0, -500%);
  transform: translate(0, -500%);
}

.modal-body {
  padding: 0px 25px;
  font-size: 14px;
}

.modal-header {
  border-bottom: #eeeeee solid 1px;
  background: #E0E0E0;
  padding: 5px 10px;

  p {
    margin: 0px;
  }
}

.modal-footer {
  padding: 15px 25px;

  form {
    padding-bottom: 7px;
  }
}

.modal-header h2 {
  font-size: 20px;
}

.domestic-offer-modal {
  &.reveal-modal {
    height: 80%;
    min-height: 80vh;
    width: 80%;
    left: 10%;
  }
}

.offers_tab {
  .hidden_offers {
    display: none;
  }

  border-left-style: solid;
  border-left-color: #670B19;
  border-left-width: 3px;
  border-radius: 2px;
  padding-left: 0.5rem;

  .offer_row,
  .offers_title {
    padding-bottom: 0.5rem;
  }

  .offer_text_box {
    display: flex;
    padding-right: 0px;

    .offer_text {
      padding-left: 0.5rem;
      font-size: 13px;
      padding-top: 0.3rem;
    }
  }

  .offer_image {
    min-height: 20px;
    min-width: 20px;
  }

  box-shadow: 0 2px 2px 0 #E0E0E0;

  .tnc_offer {
    font-size: 12px;
    padding-top: 0.3rem;
  }

  .hide_up {
    display: none;

    #up_arrow_toggle {
      border: solid black;
      border-width: 0 2px 2px 0;
      display: inline-block;
      padding: 3px;
      transform: rotate(-135deg);
      -webkit-transform: rotate(-135deg);
      margin-left: 10px;
      margin-top: 8px;
      border-color: #670B19;
    }
  }

  #down_arrow_toggle {
    border: solid black;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 3px;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    margin-left: 10px;
    margin-bottom: 2px;
    border-color: #670B19;
  }

  .domestic_offers_toggle {
    display: flex;
    color: #670B19;
    font-size: 13px;
  }
}

#codModal {
  overflow-y: scroll;
  background-color: $body_white;
  top: 0 !important;
  position: fixed;
  width: 100%;
  height: 100%;
  border: 0;

  #cod-confirmation-message {
    color: $text_black;
  }

  #cod-confirmation-message {
    margin-top: 5%;
  }

  .close-reveal-modal {
    font-size: 1.8em;
    padding: 1% 4% 3% 10%;
    right: 0.3em;
  }

  .cod-otp-verification-form {
    font-size: 0.9em;
    text-align: center;
    padding-top: 10%;

    .error-msg-for-otp {
      display: none;
      color: red;
    }

    .cod-otp-input {
      width: 50%;
      font-size: 1.2em;
      text-align: center;
      color: $text_black;
      background-color: inherit;
      border-width: 0 0 3px;
      margin: 5% auto;
      box-shadow: none !important;
    }

    #otp-phone-change-form-and-content {
      display: none;
    }

    .cod-otp-modal-buttons {
      width: 50%;
      border-radius: 0 !important;
      text-transform: uppercase;
    }

    #otp-phone-text-value {
      border: 0;
      padding: 0;
      background: 0;
      width: auto;
      font-size: 1.2em;
      font-weight: 800;
      clear: both;
      margin: 0;
      display: inline;
    }

    #phone-change-button {
      font-size: 0.9em;
      margin: 0;
    }

    .button-as-text[type=button] {
      background: 0;
      display: inline;
      padding: 0;
      color: $dark_red;
    }
  }

  .footer {
    margin-top: 40px;
    float: right;

    #successButton {
      background: linear-gradient(to bottom, #0E9A7D, #267363);
      border-radius: 0.1rem;
      font-size: 1rem;
      padding: 18px 52px;
    }
  }
}

#paypalSuccessModal {
  -webkit-text-size-adjust: 100%;
  background: transparent;
  padding: 0px;
  border: 0px;

  #paypalModal {
    background: $menu_background;
    width: 90%;
    margin-right: auto;
    margin-left: auto;

    .modal-body {
      padding: 15px;

      #error-message {
        .error-text {
          font-weight: bold;
          margin-bottom: 10px;
        }

        .paypal-success-reasons {
          margin-left: 10px;
        }
      }

      .text-message {
        margin-bottom: 15px;
      }

      p {
        margin-top: 10px;
        margin-bottom: 10px;
        text-align: center;
      }

      .close-modal {
        float: right;
        line-height: 0px !important;
        font-size: 30px;
        color: $text_white;
        margin-right: -10px;
      }
    }

    .modal-footer {
      text-align: center;

      .retry-payment {
        font-weight: bold;
      }

      .cancel {
        color: white;
        background-color: transparent;
      }
    }
  }
}


#get_app_link {
  display: none;

  .panel {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.4);
    padding-right: 8px;

    h1 {
      color: $text_black;
    }

    .close {
      float: right;
      border-radius: 50%;
      background-color: transparent;
      padding: 2px 6px;
      color: $text_black;
      font-weight: bold;
    }

    p {
      padding-right: 2rem;
      text-align: justify;
      color: $text_black;
    }

    .success {
      margin-left: 19%;
      text-align: center;
      font-size: 1.2em;
      display: inline-block;
      padding: 0px 20px 0px 25px;
      border-radius: 4px;
      background-color: $dark_red;
      color: $body_white;
      box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);

      .fi-social-android {
        font-size: 30px;
        color: $body_white;
        vertical-align: middle;
      }

      .fi-social-apple {
        font-size: 30px;
        color: $body_white;
        vertical-align: middle;
      }
    }
  }
}

.card_message {
  display: none;
  color: #076915;
}

#create_orders_block {
  .accordion {
    padding-bottom: 1em;
  }

  // font-size: $big_font;
  // label {
  //   color: inherit;
  // }
  // .accordion {
  //   padding-left: 0em;
  //   margin-left: 0em;
  //   .accordion-navigation {
  //     > a {
  //       background: inherit;
  //       color: inherit;
  //       padding: 0rem;
  //       &:before {
  //         content: '»';
  //         float: left;
  //         color: $dark_red;
  //         font-weight: 700;
  //         padding-right: 2px;
  //       }
  //     }
  //     > .content {
  //       padding: 0rem;
  //       &.active {
  //         background: inherit;
  //       }
  //     }
  //     &.active > a:before {
  //       content: '«';
  //     }
  //   }
  // }
  .item_block {
    border: 0.1em solid #dcdcdc;
    margin-bottom: .5em;
    color: $text_light_gray;
    padding: 3px;

    .truncate {
      color: $text_light_gray;
    }
  }

  ul.addons-notes {
    padding-right: 0;

    li.accordion-navigation {
      a {
        font-size: 14px;
        font-family: "Inter", sans-serif !important;
      }

      .text-right {
        float: right;
      }
    }
  }

  .order_total {
    padding: 0.2em 1em;
    margin-top: 1em;
  }

  #totals_block {
    color: $text_light_gray;
  }

  .notice_rts {
    color: rgb(103, 11, 25);
    font-size: 12px;
    line-height: 15px;
    font-style: italic
  }

  .grand_total,
  .grand_total_with_cod,
  .grand_total_pd {
    color: $text_black;
    font-size: 15px;
    font-weight: 600;
  }

  .shipping_address {
    margin-bottom: 2em;
    padding: 1em;

    .address-text {
      width: 100%;
      font-size: 14px;
      color: $text_light_gray;
    }

    table {
      background: inherit;
      border: none;
      margin-bottom: 0em;

      tr {
        &:nth-of-type(even) {
          background: inherit;
        }

        td {
          color: inherit;
          padding: 0.1em;
        }
      }
    }
  }
}

#order_show_block {
  font-size: $big_font;

  .order-ack-title {
    color: $text_black;
  }

  .panel_block {
    .panel_content {
      padding: 1em;
    }
  }

  hr {
    border: $gray solid;
    border-width: 0.1em 0 0;
  }

  table {
    width: 100%;
    border: 0em;
    background: inherit;

    tr {
      background: inherit;

      td,
      th {
        color: inherit;
        line-height: 1em;
        padding: 0.3em;
      }
    }
  }

  .line_item_details {
    padding: 0.4em;
    color: $text_black;
    border: none;
  }

  .designer-order-block {
    background-color: $body_white;

    .panel_content {
      background-color: $body_white;
    }
  }
}

.notice_class {
  padding-left: 10px;
  color: orange;
  font-size: 14px;
  line-height: 15px;
}

.quantity_total {
  margin-right: 0em;
}

table.customer_order {
  border: 1px solid $gray;
  border-collapse: collapse;
}

table.customer_order tr {
  td {
    border: 1px solid $gray;
    color: white;
  }

  th {
    border: 1px solid $gray;
    color: white;
  }

  td.cost {
    text-align: right;
  }
}

table.customer_order tr {
  background: #333333;
  color: white;
}


/* The Modal (background) */
.modalForm {
  display: none;
  /* Hidden by default */
  position: fixed;
  z-index: 999;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  padding-top: 80px;
  left: 0;
  top: 0;
  width: 100%;
  height: 800px;
  background-color: rgb(0, 0, 0);

  /* Modal Content (Iframe) */
  .modal-content {
    margin: auto;
    display: block;
    width: 80%;
    max-width: 100%;
    -webkit-animation-name: zoom;
    -webkit-animation-duration: 0.6s;
    animation-name: zoom;
    animation-duration: 0.6s;
  }

  @-webkit-keyframes zoom {
    from {
      transform: scale(0)
    }

    to {
      transform: scale(1)
    }
  }

  @keyframes zoom {
    from {
      transform: scale(0)
    }

    to {
      transform: scale(1)
    }
  }

  /* The Close Button */
  .close {
    position: absolute;
    top: 25px;
    right: 5px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    transition: 0.3s;
  }

  .close:hover,
  .close:focus {
    color: #bbb;
    text-decoration: none;
    cursor: pointer;
  }

  /* 100% Image Width on Smaller Screens */
  @media only screen and (max-width: 700px) {
    .modal-content {
      width: 100%;
    }
  }

  @media only screen and (min-width: 1200px) {
    .modal-content {
      width: auto;
      height: auto;
    }
  }
}

/* Order status on order show page */
#order_status {
  float: left;
  margin-left: 1%;

  #circle_div {
    position: relative;
    float: left;
    width: 16%;

    .circle_stage {
      display: inline-block;
      border-radius: 50%;
      -moz-border-radius: 50%;
      -webkit-border-radius: 50%;
      -o-border-radius: 50%;
      font-size: 12px;
      line-height: 50px;
      text-align: center;
      border: 2px solid;
    }

    .circle_stage_domestic {
      display: inline-block;
      border-radius: 50%;
      -moz-border-radius: 50%;
      -webkit-border-radius: 50%;
      -o-border-radius: 50%;
      color: $text_black;
      text-align: center;
      border: 2px solid $light_grey;
      font-size: 0.7em;
      vertical-align: middle;
    }

    .square_stage {
      display: inline-block;
      font-size: 12px;
      line-height: 50px;
      text-align: center;
      border: 2px solid;
    }

    .step_image {
      background: transparent;
      position: relative;
      top: -4px;
    }

    .step_number {
      position: relative;
      top: -14px;
      color: black;
    }

    .vertical_line {
      width: 6px;
      height: 40px;
      margin-left: 52px;
      margin-bottom: -8px;
      display: inline-block;
      border: 1px solid
    }
  }

  #notes_div {
    width: 82%;
    margin-left: 80px;

    .status_note {
      margin-left: 18px;
      color: $text_black;
      padding-top: 2px;
      font-size: small;
      margin-top: -3px;
      margin-bottom: 25px;
    }
  }
}

.all_order_stages {
  display: inline-block;

  .horizontal-line {
    width: 2em;
    height: 0.3em;
    background: yellowgreen;
    border: 1px solid white;
    border-radius: 1px;
    float: right;
  }

  .base {
    background: yellowgreen;
    display: inline-block;
    height: 1em;
    margin-left: 5%;
    position: relative;
    width: 2.5em;
    float: left;
    margin-right: 5%;

    .pointer {
      border-left: 15px solid yellowgreen;
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
      content: "";
      height: 0;
      left: 40px;
      position: absolute;
      top: 0px;
      width: 0;
    }

    .uc_base {
      font-size: x-small;
      color: black;
      padding: 2px;
    }
  }
}

.status_text {
  margin: 2% 10% 0% 10%;
  width: 80%;
  text-align: center;
  color: $text_black;
  border-radius: 2px;
  font-size: 0.8em;
  font-weight: 600;
  border: $border_black;
}

#return_panel {
  .bordered_block {
    background-color: transparent;
    box-shadow: none;

    .policy_button {
      background-color: $light_red;
      color: $text_white;
    }
  }
}

/* Return Button */
body.modal-open {
  overflow: hidden;
  position: fixed;
}

.reveal-modal {
  overflow-y: auto;
  height: 90%;
  position: fixed;
  top: 20px !important;

  .close-reveal-modal {
    &.close-button {
      color: #fff;
      font-size: 0.6875rem;
      font-weight: 400;
      line-height: normal;
      position: static;
    }
  }
}


#bank_deposit_order_box {
  display: none;
  margin-right: 30px;
  background-color: #cecece;
  color: #000000;
  text-align: center;
  padding: 8px;
  position: fixed;
  z-index: 10;
  bottom: 1%;
  font-size: 16px;
  line-height: 21px;
  border: 2px solid #30924A;

  .bank_deposit_order_text_close {
    float: right;
    text-align: right;
    position: relative;
    left: 25px;
    bottom: 25px;
    padding: 0px 3px;
    cursor: pointer;
    border-radius: 50%;
    background-color: #a9a9a9;
  }

  .bank_deposit_order_text {
    span {
      color: #30924A;
      text-transform: uppercase;
      font-weight: 500;
    }
  }
}

.shipping_options {
  .shipping_option {
    .shipping_radio_button {
      font-size: 14px;
      display: flex;
      justify-content: space-between;

      label {
        display: flex;
        align-items: center;
        gap: 5px;
        color: #303030;

        input[type="radio"] {
          margin: 0;
        }
      }
    }

    .shipping_option_price {
      float: right;
      font-size: 14px;
    }
  }

  .delivery_message {
    padding-left: 18px;
    font-size: 13px;
    color: $dark_red;
  }
}

.cashback-reminder {
  background-color: #eee;
  padding: 1.3em 1.6em;
  margin-bottom: 1em;
  text-align: center;
  border: 2px solid #bdbdbd;
}

#cancel-confirmation-modal {
  .cancel-reason-request {
    margin-bottom: 0.45em;
  }

  .cancel-helper-info {
    margin-top: 1.2em;
  }

  .cancel-order-reason-select-warning {
    font-size: 0.9em;
    color: #a94442;
    margin-top: 0.5em;
  }
}

#otp-verification-modal.reveal-modal {
  #otp-form-and-content {
    padding-top: 10%;
    font-size: 0.9em;
    text-align: center;
  }

  #otp-show-phone-details {
    font-size: 1.2em;
    font-weight: bold;
  }

  #otp-error-msg {
    display: none;
    color: red;
  }

  #otp-form {
    .cod-otp-input {
      width: 50%;
      font-size: 3em;
      font-family: monospace;
      padding: 1em 0em;
      text-align: center;
      color: #303030;
      background-color: inherit;
      border-width: 0 0 3px;
      margin: 2% auto;
    }

    #otp-submit-button {
      width: 50%;
      text-transform: uppercase;
    }

    #resend-otp {
      color: #670b19;
      display: inline;
      padding: 0;
      background: none;
    }
  }
}

.addon_form_link {
  padding: 0.5em;
  border: 2px solid #670b19;

  a {
    color: #670b19;
  }

  &.filled {
    border-color: #808080;
  }

  &.filled {
    a {
      color: #1a1a1a;
    }
  }
}

#returnStatusModal {
  height: auto;
  min-height: auto;
  max-height: 90%;
  overflow-y: auto;
}

#return_status {

  #horizontal_line {
    width: 4px;
    height: 40px;
    position: relative;
    top: 0%;
    z-index: 0;
    left: 50%;
    right: 0%;
    border-radius: 0% !important;
  }

  table {
    width: 100%;

    tr,
    td {
      text-align: center !important;
    }
  }

  .stage_name {
    top: 15px;
    font-weight: bold;
    font-size: 20px;
    color: #670b19;
  }

  .timestamp {
    display: block;
    font-size: 13px;
  }

  .note {
    display: block;
    font-size: 13px;
    color: lightslategrey;
  }

  .step_image {
    min-width: 35px;
  }
}

.order_summary {
  background: #eee;
}

.wallet_payment {
  background: #eee;
  margin-bottom: 10px !important;
  padding: 15px 15px 0 15px;
}

.credit_card_information {
  background: #f4f4f4;
  border: 1px solid #ebebeb;
  padding: 10px;

  h5 {
    font-weight: bold !important;
    font-size: 18px;
  }

  p {
    font-size: 15px;
  }
}

#how_to_pay {
  background: #f4f4f4;
  border: 1px solid #ebebeb;
  padding: 10px;
  margin: 1rem 0;
}

.checkout_message_icons {
  display: flex;
  justify-content: flex-end;
}

.document-upload {
  position: relative;
  padding: 1rem;
  background-color: #f7f7f7;
  font-size: 0.8rem;
  text-align: left;
  max-width: 1000px;
  margin: 0 auto;

  .za-label {
    display: block;
    font-weight: bold;
    padding-right: 10rem;
    margin-bottom: 0.5rem;
  }

  .za-note {
    display: block;
    color: red;
    font-size: 0.8rem;
    padding-right: 10rem;
  }

  .upload-button {
    position: absolute;
    top: 50%;
    right: 4rem;
    transform: translateY(-50%);
    background-color: #670b19;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 0.8rem;
    text-decoration: none;
    white-space: nowrap;

    &:hover {
      background-color: #ca0101;
    }
  }
}

@media only screen and (max-width: 768px){
  .cart_mini_info{
    .row{
      margin-bottom: unset!important;
    }
  }
  .credit_card_information {
    margin-bottom: 20px !important;
  }
}
@media only screen and (min-width: 768px){
  .paymentSteps {
    display: none;
  }

  #create_orders_block {
    margin-top: 5rem;

    .order_total {
      margin-top: 0rem;
    }
  }

  #order_show_block {
    margin-top: 8rem;
  }
}

@media screen and (max-width: 1200px) {
  #main-section #container {

    .checkout-header,
    .shipping-payment-text,
    .checkout-container {
      max-width: 1000px;
    }

    .checkout-container {
      .left-column {
        .payment_box_style {
          .billing-form {
            .billing-fields {
              .form-field {

                .number-field,
                .same-address-container,
                #showpincodefields .pin-code-field .pin-code-field-helper,
                #showpincodefieldsshipping .pin-code-field .pin-code-field-helper,
                input,
                select,
                textarea {
                  max-width: 290px;
                }
              }
            }
          }
        }
      }
    }
  }
}

@media screen and (min-width:1024px) {
  .sticky-button {
    position: relative;
    margin-bottom: 20px;
  }
}

@media only screen and (min-width: 64.0625em) {
  .large-block-grid-4>li {
    list-style: none;
    width: 20%;

    &:nth-of-type(1n) {
      clear: none;
    }

    &:nth-of-type(4n+1) {
      clear: both;
    }
  }
}

@media screen and (min-width:1025px) {

  .sticky-desktop,
  .sticky-desktop-int {
    display: none;
  }
}

@media screen and (max-width:1024px) {

  .sticky-mobile,
  .sticky-mobile-int {
    display: none;
  }
  .upload-button {
    position: static;
    transform: none;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 1rem auto 0;
    max-width: 35%;
    text-align: center;
  }

  .za-label,.za-note {
    padding-right: 0;
  }
  #main-section #container {
    .checkout-header {
      margin: 4rem auto 2rem;

      .logo-img {
        display: none;
      }
    }

    .checkout-header,
    .shipping-payment-text,
    .checkout-container {
      max-width: 900px;
    }

    .checkout-container {
      .left-column {
        .payment_box_style {
          .billing-form {
            .billing-fields {
              .form-field {

                .number-field,
                .same-address-container,
                #showpincodefields .pin-code-field .pin-code-field-helper,
                #showpincodefieldsshipping .pin-code-field .pin-code-field-helper,
                input,
                select,
                textarea {
                  max-width: 235px;
                }
              }
            }
          }
        }
      }


      .right-column {
        .payment-container {
          .cart_mini_info {
            .item_block {
              .line-item {
                .addons-notes {
                  max-width: 400px;
                  margin-right: 1.7rem;
                }
              }
            }
          }
        }
      }
    }

  }

  .pay_online {
    #pp-message {
      display: none;
    }
  }
}

.designer_order_image {
  width: 100% !important;
}

@media screen and (max-width:950px) {
  #main-section #container {

    .checkout-header,
    .shipping-payment-text,
    .checkout-container {
      max-width: 725px;
    }

    .checkout-container {
      .left-column {
        .payment_box_style {
          .billing-form {
            .billing-fields {
              margin-right: 0;

              .form-field {

                .number-field,
                .same-address-container,
                #showpincodefields .pin-code-field .pin-code-field-helper,
                #showpincodefieldsshipping .pin-code-field .pin-code-field-helper,
                input,
                select,
                textarea {
                  max-width: 185px;
                }
              }
            }
          }
        }
      }
    }
  }
}

@media only screen and (max-width: 768px) {

  .credit_card_information {
    margin-bottom: 20px !important;
    display: none;
  }

  #how_to_pay {
    display: none;
  }

  #main-section #container {
    margin: 0;

    .modal_body {
      grid-template-columns: 1fr;
    }

    .paymentSteps {
      margin-top: 4rem;
    }

    .checkout-container {
      flex-direction: column;
      gap: 0;

      .left-column {
        max-width: none;
        margin-right: 0;
        width: 100%;

        .payment_box_style {
          .billing-form {
            .billing-fields {
              margin-right: 0;

              .form-field {

                .number-field,
                .same-address-container,
                #showpincodefields .pin-code-field .pin-code-field-helper,
                #showpincodefieldsshipping .pin-code-field .pin-code-field-helper,
                input,
                select,
                textarea {
                  max-width: 295px;
                }
              }
            }
          }
        }
      }


      .right-column {
        width: 100%;

        .payment-container {
          .payment-and-items {
            margin-top: 0;
          }

          .bordered_block {
            margin: 0 0 1rem 0;
          }
        }
      }
    }

    .checkout-header {
      margin: 0rem auto 1rem;
      justify-content: end;
    }
  }

  form {
    margin: 0 10px;
  }
}

@media screen and (max-width: 500px) {
  #main-section #container .checkout-container .left-column {

    .payment_box_style {
      .billing-form {
        .billing-fields {
          margin-right: 0;

          .form-field {

            .number-field,
            .same-address-container,
            #showpincodefields .pin-code-field .pin-code-field-helper,
            #showpincodefieldsshipping .pin-code-field .pin-code-field-helper,
            input,
            select,
            textarea {
              max-width: 200px;
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 500px) {
  #main-section #container .checkout-container .left-column {

    .payment_box_style {
      .billing-form {
        .billing-fields {
          margin-right: 0;

          .form-field {

            .number-field,
            .same-address-container,
            #showpincodefields .pin-code-field .pin-code-field-helper,
            #showpincodefieldsshipping .pin-code-field .pin-code-field-helper,
            input,
            select,
            textarea {
              max-width: 160px;
            }
          }
        }
      }
    }
  }
}
