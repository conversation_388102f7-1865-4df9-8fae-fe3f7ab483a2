var MR = MR || {};
MR = (function (window, document, Mirraw) {
  Mirraw.searchloader = {
    initSearchLoader: function () {
      const $form = $('.search-bar-form');
      const $overlay = $('.overlay');
      const $loader = $('.loader_main');
      function showLoader() {
        $overlay.show();
        $loader.show();
      }
      function hideLoader() {
        $overlay.hide();
        $loader.hide();
      }
      $(document).ready(function() {
        $form.on('submit', function (event) {
          showLoader();
          $form.submit();
        });
        $(document).on('click', '.unbxd-as-keysuggestion', function () {
          showLoader();
        });
        $(document).on('click', '.unbxd-as-popular-product-name', function () {
          showLoader();
        });
        $form.on('keydown', function (event) {
          if (event.key === 'Enter') {
            showLoader();
            $form.submit();
          }
        });
        hideLoader();
      });
      window.addEventListener('pageshow', function () {
        hideLoader();
      });
    },
  };
  return Mirraw;
})(this, this.document, MR);
afterWindowOrTrubolinksLoad(function(){
  MR.searchloader.initSearchLoader();
});