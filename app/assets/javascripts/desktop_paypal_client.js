//= require desktop_order_form_validation
var PAYPAL_SDK = PAYPAL_SDK || {};

PAYPAL_SDK = (function(window, document, Paypal){
  const inputBillingAddress = ['#order_billing_email','#order_billing_first_name','#order_billing_last_name','#order_billing_phone','#order_billing_country','#order_billing_pincode','#order_billing_street_line_1','#order_billing_street_line_2','#order_billing_street','#order_billing_city','#order_billing_state'];
  const inputShippingAddress = ['#order_last_name', '#order_first_name','#order_country','#order_pincode','#order_street_line_1','#order_street_line_2','#order_city','#order_street','#order_buyer_state','#order_phone'];

  Paypal.fundingSource = null
  Paypal.product = { 
    paypalButtonRender: function(){
      
      function triggerGA4(){
        window.dataLayer = window.dataLayer || [];
        var ga4_payment_params, ga4_shipping_params, i, item, item_value, len, pay_type_value, ref;

        item_value = 0;
        items_data = gon.items_data;
        for (count = 0, len = items_data; count < len; count++) {
          item = items_data[count];
          item_value += item['price'];
        }
        ga4_shipping_params = {
            event: "ga4_add_shipping_info",
            ecommerce: {
            currency: "INR",
            country_code: gon.country_code,
            value: gon.item_total_price,
            shipping_tier: "Regular",
            tax: gon.tax_amount || 0,
            coupon: gon.coupon_code || "",
            coupon_discounts: gon.coupon_price || 0,
            customization: gon.addon_charges,
            gift_wrap: gon.gift_wrap,
            offers_discount: gon.bmgn_discount_rounded_val,
            items: gon.items_data
          }
        };
        dataLayer.push({
          ecommerce: null
        });
        dataLayer.push(ga4_shipping_params);
        console.log(ga4_shipping_params);
        pay_type_value = Paypal.fundingSource;
        ga4_payment_params = {
          event: "ga4_add_payment_info",
          ecommerce: {
            currency: "INR",
            country_code: gon.country_code,
            value: gon.item_total_price,
            tax: gon.tax_amount || 0,
            coupon: gon.coupon_code || "",
            coupon_discounts: gon.coupon_price || 0,
            customization: gon.addon_charges,
            gift_wrap: gon.gift_wrap,
            offers_discount: gon.bmgn_discount_rounded_val,
            items: gon.items_data,
            payment_type: pay_type_value
          }
        };
        dataLayer.push({
          ecommerce: null
        });
        dataLayer.push(ga4_payment_params);
        console.log(ga4_payment_params);
      }

      function onChange(handler) {
        var input_ids = inputBillingAddress.concat(inputShippingAddress).concat(['#ship_to_same_address'])
        for (var i in input_ids) {
          var element = input_ids[i];
          $(element).bind('change', handler);
        }
      }
      
      function toggleButton(actions) {
          return PLACE_ORDER_FORM_VALIDATION.methods.isValid() ? actions.enable() : actions.disable();
      }
      
      
      paypal.Buttons({
        style: {
          size: 'medium',
          color: 'gold',
          shape: 'rect',
          label: 'pay',
          tagline: false
        },
    
        env: gon.env_name, // Valid values are sandbox and live.
    
        onInit: function(data, actions) {
          $("#payment-options").css('display', 'none');
          toggleButton(actions)
          onChange(function(e) { 
            if (e.target.id == 'order_billing_country' || e.target.id == 'order_country') {
              setTimeout(function(){
                element = ['#order_billing_state', '#order_buyer_state']
                for (var i in element){
                  $(element[i]).bind('change', function(){toggleButton(actions);});
                }
              }, 1000);
            }
            toggleButton(actions);
          });
        },
    
        onClick: function(data) {
          PLACE_ORDER_FORM_VALIDATION.methods.isValid() ?  PLACE_ORDER_FORM_VALIDATION.methods.removeValidationCss() : PLACE_ORDER_FORM_VALIDATION.methods.toggleValidationMessage()
          Paypal.fundingSource =  data.fundingSource 
          if(typeof _osCheckout != 'undefined') _osCheckout(_osParamsObj);
        },
    
        createOrder: function() {
          // Set up a url on your server to create the payment
          var CREATE_URL = '/orders';
          function post( url, params ) {
              triggerGA4();
              url += '?' + ( new URLSearchParams( params ) ).toString();
            return fetch( url, {method: "POST"} ).then( function(response){return response.json();});
          };
          
          var payload_old = $("#new_order").serializeArray();
          var payload = {}
          for (var i in payload_old) {
            var ele = payload_old[i]
            payload[ele['name']] = ele['value']
          } 
          payload['order[pay_type]'] = Paypal.fundingSource
          payload['order[attempted_payment_gateway]'] = 'paypal_smartpay'
          
          return post(CREATE_URL, payload)
          .then(function(res) {
            if (res.has_error != undefined  ) {
              window.location.href = "/order/paypal_response_handling?error=true&messages=" + res.errors + "&redirect_root=" + res.redirect_root
            } else if (res.redirect_to){
              return (window.location.href = res.redirect_to);
            } else {
              sessionStorage.setItem("returnUrl", res.return_url);
              return res.id
            }
          });
        },  
    
        onApprove: function(data, actions) {
          var EXECUTE_URL = '/order/paypal_execute';
          var data = {
            orderID: data.orderID,
          };
          return fetch(EXECUTE_URL, {
          method: 'post',
          headers: {
            'content-type': 'application/json'
          },
          body: JSON.stringify(data)
          })
          .then(function (res) {
            if (res.status == 200) {
              return res.json()
            }
            else {
              window.location.href = "/order/paypal_response_handling?error=true&messages=" + res.errors
            }
          })
          .then(function (res) {
            var return_url = sessionStorage.getItem("returnUrl");
            if (res.has_error){
              window.location.href = "/order/paypal_response_handling?error=true&messages=" + res.errors  
            }
            else {
            window.location.href = '/order/paypal_response_handling?returnUrl=' + return_url
            }
          })
          ;
        },
        onError: function(data){
          window.location.href = "/order/paypal_response_handling?error=true&messages=" + res.errors
        }
      }).render('#paypal-button-container');
    
    }
  }
  return Paypal; 
})(this, this.document, PAYPAL_SDK);

PAYPAL_SDK.product.paypalButtonRender()
