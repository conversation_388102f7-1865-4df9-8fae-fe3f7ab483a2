//= require fuse.min
//= require jquery.validate.min.js

var MR = MR || {};

MR = (function(window, document, Mirraw) {
  var zipCountryCode, zipRegex, stateListFuse;

  Mirraw.shipping = {
    zipCountryCode: undefined,
    zipRegex: undefined,
    stateListFuse: undefined,

    shippingCountry: function() {
      var country = ($("#order_country").is(":visible")) ? $("#order_country").val() : $("#order_billing_country").val();
      $("#no_india_shipping").removeClass().text("");
      
      if (country === 'India' && $('#order_shipping_restrict').val() === 'india') {
        $('#order_pay_type_cash_on_delivery').attr('disabled', true);
        $('#cod_status_always').removeClass().text("");
        $('#order_pay_type_cash_before_delivery').attr('disabled', true);
        $('#cod1_status_always').removeClass().text("");
        $('input[name="commit"]').attr('disabled', 'true');
        $("#no_india_shipping").removeClass().addClass("alert alert-danger").text("Order cannot be shipped to India.");
      } else if (typeof gon === "undefined" || gon.rakhi_pre_order !== true) {
        $('input[name="commit"]').removeAttr('disabled');
      }
    },

    setMaxCount: function(phone_number) {
      var country = this.set_country(phone_number);
      var digitMaxCount;
      
      if (country != "India") {
        digitMaxCount = 20;
      } else {
        if (gon && gon.max_phone_length) {
          digitMaxCount = gon.max_phone_length;
        } else {
          digitMaxCount = 10;
        }
      }
      return digitMaxCount;
    },

    set_country: function(phone_number) {
      var country = $("#order_country").val();
      if (phone_number == "billing_phone_number") {
        country = $("#order_billing_country").val();
      }
      return country;
    },

    getPhoneNoMaxDigitCount: function(phone_number) {
      var country = this.set_country(phone_number);
      var digitMaxCount;
      
      switch(country) {
        case 'United States': digitMaxCount = 10; break;
        case 'India': digitMaxCount = this.setMaxCount(phone_number); break;
        case 'UAE': digitMaxCount = 9; break;
        default: digitMaxCount = this.setMaxCount(phone_number);
      }
      return digitMaxCount;
    },

    getPhoneNoMinDigitCount: function(phone_number) {
      var country = this.set_country(phone_number);
      var digitMinCount;
      
      switch(country) {
        case 'United States': digitMinCount = 10; break;
        case 'India': digitMinCount = 10; break;
        case 'UAE': digitMinCount = 9; break;
        default: digitMinCount = 8;
      }
      return digitMinCount;
    },

    appendPhoneCode: function(selectName, inputName) {
      var country = $('#' + selectName).val();
      $.ajax({
        type: 'GET',
        data: { country: country },
        url: '/country/get_state_dial_code',
        datatype: 'JSON',
        success: function(data, status, jqhxr) {
          $('#' + inputName).val('+' + data.dial_code);
          if (country === "India") {
            $('#' + inputName).attr('readonly', true);
          } else {
            $('#' + inputName).attr('readonly', false);
          }
        }
      });
    },

    rakhiPreOrder: function() {
      var country = ($("#order_country").is(":visible")) ? $("#order_country").val() : $("#order_billing_country").val();

      if (typeof gon !== "undefined" && gon.rakhi_pre_order) {
        $("#rakhi_pre_order_message").removeClass().text("");

        if (gon.rakhi_all_schedule) {
          $("#rakhi_pre_order_message").removeClass().addClass("alert alert-success").text("Your order will be delivered " + gon.rakhi_pre_order_date + ".");
          $(".delivery_time").hide();
          return false;
        } else if (gon.rakhi_with_other_designs) {
          if (country === 'India') {
            $("#rakhi_pre_order_message").removeClass().addClass("alert alert-info").text("Your order has multiple items, only Rakhi will be delivered " + gon.rakhi_pre_order_date + ".");
            $(".delivery_time").text((Math.round(gon.indian_shipping_time) + ' days'));
          } else {
            $("#rakhi_pre_order_message").removeClass().addClass("alert alert-success").text("Your order will be delivered " + gon.rakhi_pre_order_date + ".");
            $(".delivery_time").hide();
          }
          return false;
        }
      }
    },

    handleCodWithOtp: function(form) {
      $('#otp-error-msg').hide();
      $('#cod-otp').val('');
      var phoneNo = $('#order_billing_phone').val();
      $.ajax(this.generateOtp(phoneNo, false));
      $('#codModal').modal();
      
      $('#otp-form').off('ajax:success').on('ajax:success', function(evt, data) {
        if(data.verified === true) {
          $('#codModal').modal('hide');
          $('#green_button_box').find('input[type=submit]').attr('disabled', true).css('opacity', 0.5);
          form.submit();
        } else {
          $('#otp-error-msg').show();
        }
      });
    },

    generateOtp: function(phoneNo, resend) {
      return {
        type: 'POST',
        data: {
          phone: phoneNo,
          resend: resend
        },
        url: '/carts/generate_otp'
      };
    },

    displayCustomsChargeMessage: function(country) {
      if (!gon || !gon.custom_duty_country) return;
      
      var customsCharge = gon.custom_duty_country.country[country.toLowerCase()];
      if (typeof customsCharge !== 'undefined') {
        if (gon.total < customsCharge) {
          $('#customs_charge_message').html(gon.custom_duty_country.message.no_duty);
        } else {
          $('#customs_charge_message').html(gon.custom_duty_country.message.duty);
        }
      } else {
        $('#customs_charge_message').html('');
      }
    },

    createStateDropDown: function(state_list) {
      var options = '';
      $.each(state_list, function(index, state) {
        options += "<option value='" + state + "'>" + state + "</option>";
      });
      return options;
    },

    paramsGetStates: function(country_code, id, selected_value, clear_pin_and_city) {
      if (clear_pin_and_city === undefined) clear_pin_and_city = true;
      
      return {
        type: 'GET',
        url: '/country/' + country_code + '/get_states',
        success: function(data, status, jqhxr) {
          var options = Mirraw.shipping.createStateDropDown(data);
          if (id === 'order_billing_country') {
            if (clear_pin_and_city) {
              $("#order_billing_city").val("");
              $("#order_billing_pincode").val("");
            }
            if (options === '') {
              $('#order_billing_state').replaceWith('<input id="order_billing_state" name="order[billing_state]" size="30">');
            } else {
              options = "<option value=''>Please Select</option>" + options;
              var list = "<select id='order_billing_state' name='order[billing_state]'>" + options + "</select>";
              $('#order_billing_state').replaceWith(list);
            }
            if (typeof selected_value !== 'undefined') {
              $('#order_billing_state').val(selected_value);
            }
            $.ajax(Mirraw.shipping.showPincodeFormat(country_code, id));
          } else {
            $("#order_city").val("");
            $("#order_pincode").val("");
            if (options === '') {
              $('#order_buyer_state').replaceWith('<input id="order_buyer_state" name="order[buyer_state]" size="30">');
            } else {
              options = "<option value=''>Please Select</option>" + options;
              var list = "<select id='order_buyer_state' name='order[buyer_state]'>" + options + "</select>";
              $('#order_buyer_state').replaceWith(list);
            }
            if (typeof selected_value !== 'undefined') {
              $('#order_buyer_state').val(selected_value);
            }
            $.ajax(Mirraw.shipping.showPincodeFormat(country_code, id));
          }
          $('#order_billing_pincode').trigger('input');
        }
      };
    },

    showPincodeFormat: function(country_code, id) {
      return {
        type: 'GET',
        url: '/country/' + country_code + '/get_pincode_format',
        success: function(data, status, jqhxr) {
          var pincode_format = data[0][0];
          if (id === 'order_billing_country') {
            if (pincode_format !== "" && pincode_format !== null) {
              $("#showpincodefields").show();
              $("#pincode_format_notice").text("Please enter Pincode / Zip / Postal Code in following format. Example : " + pincode_format);
              $("#pincode_format_notice").css('display', 'inline-block');
            } else {
              $("#order_billing_pincode").val("None");
              $("#showpincodefields").hide();
              $("#pincode_format_notice").css('display', 'none');
            }
          } else {
            if (pincode_format !== "" && pincode_format !== null) {
              $("#showpincodefieldsshipping").show();
              $("#pincode_format_notice_shipping").text("Please enter Pincode / Zip / Postal Code in following format. Example : " + pincode_format);
              $("#pincode_format_notice_shipping").css('display', 'inline-block');
            } else {
              $("#order_pincode").val("None");
              $("#showpincodefieldsshipping").hide();
              $("#pincode_format_notice_shipping").css('display', 'none');
            }
          }
          Mirraw.shipping.zipCountryCode = data[1][0];
          Mirraw.shipping.zipRegex = new RegExp(data[1][1], 'i');
          if (Mirraw.shipping.zipCountryCode === undefined) {
            Mirraw.shipping.stateListFuse = undefined;
          } else {
            var options = {
              shouldSort: true,
              threshold: 0.85,
              location: 0,
              distance: 100,
              maxPatternLength: 32,
              minMatchCharLength: 2,
              keys: ["value"]
            };
            Mirraw.shipping.stateListFuse = new Fuse($('#order_billing_state option'), options);
          }
        }
      };
    },

    paramInternationalFetchCityStateOptions: function(country_code, pincode, address_city, address_state) {
      return {
        type: 'GET',
        url: '//api.zippopotam.us/' + country_code + '/' + pincode,
        datatype: 'JSON',
        success: function(data, status, jqhxr) {
          if (data.places !== undefined && data.places[0] !== undefined) {
            $(address_city).val(data.places[0]['place name']);
            var state_name = data.places[0].state;
            if (state_name === '' || state_name === undefined) {
              var state_code = data.places[0]['state abbreviation'];
              if (state_code === '' || state_code === undefined) {
                state_name = data.places[0]['place name'];
              } else {
                state_name = state_code;
              }
            }
            if (Mirraw.shipping.stateListFuse !== undefined) {
              var state_option = Mirraw.shipping.stateListFuse.search(state_name)[0];
              if (state_option !== undefined) {
                state_name = state_option.value;
              }
            }
            if (state_name !== undefined) {
              $(address_state).val(state_name).prop('selected', true);
            }
          }
        }
      };
    },

    paramsGetCityState: function(pincode, address_city, address_state) {
      return {
        type: 'GET',
        data: { pincode: pincode },
        url: '/api/v1/addresses/pincode_info',
        datatype: 'JSON',
        success: function(data, status, jqhxr) {
          if (data) {
            $(address_city).val(data.city_name);
            $(address_state).val(data.state).prop('selected', true);
          }
        }
      };
    },

    CityStateAutoFill: function(address_pincode, address_country, address_city, address_state) {
      var pincode = $(address_pincode).val();
      var country = $(address_country).val();
      if (country === 'India') {
        if (pincode.length === 6) {
          $.ajax(Mirraw.shipping.paramsGetCityState(pincode, address_city, address_state));
        }
      } else if ((Mirraw.shipping.zipCountryCode !== undefined) && (Mirraw.shipping.zipRegex !== undefined) && Mirraw.shipping.zipRegex.test(pincode)) {
        $.ajax(Mirraw.shipping.paramInternationalFetchCityStateOptions(Mirraw.shipping.zipCountryCode, pincode, address_city, address_state));
      }
    },

    domValidation: function() {
      $("#new_order").validate({
        rules: {
          "order[billing_email]": {
            required: {
              depends: function() {
                $(this).val($.trim($(this).val()));
                return true;
              }
            },
            email: true
          },
          "order[billing_first_name]": {
            required: true,
            minlength: 2
          },
          "order[billing_last_name]": {
            required: true,
            minlength: 2
          },
          "order[billing_phone]": {
            required: true,
            digits: true,
            minlength: function() {
              return Mirraw.shipping.getPhoneNoMinDigitCount("billing_phone_number");
            },
            maxlength: function() {
              return Mirraw.shipping.getPhoneNoMaxDigitCount("billing_phone_number");
            }
          },
          "order[billing_country]": {
            required: true
          },
          "order[billing_pincode]": {
            required: true,
            minlength: function() {
              if ($("#order_billing_country").val() == 'United States') {
                return 5;
              }
            },
            maxlength: function() {
              if ($("#order_billing_country").val() == 'United States') {
                return 5;
              }
            }
          },
          "order[billing_street_line_1]": {
            required: true
          },
          "order[billing_street_line_2]": {
            required: true
          },
          "order[billing_street]": {
            required: true
          },
          "order[billing_city]": {
            required: true
          },
          "order[billing_state]": {
            required: true
          },
          'password': {
            required: true,
            minlength: 6
          },
          "order[first_name]": {
            required: '#ship_to_same_address:unchecked'
          },
          "order[last_name]": {
            required: '#ship_to_same_address:unchecked'
          },
          "order[phone]": {
            required: '#ship_to_same_address:unchecked',
            digits: true,
            minlength: function() {
              return Mirraw.shipping.getPhoneNoMinDigitCount("shipping_phone_number");
            },
            maxlength: function() {
              return Mirraw.shipping.getPhoneNoMaxDigitCount("shipping_phone_number");
            }
          },
          "order[country]": {
            required: '#ship_to_same_address:unchecked'
          },
          "order[pincode]": {
            required: '#ship_to_same_address:unchecked',
            minlength: function() {
              if ($("#order_country").val() == 'United States') {
                return 5;
              }
            },
            maxlength: function() {
              if ($("#order_country").val() == 'United States') {
                return 5;
              }
            }
          },
          "order[street_line_1]": {
            required: '#ship_to_same_address:unchecked'
          },
          "order[street_line_2]": {
            required: '#ship_to_same_address:unchecked'
          },
          "order[street]": {
            required: '#ship_to_same_address:unchecked'
          },
          "order[city]": {
            required: '#ship_to_same_address:unchecked'
          },
          "order[buyer_state]": {
            required: '#ship_to_same_address:unchecked'
          }
        },
        messages: {
          'password': {
            required: 'Please provide a password.',
            minlength: 'Password length should be greater than 6.'
          }
        },
        submitHandler: function(form) {
          if($('#codModal').length && $('#order_pay_type_cash_on_delivery').is(":checked")) {
            if ($('#cod-confirm-wo-otp').length) {
              $('#cod-confirmation-message').css('color', 'white');
              $('#codModal').modal();
              $('#cod-confirm-wo-otp').off('click').on('click', function() {
                $('#codModal').modal('hide');
                form.submit();
              });
            } else {
              Mirraw.shipping.handleCodWithOtp(form);
            }
          } else {
            form.submit();
            $('#green_button_box').find('input[type=submit]').attr('disabled', true).css({
              'opacity': '0.5', 
              'background-image': 'none'
            }).val('Please Wait ...');
          }
        }
      });
    },

    modalEvents: function () {
      var $modal = $("#change_address");
      var $openBtn = $("#open_modal");
      var $closeBtn = $modal.find(".close_btn");
    
      $openBtn.on("click", function (e) {
        e.preventDefault();
        $modal.show();
      });
    
      $closeBtn.on("click", function (e) {
        e.preventDefault();
        $modal.hide();
      });
    
      // Optional: close modal if user clicks outside the modal box
      $(window).on("click", function (e) {
        if ($(e.target).is($modal)) {
          $modal.hide();
        }
      });
    },

    bindSavedAddressSelectionEvents: function() {
      $('[id^="saved_address_"]').each(function () {
        $(this).on("click", function () {
          var idx = $(this).attr('id').replace('saved_address_', '');
    
          $('#order_billing_first_name').val(gon.addresses[idx].first_name);
          $('#order_billing_last_name').val(gon.addresses[idx].last_name);
          $('#order_billing_phone').val(gon.addresses[idx].phone);
          $('#order_billing_pincode').val(gon.addresses[idx].pincode);
          $('#order_billing_street_line_1').val(gon.addresses[idx].street_address_line_1);
          $('#order_billing_street_line_2').val(gon.addresses[idx].street_address_line_2);
          $('#order_billing_city').val(gon.addresses[idx].city);
          $('#order_billing_state').val(gon.addresses[idx].state);
          $('#order_billing_country').val(gon.addresses[idx].country);
    
          $('#change_address').hide();
        });
      });
    },
  
    updateForm: function() {
      if ($('#ship_to_same_address').is(':checked')) {
        $('.cod_error').show();
        $('.shipping-show').hide();
        $('.designer_unshippable').css('border', 'solid 2px #03A9F4');
      } else {
        $('.cod_error').hide();
        $('.shipping-show').show();
        $('.designer_unshippable').css('border', '');
      }

      if (!$('#phone-code1').length > 0) {
        $('#order_billing_phone').attr('style', 'width: 60% !important');
      }
      if ($("#order_billing_country").val().length > 0) {
        this.appendPhoneCode("order_billing_country", 'phone-code1');
      }
      if ($("#order_country").val().length > 0) {
        this.appendPhoneCode("order_country", 'phone-code2');
      }
      
      this.CityStateAutoFill('#order_billing_pincode', '#order_billing_country', '#order_billing_city', '#order_billing_state');
      this.CityStateAutoFill('#order_pincode', '#order_country', '#order_city', '#order_buyer_state');
      
      this.displayCustomsChargeMessage($('#order_billing_country').val());
    },

    bindEvents: function() {
      $('#ship_to_same_address').on('click', function() {
        Mirraw.shipping.updateForm();
      });

      if ($('#order_billing_country, #order_country').length > 0) {
        var $countryList = $('#order_billing_country, #order_country');
        
        $countryList.on('change', function(e) {
          if ($(this).val() !== '') {
            $.ajax(Mirraw.shipping.paramsGetStates($(this).val(), $(this)[0].id));
          }
        });

        if ($countryList.val() !== '') {
          $.ajax(Mirraw.shipping.paramsGetStates($countryList.val(), $countryList[0].id, $('#order_billing_state').val(), false));
        }
        this.shippingCountry();
      }

      $('#order_billing_country').on('change', function(e) {
        Mirraw.shipping.rakhiPreOrder();
        Mirraw.shipping.appendPhoneCode('order_billing_country', 'phone-code1');

        if ($('#ship_to_same_address').prop('checked')) {
          Mirraw.shipping.displayCustomsChargeMessage($(this).val());
        }
      });

      $('#order_country').on('change', function(e) {
        Mirraw.shipping.rakhiPreOrder();
        Mirraw.shipping.appendPhoneCode('order_country', 'phone-code2');
      });

      $(document).on('keyup paste', '#order_billing_pincode', function(e) {
        if (e.target.id === 'order_billing_pincode') {
          Mirraw.shipping.CityStateAutoFill('#order_billing_pincode', '#order_billing_country', '#order_billing_city', '#order_billing_state');
        }
      });

      $(document).on('keyup paste', '#order_pincode', function(e) {
        if (e.target.id === 'order_pincode') {
          Mirraw.shipping.CityStateAutoFill('#order_pincode', '#order_country', '#order_city', '#order_buyer_state');
        }
      });
    },

    init: function() {
      this.bindEvents();
      this.updateForm();
      this.domValidation();
      this.modalEvents();
      this.bindSavedAddressSelectionEvents();
    }
  };

  $(document).ready(function() {
    Mirraw.shipping.init();
  });

  return Mirraw;
})(this, this.document, MR);