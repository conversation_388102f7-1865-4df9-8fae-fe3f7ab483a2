MR.unitToggle = {
  initialize: function () {
    this.bindToggleEvent();
  },

  bindToggleEvent: function () {
    $(document).on('change', '.unitToggle', function () {
      var isCM = $(this).is(':checked');

      var $modal = $(this).closest('.reveal-modal');

      // Change cell values
      $modal.find('td[data-inch]').each(function () {
        var $cell = $(this);
        $cell.text(isCM && $cell.data('cm') ? $cell.data('cm') : $cell.data('inch'));
      });

      // Highlight selected unit
      var $wrapper = $(this).closest('.switch-wrapper');
      $wrapper.find('.unit-label-in').toggleClass('active', !isCM);
      $wrapper.find('.unit-label-cm').toggleClass('active', isCM);
    });
  }
};

$(function () {
  MR.unitToggle.initialize();
});
