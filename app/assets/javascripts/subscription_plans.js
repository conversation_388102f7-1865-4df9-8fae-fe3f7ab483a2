var MR = MR || {};
var MR = (function(window, document, Mirraw) {

  Mirraw.tabs = {
    handleTabClick: function(tabId, contentId, hideId, extraAction) {
      $(tabId).on('click', function() {
        if (hideId) $(hideId).hide();
        $(this).addClass('active').siblings().removeClass('active');
        if (extraAction) extraAction();
        $(contentId).show();
      });
    },

    init: function() {
      // Basic
      MR.tabs.handleTabClick('#basic', '#basic-content', '#basic_plus-content');
      MR.tabs.handleTabClick('#basic_plus', '#basic_plus-content', '#basic-content');

      // Express
      MR.tabs.handleTabClick('#express', '#express-content', '#express_plus-content');
      MR.tabs.handleTabClick('#express_plus', '#express_plus-content', '#express-content');

      // Max
      MR.tabs.handleTabClick('#max', '#max-content', '#max_plus-content', function() {
        $('#popular').removeClass('d-none');
      });

      MR.tabs.handleTabClick('#max_plus', '#max_plus-content', '#max-content', function() {
        $('#popular').addClass('d-none');
      });
    }
  };

  return Mirraw;

})(this, this.document, MR);

// Initialize after DOM is ready
$(function() {
  MR.tabs.init();
});
