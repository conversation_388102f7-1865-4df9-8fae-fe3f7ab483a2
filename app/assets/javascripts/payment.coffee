//= require payments/card
//= require payments/other
//= require payments/upi
//= require payments/saved_card

class @Payment
  bindings = ->
    @$container.on "toggled", =>
      $('.payment-error, .dom-payment-error').hide()
      @optionList.forEach (option) ->
        option.clear()

  checkPaymentSelection = ->
    @optionList.forEach (option) ->
    return false

  constructor: (optionList)->
    bindings = bindings.bind(this)
    @checkPaymentSelection = checkPaymentSelection.bind(this)
    @$container = $('.accordion')
    @optionList = optionList

    bindings()



$ ->
  
  $ ->

    # new code added as the offer message color cannot be changed so removing the dark color being applied by adding a new color
    $(".accordion-navigation").on "click", (event) -> 
      $(".payment-text-block").css('background', '#f4f4f4')

    if $(".accordion-navigation.active").length == 0 || $('[name=payment_option]').filter(":checked").val() == undefined
      selected_option = $(".accordion-navigation")
      symbol = selected_option.data('symbol')
      grandtotal = selected_option.data('grandtotal')
      computeGrandTotal(symbol, grandtotal, 0, 0, 0)
      $('#shipping_charge').html('<span>Shipping</span> <span>' + symbol + ' ' + selected_option.data('shipping') + '</span>')
      $('#prepaid_discount').val(0)
      $('.prepaid_discount').hide()

  cardPayment = new CardPayment('.card-number', '.expiry-date', '.cvv', '.card-icon', $(".accordion-navigation.card"))
  otherPayment = new OtherPayment('.accordion-navigation')
  payment = new Payment([cardPayment, otherPayment])
  new UPIPayment('.payment-upi')
  new SavedCard('.payment-saved-card')

  $(".accordion-navigation.card.active").on "click", (event) ->
    togglePrepaidPromotion()
  
  accordionNavigation = $('.accordion-navigation')
  accordionNavigation.find(".inline-options").on "change", (event) ->
    accordionNavigation.removeClass("active")
    accordionNavigation.find(".content").removeClass("active")
    $('.cvv-input').attr("type", "hidden")
    $('.payment-upi-id input').attr("type", "hidden")
    togglePrepaidPromotion()

  shakeEffect = (error_msg) ->
    $active_accordion =  $(".accordion-navigation.active")
    $active_accordion.find('.payment-error').slideDown('slow').text error_msg
    $active_accordion.find('a').addClass 'shake-effect'
    setTimeout (->
      $active_accordion.find('a').removeClass 'shake-effect'
    ), 750

  togglePrepaidPromotion = ->
    if WALLET.methods.isValidWallet()
      WALLET.methods.unCheckWallet()
    selected_option = $('[name=payment_option]').filter(":checked")
    symbol = selected_option.data('symbol')
    grandtotal = selected_option.data('grandtotal')
    prepaid_discount = selected_option.data('prepaidDiscount')
    shipping_discount = selected_option.data('shipping')
    if !$('#cashondelivery').is(":checked")
      $('#wallet_discount_order_page').show()
      $('.dom-payment-error').hide()
      $('.payment-error').hide()
      if selected_option.data('prepaidShippingPromo') == 'available'
        $('#shipping_charge').html('<span>Shipping</span> <span>' + ('FREE').fontcolor('green').bold() + '</span>')
        computeGrandTotal(symbol, grandtotal, prepaid_discount, 0, shipping_discount)
      else
        $('#shipping_charge').html('<span>Shipping</span> <span>' +  symbol + ' ' + shipping_discount + '</span>')
        computeGrandTotal(symbol, grandtotal, prepaid_discount, 0, 0)
      $('.prepaid_discount').show()
      $('#prepaid_discount').val(gon.prepaid_discount)
      $('.grand_total').show()
      $('.grand_total_with_cod.hide').hide()
      $('.cod_charges').hide()
    else
      WALLET.methods.unCheckWallet()
      $('.dom-payment-error').hide()
      $('.payment-error').hide()
      $('#wallet_discount_order_page').hide()
      $('#shipping_charge').html('<span>Shipping</span> <span>' +  symbol + ' ' + shipping_discount + '</span>')
      $('.prepaid_discount').hide()
      $('#prepaid_discount').val(0)
      $('.grand_total').hide()
      $('.grand_total_with_cod.hide').show()
      $('.cod_charges').show()
    $('#mastercard_discount').val('0')
    $('.mastercard_discount').hide()
    $('.saved_card_message').hide()

  $('.saved_cards_input').on 'click', ->
    selected_option = $(".accordion-navigation.active")
    mastercard_discount = selected_option.data('mastercardDiscount')
    if mastercard_discount == 0
      return
    else
      symbol = selected_option.data('symbol')
      grandtotal = selected_option.data('grandtotal')
      prepaid_discount = selected_option.data('prepaidDiscount')
      shipping_discount = selected_option.data('shipping')
      if $(this).data().cardType.toLowerCase() == 'mastercard'
        $('.mastercard_discount').show()
        $('#mastercard_discount').val(gon.mastercard_discount)
        if selected_option.data('prepaidShippingPromo') == 'available'
          $('#shipping_charge').html('<span>Shipping</span> <span>' + ('FREE').fontcolor('green').bold() + '</span>')
          computeGrandTotal(symbol, grandtotal, 0, mastercard_discount, shipping_discount)
        else
          $('#shipping_charge').html('<span>Shipping</span> <span>' + symbol + ' ' + selected_option.data('shipping') + '</span>')
          computeGrandTotal(symbol, grandtotal, 0, mastercard_discount, 0)
        $('.prepaid_discount').hide()
        $('#prepaid_discount').val(0)
        message_attr = '.card_message_'+ $(this).data().key
        $(message_attr).show()
        $(message_attr).text 'Mastercard Discount is applied!'
      else
        $('#mastercard_discount').val('0')
        $('.mastercard_discount').hide()
        if $('.prepaid_discount').length > 0
          $('.prepaid_discount').show()
          $('#prepaid_discount').val(gon.prepaid_discount)
        if selected_option.data('prepaidShippingPromo') == 'available'
          $('#shipping_charge').html('<span>Shipping</span> <span>' + ('FREE').fontcolor('green').bold() + '</span>')
          computeGrandTotal(symbol, grandtotal, prepaid_discount, 0, shipping_discount)
        else
          $('#shipping_charge').html('<span>Shipping</span> <span>' + symbol + ' ' + selected_option.data('shipping') + '</span>')
          computeGrandTotal(symbol, grandtotal, prepaid_discount, 0, 0)
        $('.saved_card_message').hide()

  computeGrandTotal = (symbol, grandtotal, prepaid_discount, mastercard_discount, shipping_discount) ->
    total = grandtotal - prepaid_discount - mastercard_discount - shipping_discount
    total_with_symbol = symbol + ' ' + total
    $('.grand_total').html('<span>Grand Total</span> <span>' + total_with_symbol + '</span>')
    $('.grand_total.top').html(total_with_symbol)

  $("#new_order").on "submit", (event) ->
    $active_accordion =  $(".accordion-navigation.active")
    unless otherPayment.isValid() || cardPayment.isSelected() || WALLET.methods.isValidWallet()
      if $active_accordion.length == 0
        $('html, body').animate({
          scrollTop: 0
        }, 500, -> $('.dom-payment-error').slideDown().text('Please Select an option').addClass('shake-effect'))
        $('.dom-payment-error').removeClass 'shake-effect'
        return false
      $('html, body').animate({
        scrollTop: $active_accordion.offset().top - 120
      }, 500, -> shakeEffect('Please Select an option'))
      return false
    if $(".accordion-navigation.upi.active").length == 1 && $('[name=upi_id]').val() == ''
      $('html, body').animate({
        scrollTop: $active_accordion.offset().top - 120
      }, 500, -> shakeEffect('Please Enter Valid UPI ID / Mobile Number'))
      return false
    if $active_accordion.length == 0
      set_pay_type = $('[name=payment_option]').filter(":checked").data('pay-type')
      $("#pay_type").val(set_pay_type)
    else
      $("#pay_type").val($active_accordion.data("active"))
      if $active_accordion.data("active") == 'SAVED_CARD'
        $('#card-token').val($('[name=payment_option]').filter(":checked").data("token"))
    unless $('[name=payment_option]').filter(":checked").val() == undefined
      $('[name=payment_option]').val($('[name=payment_option]').filter(":checked").val())
    if cardPayment.isSelected() && $('#card-token').val().length == 0 && $('#card-error').val().length == 0
      $active_accordion.find('.payment-error').hide()
      event.preventDefault()
      cardPayment.makePayment()
    else
      $('.add_checkout').attr('disabled', false).val('PLACE ORDER')
      error_msg = if $('#card-error').val().length > 0 then $('#card-error').val() else 'Please enter valid card details'
      if cardPayment.isSelected() && $('#card-token').val().length == 0
        $('html, body').animate({
          scrollTop: $active_accordion.offset().top - 120
        }, 500, -> shakeEffect(error_msg))
        $('#card-error').val('')
        return false
