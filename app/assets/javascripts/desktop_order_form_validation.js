var PLACE_ORDER_FORM_VALIDATION = PLACE_ORDER_FORM_VALIDATION || {};

PLACE_ORDER_FORM_VALIDATION = (function(window, document, ValidateForm){
  const inputBillingAddress = ['#order_billing_email','#order_billing_first_name','#order_billing_last_name','#order_billing_phone','#order_billing_country','#order_billing_pincode','#order_billing_street_line_1','#order_billing_street_line_2','#order_billing_street','#order_billing_city','#order_billing_state'];
  const inputShippingAddress = ['#order_last_name', '#order_first_name','#order_country','#order_pincode','#order_street_line_1','#order_street_line_2','#order_street','#order_city','#order_buyer_state','#order_phone'];

  ValidateForm.methods = {

    isValid: function(){
      return PLACE_ORDER_FORM_VALIDATION.methods.validateDetails()
    },

    toggleValidationMessage: function(){
      var missingArray = $('#ship_to_same_address').is(":checked") ?  PLACE_ORDER_FORM_VALIDATION.methods.missingElements(inputBillingAddress) : PLACE_ORDER_FORM_VALIDATION.methods.missingElements(inputBillingAddress.concat(inputShippingAddress))
      
      togglePhoneValidation()
      var notice_html = $('#error_explanation')
      if (missingArray.length != 0) {
        for (var i in missingArray) {
          $(missingArray[i]).css('border-color', 'red');
        }
        ValidateForm.methods.toggleErrorMessage(missingArray[0])
         $('#error_explanation').children().html('Blank Fields : ' + missingArray)
          notice_html.removeClass('hide');
        $('#validation-message').css('display', 'block');
      }
      else {
        notice_html.addClass('hide')
        return true
      } 

      function togglePhoneValidation() {
        if(!$('#ship_to_same_address').is(":checked") ){
          return PLACE_ORDER_FORM_VALIDATION.methods.validatePhone('_billing') && PLACE_ORDER_FORM_VALIDATION.methods.validatePhone('')
        }
        else {
          return PLACE_ORDER_FORM_VALIDATION.methods.validatePhone('_billing')
        }
      }

    },

    validatePhone: function(type){
      var element = $("#order" + type + "_phone")
      var phone = element.val()
      var country_code = $("#order" + type + "_country").val()

      var phoneMinimunCount = getPhoneMinimunCount(country_code)
      var getPhoneMaxiumCount = getPhoneMaxiumCount(country_code)      
      var phoneRegex = new RegExp("^[0-9]{" + phoneMinimunCount + "," + getPhoneMaxiumCount + "}$")
      if (phone == undefined || phone.trim() == '' || !phoneRegex.test(phone)) {
        PLACE_ORDER_FORM_VALIDATION.methods.toggleErrorMessage(element)
        return false
      }
      else {
        return true
      }

      function getPhoneMinimunCount (country_code) {
        switch (country_code) {
          case 'United States':
            return 10
          case 'India':
            return 10
          case 'UAE':
            return 9
          default:
            return 8
        }
      }
      function getPhoneMaxiumCount (country_code) {
        switch (country_code) {
          case 'United States':
            return 10
          case 'India':
            return 10
          case 'UAE':
            return 9
          default:
            if (country_code != 'India') {
              return 20
            }
            else{
              if (gon.max_phone_length != undefined) {
                return gon.max_phone_length
              }
              else {
                return 10
              }
            }
        }
      }
    },
    toggleErrorMessage: function(element){
      $(element).css('border-color', 'red');
      $('html, body').animate({
        scrollTop: $(element).offset().top-200
    }, 1000, function(){$(element).focus();});
    },

    validateEmail: function(){
      var email = $('#order_billing_email').val()
      var emailRegex = new RegExp("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
      if (email == undefined || email.trim() == '' || !emailRegex.test(email)) {
        PLACE_ORDER_FORM_VALIDATION.methods.toggleErrorMessage('#order_billing_email');
        return false
      }
      else {
        return true
      }
    },

    validateDetails: function(){
      var missingArray = PLACE_ORDER_FORM_VALIDATION.methods.missingElements(inputBillingAddress)

      if (missingArray.length == 0) {
        if (!$('#ship_to_same_address').is(":checked")) {
          var missingArray = PLACE_ORDER_FORM_VALIDATION.methods.missingElements(inputShippingAddress)
          return missingArray.length == 0 && PLACE_ORDER_FORM_VALIDATION.methods.validateEmail() && PLACE_ORDER_FORM_VALIDATION.methods.validatePhone('_billing') && PLACE_ORDER_FORM_VALIDATION.methods.validatePhone('')
        }
        else{
          return true && PLACE_ORDER_FORM_VALIDATION.methods.validateEmail() && PLACE_ORDER_FORM_VALIDATION.methods.validatePhone('_billing')
        }
      }
      else {
        return false
      }
    },

    missingElements: function(elementIds){
      missingElements = []
      for (var i in elementIds) {
        var element = elementIds[i];
        $(element).css('border-color', '');
        if ($(element).length > 0 && ($(element).val() == undefined || $(element).val().trim() == '')) {
          missingElements.push(element);
        }
      }
      return missingElements
    },

    removeValidationCss: function() {
      var inputFields = inputBillingAddress.concat(inputShippingAddress);
      for (var i in inputFields) {
        var element = inputFields[i];
        $(element).css('border-color', '');
      }
      $('#error_explanation').addClass('hide');
    },
  }
  return ValidateForm;
})(this, this.document, PLACE_ORDER_FORM_VALIDATION);
