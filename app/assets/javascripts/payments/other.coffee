class @OtherPayment
  bindings = ->
    @$initialListInputs.on "change", clearRemainingListInputs
    @$remainingListInputs.on "change", (event) =>
      $targetInput = $(event.target)
      return if $targetInput.val() == "Default"
      clearInitialListInputs()

  clearInitialListInputs = ->
    togglePrepaidPromotion()
    @$initialListInputs.filter(":checked").attr('checked', false) && @$remainingListInputs.find(":selected").attr('selected', true)
    $('.cvv-input').attr("type", "hidden")
    $('.payment-upi-id input').attr("type", "hidden")

  clearRemainingListInputs = ->
    @$remainingListInputs.find(":selected").attr('selected', false)
    @$remainingListInputs.each ->
      $el = $(this)
      $el.val($el.data("default"))

  togglePrepaidPromotion = ->
    if WALLET.methods.isValidWallet()
      WALLET.methods.unCheckWallet()
    selected_option = $(".accordion-navigation.active")
    symbol = selected_option.data('symbol')
    grandtotal = selected_option.data('grandtotal')
    prepaid_discount = selected_option.data('prepaidDiscount')
    shipping_discount = selected_option.data('shipping')
    $('#wallet_discount_order_page').show()
    if $(".accordion-navigation.active").length == 1
      if selected_option.data('prepaidShippingPromo') == 'available'
        $('#shipping_charge').html('<span>Shipping</span> <span>' + ('FREE').fontcolor('green').bold() + '</span>')
        computeGrandTotal(symbol, grandtotal, prepaid_discount, 0, shipping_discount)
      else
        $('#shipping_charge').html('<span>Shipping</span> <span>' + symbol + ' ' + selected_option.data('shipping') + '</span>')
        computeGrandTotal(symbol, grandtotal, prepaid_discount, 0, 0)
      $('.prepaid_discount').show()
      if grandtotal < 0
        selected_option.data('grandtotal')
      $('#prepaid_discount').val(gon.prepaid_discount)
      $('.grand_total').show()
      $('.grand_total_with_cod.hide').hide()
      $('.cod_charges').hide()
    else
      selected_option = $(".accordion-navigation")
      grandtotal = selected_option.data('grandtotal')
      if grandtotal < 0
        selected_option.data('grandtotal')
      symbol = selected_option.data('symbol')
      shipping_discount = selected_option.data('shipping')
      computeGrandTotal(symbol, grandtotal, 0, 0, 0)
      $('#shipping_charge').html('<span>Shipping</span> <span>' + ' ' + shipping_discount + '</span>')
      $('.prepaid_discount').hide()
      $('#prepaid_discount').val(0)
    $('#mastercard_discount').val("0")
    $('.mastercard_discount').hide()
    $('.card_message').hide()
    $('.saved_card_message').hide()

  computeGrandTotal = (symbol, grandtotal, prepaid_discount, mastercard_discount, shipping_discount) ->
    total = grandtotal - prepaid_discount - mastercard_discount - shipping_discount
    if (total < 0) 
      total = 0
    total_with_symbol = symbol + ' ' + total
    $('.grand_total').html('<span>Grand Total</span> <span>' + total_with_symbol + '</span>')
    $('.grand_total.top').html(total_with_symbol)
  
  isValid: ->
    @$initialListInputs.filter(":checked").length > 0 || (@$remainingListInputs.find(":selected").val() != "Default" && @$remainingListInputs.find(":selected").length != 0)

  constructor: (container) ->
    bindings = bindings.bind(this)
    clearInitialListInputs = clearInitialListInputs.bind(this)
    clearRemainingListInputs = clearRemainingListInputs.bind(this)

    @$container = $(container)
    @$initialListInputs = @$container.find('[type=radio]')
    @$remainingListInputs = $('.payment-other-remaining-list select')

    bindings()

  clear: ->
    clearInitialListInputs()
    clearRemainingListInputs()
