var MR = MR || {};
MR = (function(window, document, Mirraw) {
  Mirraw.newArrivals = {
    initSwiper: function(container = document) {
      const swiperEl = $(container).find('.swiper')[0];
      if (swiperEl && !swiperEl.classList.contains('swiper-initialized')) {
        new Swiper(swiperEl, {
          slidesPerView: 'auto',
          spaceBetween: 10,
          loop: true,
          autoplay: { delay: 5000, disableOnInteraction: false },
          navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
          },
        });
        swiperEl.classList.add('swiper-initialized');
      }
    },

    initLazyLoadPlaylists: function() {
      const observer = new IntersectionObserver(function(entries) {
        entries.forEach(function(entry) {
          if (entry.isIntersecting) {
            const $container = $(entry.target);
            const playlist_id = $container.data('id');

            $.ajax({
              url: `/pages/playlist/${playlist_id}`,
              type: 'GET',
              dataType: 'html',
              success: function(response) {
                $container.html(response);
                MR.newArrivals.initSwiper($container);
              },
              error: function() {

              }
            });

            observer.unobserve(entry.target);
          }
        });
      }, { threshold: 0.1 });

      $('.playlist-card').each(function() {
        observer.observe(this);
      });
    },

    init() {
      this.initLazyLoadPlaylists();
    }
  };

  return Mirraw;
})(this, document, MR);

$(document).ready(function () {
  MR.newArrivals.init();
});
