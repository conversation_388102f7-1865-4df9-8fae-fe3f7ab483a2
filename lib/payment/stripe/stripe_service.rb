module Payment
  module Stripe
    class StripeService
      attr_reader :order, :payment_details

      def initialize(order)
        @order = order
        @payment_details = Payment::PaymentDetails.new(order, order.country_code, order.currency_code)
      end

      def create_intent
        customer = find_or_create_customer
        ephemeral_key = create_ephemeral_key(customer.id)
        intent = find_or_create_payment_intent(customer.id)

        intent.to_hash.merge(ephemeral_secret: ephemeral_key.secret, customer_id: customer.id)
      rescue => e
        { error: e.message }
      end

      def get_stripe_item_details
        customer = find_or_create_customer
        payment_intent = find_or_create_payment_intent(customer.id)

        {
          payment_method_types: ['card'],
          line_items: [{
            price_data: {
              currency: order.currency_code.downcase,
              product_data: { name: 'Order Total' },
              unit_amount: calculate_amount,
            },
            quantity: 1,
          }],
          mode: 'payment',
          customer: customer.id,
          payment_intent_data: {
            shipping: shipping_address,
            receipt_email: order.email,
            setup_future_usage: 'off_session',
            metadata: metadata,
          },
          success_url: success_url,
          cancel_url: cancel_url
        }
      end

      private

      def find_or_create_customer
        existing_customer = order.user.payment_gateway_customers.stripe_customer.first
        return ::Stripe::Customer.retrieve(existing_customer.customer_id) if existing_customer

        customer = ::Stripe::Customer.create(
          name: order.name,
          email: order.email,
          address: shipping_address[:address]
        )
        PaymentGatewayCustomer.create!(
          payment_gateway_type: 'stripe',
          customer_id: customer.id,
          user_id: order.user.id
        )
        customer
      end

      def find_or_create_payment_intent(customer_id)
        transaction = order.payment_gateway_transaction

        if transaction&.gateway_type == 'stripe'
          ::Stripe::PaymentIntent.retrieve(transaction.transaction_id)
        else
          intent = ::Stripe::PaymentIntent.create(intent_parameters.merge(customer: customer_id))
          transaction ? transaction.update!(gateway_type: 'stripe', transaction_id: intent.id) :
                        order.create_payment_gateway_transaction(transaction_id: intent.id, customer_id: customer_id, gateway_type: 'stripe')
          intent
        end
      end

      def create_ephemeral_key(customer_id)
        ::Stripe::EphemeralKey.create(
          { customer: customer_id },
          stripe_version: ::Stripe.api_version
        )
      end

      def calculate_amount
        total = payment_details.get_total_amount_to_pay

        case order.currency_code.downcase
        when 'jpy'
          total.to_i
        when 'aed'
          ((order.total / order.currency_rate) * 100).to_i
        else
          (total * 100).to_i
        end
      end

      def metadata
        {
          order_number: order.number,
          order_currency: order.currency_code.downcase,
        }
      end

      def shipping_address
        {
          name: order.name,
          address: {
            line1: order.street,
            postal_code: order.pincode,
            city: order.city,
            state: order.buyer_state,
            country: order.country_code
          }
        }
      end

      def intent_parameters
        {
          description: "Mirraw Online - #{order.number}",
          shipping: shipping_address,
          receipt_email: order.email,
          amount: calculate_amount,
          currency: order.currency_code.downcase,
          metadata: metadata 
        }
      end

      def success_url
        root_url = "http://#{ENV['MIRRAW_MOBILE_DOMAIN']}"
        "#{root_url}/orders/stripe_checkout_success/#{order.id}?session_id={CHECKOUT_SESSION_ID}&number=#{order.number}"
      end

      def cancel_url
        "http://#{ENV['MIRRAW_MOBILE_DOMAIN']}/orders/new"
      end
    end
  end
end
